package com.bookmap.api.simple.demo.indicators;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import com.bookmap.api.simple.demo.indicators.OrderSenderControllerV2; // Keep this for the controller type
import velox.api.layer1.data.InstrumentInfo; // For controller.getInstrumentInfo()
import velox.api.layer1.simplified.Api; // For controller.getApi()
import velox.api.layer1.messages.Layer1ApiSoundAlertMessage; // For sound alerts

public final class DrawdownGuard {
    private final OrderSenderControllerV2 controller;

    private static final double FP_EPSILON = 1e-9; // Epsilon for floating-point comparisons

    private final double ddCash;          // realised+unrealised cash
    private final double ddPct;           // realised+unrealised %
    private final double ddUnrCash;       // unrealised cash
    private final double ddUnrPct;        // unrealised %
    private final double ddGivePct;       // profit give‑back %
    private final double profitEpsilon;   // Configurable profit epsilon value
    private final boolean totalDDProtectionActiveConfig; // New config flag
    private final boolean unrealizedDDProtectionActiveConfig; // New config flag

    // New fields for trigger reasons
    private volatile String totalDrawdownTriggerReason = "";
    private volatile String unrealizedDrawdownTriggerReason = "";

    /* peaks are updated from multiple MD threads – use Atomics */
    private final AtomicReference<Double> peakGain  = new AtomicReference<>(Double.NEGATIVE_INFINITY);
    private final AtomicReference<Double> peakLoss  = new AtomicReference<>(Double.POSITIVE_INFINITY);  // most negative U
    private final AtomicReference<Double> peakTotal = new AtomicReference<>(Double.NEGATIVE_INFINITY);

    /* notional changes only on trade events -> volatile is enough */
    /** Captured the instant we flip from flat→non‑flat.
     *  UPDATED on every size‑changing fill so %‑based maths stay correct. */
    private volatile double entryNotional = 0.0;

    /* session‑persisted flags ------------------------------------------------ */
    private final AtomicBoolean totalDrawdownHit              = new AtomicBoolean(false);
    private final AtomicBoolean unrealizedDrawdownActionTaken = new AtomicBoolean(false);
    private final AtomicInteger unrealizedDrawdownFlattenCount = new AtomicInteger(0);   // kept across trades
    private final AtomicInteger unrealizedDrawdownDetectionCount = new AtomicInteger(0); // New counter for detections

    /* log throttling */
    private static final long LOG_THROTTLE_MS = 1_000;
    private volatile long lastLog = 0L;

    // Snapshot values calculated by the update method
    private volatile double currentTotalDrawdownValueSnapshot = 0.0;
    private volatile double currentUnrealizedDrawdownValueSnapshot = 0.0;
    private volatile double currentTotalDrawdownPercentSnapshot = 0.0;
    private volatile double currentUnrealizedDrawdownPercentSnapshot = 0.0;

    public DrawdownGuard(OrderSenderControllerV2 controller,
                         double ddCash, double ddPct,
                         double ddUnrCash, double ddUnrPct,
                         double ddGivePct, double profitEpsilonTicks,
                         boolean totalDDProtectionActive, boolean unrealizedDDProtectionActive) {
        this.controller = controller;
        this.ddCash = ddCash;
        this.ddPct = ddPct;
        this.ddUnrCash = ddUnrCash;
        this.ddUnrPct = ddUnrPct;
        this.ddGivePct = ddGivePct;
        InstrumentInfo instrumentInfo = this.controller.getInstrumentInfo();
        this.profitEpsilon = profitEpsilonTicks * (instrumentInfo != null && instrumentInfo.pips > 0 ? instrumentInfo.pips : 1.0);
        this.totalDDProtectionActiveConfig = totalDDProtectionActive;
        this.unrealizedDDProtectionActiveConfig = unrealizedDDProtectionActive;

        if (OrderSenderControllerV2.debug_mode_ordersendercontroller) {
            OrderSenderControllerV2.sendtolog(String.format(
                "[DrawdownGuard:%s] Initialized with: ddCash=%.2f, ddPct=%.2f%%, ddUnrCash=%.2f, ddUnrPct=%.2f%%, ddGivePct=%.2f%%, profitEpsilon=%.5f (from %.2f ticks), totalActive=%b, unrActive=%b, pips=%.5f",
                this.controller.getAlias(),
                ddCash, ddPct * 100, ddUnrCash, ddUnrPct * 100, ddGivePct * 100, this.profitEpsilon, profitEpsilonTicks,
                totalDDProtectionActive, unrealizedDDProtectionActive, (instrumentInfo != null && instrumentInfo.pips > 0 ? instrumentInfo.pips : 1.0)
            ));
        }
        reset(); // Initialize peaks and other state
    }

    /**
     * Two‑stage draw‑down protection.
     * <br>Stage A – "hard stop" caps the *first* adverse excursion (even if the trade never goes green).
     * <br>Stage B – classic profit give‑back once the position has been in the money.
     * <br>Total‑equity guard (realised + unrealised) remains unchanged.
     */
    void update(double realised, double unrealised, double pointValue) {
        if (pointValue <= 0 || Double.isNaN(pointValue)) {
            if (OrderSenderControllerV2.debug_mode_ordersendercontroller && (System.currentTimeMillis() - lastLog > LOG_THROTTLE_MS)) {
                OrderSenderControllerV2.sendtolog(String.format("[DrawdownGuard:%s] Invalid pointValue (%.2f) in update. Skipping.", this.controller.getAlias(), pointValue));
                lastLog = System.currentTimeMillis();
            }
            return;
        }

        double totalPnL = realised + unrealised;

        // (A) Update peaks
        peakGain.getAndAccumulate(unrealised, Math::max); // Max unrealised gain for this trade
        peakLoss.getAndAccumulate(unrealised, Math::min); // Max unrealised loss (most negative) for this trade
        peakTotal.getAndAccumulate(totalPnL, Math::max);  // Max total P&L (session level)

        // Update snapshot values before checks
        currentUnrealizedDrawdownValueSnapshot = unrealised; // This is the current unrealized PnL itself
        currentTotalDrawdownValueSnapshot = totalPnL;       // This is the current total PnL

        // Calculate percentage drawdowns if entryNotional is valid
        if (entryNotional > FP_EPSILON) {
            currentUnrealizedDrawdownPercentSnapshot = (unrealised / entryNotional); // Can be positive or negative
            currentTotalDrawdownPercentSnapshot = (totalPnL / entryNotional);         // Can be positive or negative
        } else {
            currentUnrealizedDrawdownPercentSnapshot = 0.0;
            currentTotalDrawdownPercentSnapshot = 0.0;
        }


        // (B) Total P&L Drawdown Check (Realized + Unrealized) - Session Level
        if (totalDDProtectionActiveConfig && !totalDrawdownHit.get()) {
            boolean cashTrigger = (ddCash > 0) && (totalPnL < -ddCash);
            boolean pctTrigger = (ddPct > 0 && entryNotional > FP_EPSILON) && (totalPnL < 0 && (Math.abs(totalPnL) / entryNotional) > ddPct);

            if (cashTrigger || pctTrigger) {
                if (totalDrawdownHit.compareAndSet(false, true)) {
                    totalDrawdownTriggerReason = String.format("Total PnL: %.2f hit %s limit (Cash: %.2f/%.2f, Pct: %.2f%%/%.2f%% of %.2f Notional)",
                                                              totalPnL, (cashTrigger && pctTrigger ? "Cash & Pct" : (cashTrigger ? "Cash" : "Pct")),
                                                              Math.abs(totalPnL), ddCash,
                                                              (entryNotional > FP_EPSILON ? (Math.abs(totalPnL) / entryNotional) * 100 : 0), ddPct * 100, entryNotional);
                    OrderSenderControllerV2.sendtolog(String.format("[DrawdownGuard:%s] CRITICAL: %s. Flattening and disabling strategy.", this.controller.getAlias(), totalDrawdownTriggerReason));
                    Api api = this.controller.getApi();
                    if (api != null) {
                        api.sendUserMessage(new Layer1ApiSoundAlertMessage("DrawdownGuard: Total PnL Stop Hit! Strategy Disabled."));
                    }
                    this.controller.flattenAndDisableStrategy(totalDrawdownTriggerReason, true); 
                    OrderSenderControllerV2.requestStrategyForFlatteningRealisedDisable = true;
                }
            }
        }

        // (C) Unrealised P&L Drawdown Check - Trade Level (only if not already flat/disabled by total DD)
        if (unrealizedDDProtectionActiveConfig && !totalDrawdownHit.get() && entryNotional > FP_EPSILON) { // Only active if there's a position
            boolean hardStopCashTrigger = (ddUnrCash > 0) && (unrealised < -ddUnrCash);
            boolean hardStopPctTrigger  = (ddUnrPct > 0) && (unrealised < 0 && (Math.abs(unrealised) / entryNotional) > ddUnrPct);
            
            double currentPeakGain = peakGain.get();
            boolean giveBackTrigger = (ddGivePct > 0 && currentPeakGain > profitEpsilon) && (unrealised < (currentPeakGain * (1.0 - ddGivePct)));

            if (hardStopCashTrigger || hardStopPctTrigger || giveBackTrigger) {
                if (!unrealizedDrawdownActionTaken.get()) { // Check if action for this specific drawdown type was already taken for the current trade/segment
                    unrealizedDrawdownDetectionCount.incrementAndGet();
                    String triggerType = "";
                    if (hardStopCashTrigger) triggerType += "Unr.Cash ";
                    if (hardStopPctTrigger) triggerType += "Unr.Pct ";
                    if (giveBackTrigger) triggerType += "GiveBack ";
                    
                    unrealizedDrawdownTriggerReason = String.format("Unrealised PnL: %.2f hit %s limit (UnrCash: %.2f/%.2f, UnrPct: %.2f%%/%.2f%% of %.2f Notional, GiveBack: %.2f < %.2f * (1-%.2f%%))",
                                                                  unrealised, triggerType.trim(),
                                                                  Math.abs(unrealised), ddUnrCash,
                                                                  (Math.abs(unrealised) / entryNotional) * 100, ddUnrPct * 100, entryNotional,
                                                                  unrealised, currentPeakGain, ddGivePct * 100);

                    OrderSenderControllerV2.sendtolog(String.format("[DrawdownGuard:%s] WARNING: %s. Flattening current position.", this.controller.getAlias(), unrealizedDrawdownTriggerReason));
                    // Set actionTaken to true BEFORE flattening to avoid re-entry if flatten is async or partial
                    unrealizedDrawdownActionTaken.set(true); 
                    unrealizedDrawdownFlattenCount.incrementAndGet();
                    this.controller.flattenCurrentPosition(unrealizedDrawdownTriggerReason, true); 
                    OrderSenderControllerV2.requestStrategyForFlatteningUnrealised = true;
                }
            } else {
                // If PnL recovers or no trigger, allow new unrealized drawdown actions for subsequent significant PnL movements.
                // This reset is crucial if a position is scaled out/in or reverses, effectively starting a new "segment"
                // for unrealized drawdown purposes, while total drawdown remains session-based.
                // We reset it here if NO trigger is active.
                unrealizedDrawdownActionTaken.set(false);
                unrealizedDrawdownTriggerReason = ""; // Clear reason when not triggered
            }
        }


        // (D) Logging (throttled)
        long now = System.currentTimeMillis();
        if (OrderSenderControllerV2.debug_mode_ordersendercontroller && (now - lastLog > LOG_THROTTLE_MS)) {
            OrderSenderControllerV2.sendtolog(String.format(
                "[DrawdownGuard:%s] Update: R=%.2f, U=%.2f, Total=%.2f | Peaks: Gain=%.2f, Loss=%.2f, Total=%.2f | Notional=%.2f | TotalHit=%b, UnrAction=%b, UnrFlattens=%d, UnrDetects=%d",
                this.controller.getAlias(), realised, unrealised, totalPnL,
                peakGain.get(), peakLoss.get(), peakTotal.get(),
                entryNotional, totalDrawdownHit.get(), unrealizedDrawdownActionTaken.get(), unrealizedDrawdownFlattenCount.get(), unrealizedDrawdownDetectionCount.get()
            ));
            lastLog = now;
        }
    }

    public void updateEntryNotional(double newNotional) {
        if (OrderSenderControllerV2.debug_mode_ordersendercontroller) {
            OrderSenderControllerV2.sendtolog(String.format("[DrawdownGuard:%s] Updating entryNotional from %.2f to %.2f",
                this.controller.getAlias(), this.entryNotional, newNotional));
        }
        this.entryNotional = newNotional;
        // When entry notional changes (new trade, scaling in), reset unrealized P&L specific flags and peaks
        // Total P&L flags (totalDrawdownHit, peakTotal) are session-level and not reset here.
        peakGain.set(Double.NEGATIVE_INFINITY);
        peakLoss.set(Double.POSITIVE_INFINITY);
        unrealizedDrawdownActionTaken.set(false); 
        unrealizedDrawdownTriggerReason = ""; // Clear reason on new/modified position
        // Detection count is not reset here as it's a session counter of how many times any unrealized condition was met.
        // Flatten count is also session level.
    }

    public void reset() { // Resets parts of the state, typically at the start or on manual command
        peakGain.set(Double.NEGATIVE_INFINITY);
        peakLoss.set(Double.POSITIVE_INFINITY);
        peakTotal.set(Double.NEGATIVE_INFINITY); // Reset session peak P&L
        
        // entryNotional is NOT reset here, it's set by trade events via updateEntryNotional.
        
        totalDrawdownHit.set(false);
        unrealizedDrawdownActionTaken.set(false);
        
        // Counts are typically session-level, so not reset by a generic 'reset()' unless specified.
        // If a full session reset is needed, these might be reset too:
        // unrealizedDrawdownFlattenCount.set(0);
        // unrealizedDrawdownDetectionCount.set(0);

        totalDrawdownTriggerReason = "";
        unrealizedDrawdownTriggerReason = "";
        currentTotalDrawdownValueSnapshot = 0.0;
        currentUnrealizedDrawdownValueSnapshot = 0.0;
        currentTotalDrawdownPercentSnapshot = 0.0;
        currentUnrealizedDrawdownPercentSnapshot = 0.0;


        if (OrderSenderControllerV2.debug_mode_ordersendercontroller) {
            OrderSenderControllerV2.sendtolog(String.format("[DrawdownGuard:%s] State reset. Flatten count: %d, Detection count: %d. TotalDDHit: %b",
                this.controller.getAlias(), unrealizedDrawdownFlattenCount.get(), unrealizedDrawdownDetectionCount.get(), totalDrawdownHit.get()));
        }
    }

    // Getters for state inspection
    public boolean isTotalDrawdownHit() { return totalDrawdownHit.get(); }
    public boolean isUnrealizedDrawdownActionTaken() { return unrealizedDrawdownActionTaken.get(); } // If true, means a flatten was issued for current unrealized DD
    public int getUnrealizedDrawdownFlattenCount() { return unrealizedDrawdownFlattenCount.get(); }
    public int getUnrealizedDrawdownDetectionCount() { return unrealizedDrawdownDetectionCount.get(); }
    public String getTotalDrawdownTriggerReason() { return totalDrawdownTriggerReason; }
    public String getUnrealizedDrawdownTriggerReason() { return unrealizedDrawdownTriggerReason; }

    // Getters for snapshot P&L values from the last update cycle
    public double getCurrentTotalDrawdownValue() { return currentTotalDrawdownValueSnapshot; }
    public double getCurrentUnrealizedDrawdownValue() { return currentUnrealizedDrawdownValueSnapshot; }
    public double getCurrentTotalDrawdownPercent() { return currentTotalDrawdownPercentSnapshot; }
    public double getCurrentUnrealizedDrawdownPercent() { return currentUnrealizedDrawdownPercentSnapshot; }
    public double getEntryNotional() { return entryNotional; }

}
