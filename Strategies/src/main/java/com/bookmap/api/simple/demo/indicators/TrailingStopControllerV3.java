package com.bookmap.api.simple.demo.indicators;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import velox.api.layer1.common.Log;
import com.bookmap.api.simple.demo.indicators.ConfluenceProvider;
import com.bookmap.api.simple.demo.indicators.ConfluenceTargetSelector;
import com.bookmap.api.simple.demo.indicators.VolumeProfileAnalyzer;

/**
 * TrailingStopController
 * -------------------------------------------------------------
 * 2025‑04‑27 – Revision 3
 * • CompositeMode (TIGHTEST|LOOSEST|AVERAGE)
 * • ATR_HIGH_LOW stop (highest‑high / lowest‑low ± k·ATR with min‑width)
 * • Bug‑resilient: never loosens, handles NaN, normalises weights.
 */
public final class TrailingStopControllerV3 {

    // Cache size monitoring configuration
    private static final int DEFAULT_CACHE_SIZE_THRESHOLD = 1000;
    private static int cacheWarningThreshold = DEFAULT_CACHE_SIZE_THRESHOLD;
    private static long lastCacheWarningTime = 0;
    private static final long CACHE_WARNING_COOLDOWN_MS = 60000; // 1 minute cooldown
    
    /**
     * Set the cache size threshold for monitoring warnings.
     * @param threshold The maximum cache size before warnings are logged
     */
    public static void setCacheSizeThreshold(int threshold) {
        cacheWarningThreshold = Math.max(1, threshold);
    }
    
    /**
     * Get the current cache size threshold.
     * @return The current threshold value
     */
    public static int getCacheSizeThreshold() {
        return cacheWarningThreshold;
    }

    /* ================= domain objects =============== */
    public static final class CandleBar {
        public final double high, low, close;

        public CandleBar(double h, double l, double c) {
            high = h;
            low = l;
            close = c;
        }

        public CandleBar(velox.api.layer1.simplified.Bar bar) {
            this(bar.getHigh(), bar.getLow(), bar.getClose());
        }
    }

    public static final class Position {
        public enum Side {
            LONG, SHORT
        }

        public final Side side;
        public final double entry;
        public final double contractMultiplier;
        public final double size;
        private double worstAdverse = 0.0;   // ≥0, in price units
        private double peakUnreal   = 0.0;
        private double currentUnreal= 0.0;

        public Position(Side s, double e, double contractMultiplier, double size) {
            side = s;
            entry = e;
            this.contractMultiplier = contractMultiplier;
            this.size = size;
            worstAdverse = 0.0;
        }

        // Backward-compatible constructor (defaults to 1.0)
        public Position(Side s, double e) {
            this(s, e, 1.0, 1.0);
        }

        public boolean isLong() {
            return side == Side.LONG;
        }

        public boolean isShort() {
            return side == Side.SHORT;
        }

        /** Call once per bar/tick to update MAE stats. */
        public void updateMae(double high, double low) {
            double adverse = (side == Side.LONG)
                             ? Math.max(0, entry - low)
                             : Math.max(0, high - entry);
            if (adverse > worstAdverse) worstAdverse = adverse;
        }

        public double getWorstAdverse() { return worstAdverse; }

        public void updateEquity(double close) {
            currentUnreal = (side==Side.LONG? close-entry : entry-close)
                            * contractMultiplier * size;
            if (currentUnreal > peakUnreal) peakUnreal = currentUnreal;
        }

        public double getPeakUnreal()     { return peakUnreal; }
        public double getCurrentUnreal()  { return currentUnreal; }
    }

    /* ================= spec ========================= */
    public enum Type {
        FIXED_OFFSET, FIXED_PERCENT,
        ATR, CHANDELIER, VOL_STOP,
        WEIGHTED_ATR, MA_OFFSET,
        ATR_HIGH_LOW,
        GRID, COMPOSITE, TIME_DECAY_WRAPPER,
        TIME_STOP,
        /** three‑layer stop bundle */
        TRIPLE_STOP,
        MEGA_AVERAGE_COMPOSITE,
        MAE,           // NEW – per‑trade stop based on Maximum Adverse Excursion
        DRAWDOWN,      // NEW – exit after equity gives back X from its peak
        /** classic "run-up give-back" trailing stop */
        GIVEBACK,
        SMART_TRIPLE,  // NEW – tightest(childA,childB) then loosest with cat
        DOUBLE_STOP_GIVEBACK_MAE // NEW - Combines Giveback and MAE with min profit condition
    }

    public enum CompositeMode {
        TIGHTEST, LOOSEST, AVERAGE
    }

    /*
     * ─────────────────────────────────────────────────────────*
     * Triple‑layer specification (warn / trigger / catastrophic)
     * ─────────────────────────────────────────────────────────
     */
    public static final class TripleStopSpec {
        public final StopSpec warn, trigger, catastrophic;

        public TripleStopSpec(StopSpec w, StopSpec t, StopSpec c) {
            warn = Objects.requireNonNull(w, "warn stop null");
            trigger = Objects.requireNonNull(t, "trigger stop null");
            catastrophic = Objects.requireNonNull(c, "catastrophic stop null");
        }
    }

    public static final class StopSpec {

        /* core */
        public final Type type;
        public final boolean trailing;

        /* price offset/percent */
        public final double priceOffset, percent;

        /* atr */
        public final int atrPeriod, atrHighLowLookback;
        public final double atrMult, atrHLFactor, minWidthOffset;

        /* weighted atr */
        public final int[] atrPeriods;
        public final double[] atrWeights;

        /* ma offset */
        public final int maPeriod;
        public final double maOffset, offsetPercent;

        /* grid */
        public final double gridStepOffset;
        public final int gridLevels;
        public final StopSpec baseSpec;

        /* composite / triple */
        public final List<StopSpec> children;
        public final CompositeMode compositeMode;
        public final TripleStopSpec tripleSpec; // << NEW

        /* decay */
        public final double decayFactor;
        public final int decayBars;

        /* trailing increment */
        public final double trailingIncrement;

        /* activation/break-even/time-stop */
        public final int activationBars, timeStopBars;
        public final double activationMove;
        public final boolean breakEvenEnabled;
        public final double breakEvenTrigger, breakEvenBuffer;

        public final ConfluenceProvider confluence;

        /* === NEW for MAE stop === */
        public final double maeOffset;          // absolute price units (e.g. ticks/pips)
        public final double maePnl;             // currency units

        /* ---- NEW fields for drawdown ---- */
        public final double ddAmount;       // $ give-back cap
        public final double ddPercent;      // % of peak unrealised

        /* ---- NEW field for DOUBLE_STOP_GIVEBACK_MAE ---- */
        public final double minProfitForGivebackOffset;

        /* for SMART_TRIPLE we reuse childA/B/C */
        private final StopSpec childA;       // for COMPOSITE/TRIPLE ...
        private final StopSpec childB;
        private final StopSpec childC;

        private StopSpec(Builder b) {
            type = b.type;
            trailing = b.trailing;
            priceOffset = b.priceOffset;
            percent = b.percent;
            atrPeriod = b.atrPeriod;
            atrMult = b.atrMult;
            atrHighLowLookback = b.atrHighLowLookback;
            atrHLFactor = b.atrHLFactor;
            minWidthOffset = b.minWidthOffset;
            atrPeriods = b.atrPeriods;
            atrWeights = b.atrWeights;
            maPeriod = b.maPeriod;
            maOffset = b.maOffset;
            offsetPercent = b.offsetPercent;
            gridStepOffset = b.gridStepOffset;
            gridLevels = b.gridLevels;
            baseSpec = b.baseSpec;
            List<StopSpec> childrenInit = b.children;
            if (b.type == Type.DOUBLE_STOP_GIVEBACK_MAE) {
                childrenInit = new ArrayList<>();
                if (b.childA != null) childrenInit.add(b.childA);
                if (b.childB != null) childrenInit.add(b.childB);
            }
            this.children = childrenInit;
            compositeMode = b.compositeMode;
            tripleSpec = b.tripleSpec;
            decayFactor = b.decayFactor;
            decayBars = b.decayBars;
            trailingIncrement = b.trailingIncrement;
            activationBars = b.activationBars;
            activationMove = b.activationMove;
            breakEvenEnabled = b.breakEvenEnabled;
            breakEvenTrigger = b.breakEvenTrigger;
            breakEvenBuffer = b.breakEvenBuffer;
            timeStopBars = b.timeStopBars;
            confluence = b.confluence;
            maeOffset = b.maeOffset;
            maePnl = b.maePnl;
            ddAmount = b.ddAmount;
            ddPercent = b.ddPercent;
            this.childA = b.childA;
            this.childB = b.childB;
            this.childC = b.childC;
            this.minProfitForGivebackOffset = b.minProfitForGivebackOffsetParam;
        }

        public String key() {
            // Include all fields that affect stop behavior
            String childrenKeys;
            if (children == null) {
                childrenKeys = "";
            } else {
                StringBuilder childrenSb = new StringBuilder();
                for (int i = 0; i < children.size(); i++) {
                    if (i > 0) {
                        childrenSb.append(",");
                    }
                    childrenSb.append(children.get(i).key());
                }
                childrenKeys = childrenSb.toString();
            }
            
            String baseKey = (baseSpec == null) ? "" : baseSpec.key();
            
            String tripleSpecKey;
            if (tripleSpec == null) {
                tripleSpecKey = "";
            } else {
                StringBuilder tripleSpecSb = new StringBuilder();
                tripleSpecSb.append(tripleSpec.warn.key())
                           .append(tripleSpec.trigger.key())
                           .append(tripleSpec.catastrophic.key());
                tripleSpecKey = tripleSpecSb.toString();
            }
            
            int hash = Objects.hash(
                    type, trailing, priceOffset, percent, atrPeriod, atrMult,
                    atrHighLowLookback, atrHLFactor, minWidthOffset,
                    Arrays.toString(atrPeriods), Arrays.toString(atrWeights),
                    maPeriod, maOffset, offsetPercent,
                    gridStepOffset, gridLevels, baseKey, childrenKeys,
                    tripleSpecKey,
                    compositeMode == null ? "" : compositeMode.name(),
                    decayFactor, decayBars, trailingIncrement,
                    activationBars, activationMove, breakEvenEnabled, breakEvenTrigger, breakEvenBuffer, timeStopBars);
            return Integer.toHexString(hash);
        }

        @Override
        public String toString() {
            return type + "|" + trailing + "|" + priceOffset + "|" + percent + "|" + atrPeriod + "|" + atrMult + "|"
                    + atrHighLowLookback + "|" + atrHLFactor;
        }

        /* -------- builder -------- */
        public static final class Builder {
            private final Type type;
            private boolean trailing = true;
            private double priceOffset, percent, atrMult, atrHLFactor, minWidthOffset, maOffset, offsetPercent,
                    gridStepOffset, decayFactor;
            private int atrPeriod = 14, atrHighLowLookback = 20, maPeriod = 20, gridLevels = 5, decayBars = 50;
            private int[] atrPeriods;
            private double[] atrWeights;
            private StopSpec baseSpec;
            private List<StopSpec> children;
            private CompositeMode compositeMode = CompositeMode.TIGHTEST;
            private TripleStopSpec tripleSpec;
            private double trailingIncrement = 0.0;
            private int activationBars = 0, timeStopBars = 0;
            private double activationMove = 0.0;
            private boolean breakEvenEnabled = false;
            private double breakEvenTrigger = 0.0, breakEvenBuffer = 0.0;
            private ConfluenceProvider confluence;
            // === NEW builder field for MAE ===
            private double maeOffset = Double.NaN;
            private double maePnl    = Double.NaN;
            /* drawdown builder fields */
            private double ddAmount  = Double.NaN;
            private double ddPercent = Double.NaN;
            private StopSpec childA;
            private StopSpec childB;
            private StopSpec childC;
            private double minProfitForGivebackOffsetParam = 0.0; // For DOUBLE_STOP_GIVEBACK_MAE

            public Builder(Type t) {
                type = t;
            }

            public Builder trailing(boolean v) {
                trailing = v;
                return this;
            }

            public Builder priceOffset(double v) {
                if (v < 0)
                    throw new IllegalArgumentException("priceOffset < 0");
                priceOffset = v;
                return this;
            }

            public Builder percent(double v) {
                if (v < 0)
                    throw new IllegalArgumentException("percent < 0");
                percent = v;
                return this;
            }

            public Builder atr(int period, double k) {
                if (period <= 0)
                    throw new IllegalArgumentException("atrPeriod <= 0");
                if (k < 0)
                    throw new IllegalArgumentException("atrMult < 0");
                atrPeriod = period;
                atrMult = k;
                return this;
            }

            public Builder atrHighLow(int look, int period, double k, double minOffset) {
                if (look <= 0)
                    throw new IllegalArgumentException("lookback <= 0");
                if (period <= 0)
                    throw new IllegalArgumentException("atrPeriod <= 0");
                if (k < 0)
                    throw new IllegalArgumentException("atrHLFactor < 0");
                atrHighLowLookback = look;
                atrPeriod = period;
                atrHLFactor = k;
                minWidthOffset = minOffset;
                return this;
            }

            public Builder weightedAtr(int[] periods, double[] w, double k) {
                if (periods == null || w == null || periods.length != w.length)
                    throw new IllegalArgumentException("periods/weights len mismatch");
                for (int p : periods)
                    if (p <= 0)
                        throw new IllegalArgumentException("period <= 0");
                double sum = 0;
                for (double x : w)
                    sum += x;
                if (sum < 1e-12)
                    throw new IllegalArgumentException("All weights zero");
                if (k < 0)
                    throw new IllegalArgumentException("atrMult < 0");
                atrPeriods = periods;
                atrWeights = w;
                atrMult = k;
                return this;
            }

            public Builder maOffset(int period, double offset, double pct) {
                if (period <= 0)
                    throw new IllegalArgumentException("maPeriod <= 0");
                if (offset < 0)
                    throw new IllegalArgumentException("maOffset < 0");
                if (pct < 0)
                    throw new IllegalArgumentException("offsetPercent < 0");
                maPeriod = period;
                maOffset = offset;
                offsetPercent = pct;
                return this;
            }

            public Builder grid(double step, int levels, StopSpec base) {
                if (step < 0)
                    throw new IllegalArgumentException("gridStepOffset < 0");
                if (levels <= 0)
                    throw new IllegalArgumentException("gridLevels <= 0");
                gridStepOffset = step;
                gridLevels = levels;
                baseSpec = base;
                return this;
            }

            public Builder composite(List<StopSpec> kids, CompositeMode mode) {
                children = kids;
                compositeMode = mode;
                return this;
            }

            /* ---- triple-layer ---- */
            public Builder triple(StopSpec warn, StopSpec trigger, StopSpec cat) {
                tripleSpec = new TripleStopSpec(warn, trigger, cat);
                return this;
            }

            public Builder decay(double factor, int bars, StopSpec base) {
                if (factor <= 0 || factor >= 1)
                    throw new IllegalArgumentException("decayFactor must be (0,1)");
                if (bars <= 0)
                    throw new IllegalArgumentException("decayBars <= 0");
                decayFactor = factor;
                decayBars = bars;
                baseSpec = base;
                return this;
            }

            public Builder trailingIncrement(double v) {
                if (v < 0)
                    throw new IllegalArgumentException("trailingIncrement < 0");
                trailingIncrement = v;
                return this;
            }

            public Builder activationBars(int v) {
                if (v < 0)
                    throw new IllegalArgumentException("activationBars < 0");
                activationBars = v;
                return this;
            }

            public Builder activationMove(double v) {
                if (v < 0)
                    throw new IllegalArgumentException("activationMove < 0");
                activationMove = v;
                return this;
            }

            public Builder breakEvenEnabled(boolean v) {
                breakEvenEnabled = v;
                return this;
            }

            public Builder breakEvenTrigger(double v) {
                if (v < 0)
                    throw new IllegalArgumentException("breakEvenTrigger < 0");
                breakEvenTrigger = v;
                return this;
            }

            public Builder breakEvenBuffer(double v) {
                if (v < 0)
                    throw new IllegalArgumentException("breakEvenBuffer < 0");
                breakEvenBuffer = v;
                return this;
            }

            public Builder timeStopBars(int v) {
                if (v < 0)
                    throw new IllegalArgumentException("timeStopBars < 0");
                timeStopBars = v;
                return this;
            }

            public Builder confluence(ConfluenceProvider c) {
                confluence = c;
                return this;
            }

            /** Switch to MAE mode and set its offset (absolute price units). */
            public Builder mae(double offset) {
                // This method is for convenience if you want to set MAE type and offset in one call
                // (You can also just use new Builder(Type.MAE).mae(offset) if you want)
                this.maeOffset = offset;
                return this;
            }

            public Builder maePnl(double dollars) {
                this.maePnl = dollars;
                return this;
            }

            /** Draw-down expressed as absolute currency amount. */
            public Builder drawdown(double amount) {
                this.ddAmount = amount;
                return this;
            }
            /** Draw-down as percentage (e.g. 0.4 → 40 % of peak profit). */
            public Builder drawdownPct(double pct) {
                this.ddPercent = pct;
                return this;
            }

            /** Convenience builder for the Tight → Loose → Cat pattern. */
            public Builder smartTriple(StopSpec first, StopSpec second, StopSpec cat) {
                this.childA = first;
                this.childB = second;
                this.childC = cat;
                return this;
            }

            /**
             * Configures a double stop strategy combining Giveback and MAE.
             * The Giveback component is only considered if the position is in profit by at least minProfitOffset.
             * The overall behavior (e.g., trailingIncrement) is defined by the parent DOUBLE_STOP_GIVEBACK_MAE spec.
             * @param givebackSpecConfig The StopSpec for the Giveback component (must be Type.GIVEBACK).
             * @param maeSpecConfig The StopSpec for the MAE component (must be Type.MAE).
             * @param minProfitOffsetForGiveback The minimum profit (absolute price units) required for Giveback to be active.
             */
            public Builder doubleStop(StopSpec givebackSpecConfig, StopSpec maeSpecConfig, double minProfitOffsetForGiveback) {
                if (this.type != Type.DOUBLE_STOP_GIVEBACK_MAE) {
                    throw new IllegalStateException("doubleStop builder method is only for Type.DOUBLE_STOP_GIVEBACK_MAE");
                }
                if (givebackSpecConfig.type != Type.GIVEBACK) {
                    throw new IllegalArgumentException("Child A for DoubleStop must be of Type.GIVEBACK");
                }
                if (maeSpecConfig.type != Type.MAE) {
                    throw new IllegalArgumentException("Child B for DoubleStop must be of Type.MAE");
                }
                this.childA = givebackSpecConfig;
                this.childB = maeSpecConfig;
                this.minProfitForGivebackOffsetParam = minProfitOffsetForGiveback;
                return this;
            }

            /** Abbrev: GIVEBACK-stop expressed as percentage give-back (e.g., 2.0 for 2%). */
            public Builder giveback(double pct) {
                if (pct < 0) throw new IllegalArgumentException("Giveback percentage cannot be negative.");
                this.percent = pct / 100.0; // Store as fraction
                return this;
            }

            // Sample usage for SMART_TRIPLE:
            // StopSpec smart = new StopSpec.Builder(Type.SMART_TRIPLE)
            //         .smartTriple(atrStop, ddStop, catStop)
            //         .trailing(true)
            //         .build();

            public StopSpec build() {
                return new StopSpec(this);
            }
        }
    }

    /* ================ strategy interface ================ */
    public interface StopStrategy {
        double update(Position pos, CandleBar bar);

        double current();

        boolean isTrailing();
    }

    /* ================ helpers =========================== */
    private static final class WilderATR {
        private final int period;
        private Double prevClose = null;
        private final Deque<Double> window = new ArrayDeque<>();

        WilderATR(int p) {
            period = p;
        }

        double update(CandleBar b) {
            double tr = prevClose == null ? b.high - b.low
                    : Math.max(b.high - b.low,
                            Math.max(Math.abs(b.high - prevClose), Math.abs(b.low - prevClose)));
            prevClose = b.close;
            window.addLast(tr);
            if (window.size() > period)
                window.removeFirst();
            return window.stream().mapToDouble(Double::doubleValue).average().orElse(Double.NaN);
        }
    }

    private abstract static class AbstractStrategy implements StopStrategy {
        protected double stop = Double.NaN;
        protected double trailingIncrement = 0.0;
        protected Position.Side previousPositionSide = null; // To detect position flips

        public double current() {
            return stop;
        }

        protected double applyTrailingIncrement(double prevStop, double candidateStop, boolean isLong) {
            if (Double.isNaN(candidateStop)) {
                return prevStop; // If new candidate is invalid, stick with the old stop (which could be NaN).
            }

            // If this is the first time setting the stop (prevStop is NaN), accept the candidate directly.
            if (Double.isNaN(prevStop)) {
                return candidateStop;
            }

            double currentInc = this.trailingIncrement;

            // If no trailing increment is set, or it's not positive,
            // then it's a "tighten only" stop without quantization.
            if (currentInc <= 0.0 || Double.isNaN(currentInc)) {
                if (isLong) {
                    // For long, candidate is tighter if it's greater than prevStop.
                    return (candidateStop > prevStop) ? candidateStop : prevStop;
                } else {
                    // For short, candidate is tighter if it's less than prevStop.
                    return (candidateStop < prevStop) ? candidateStop : prevStop;
                }
            }

            // Regular quantized trailing stop logic
            double eps = 1e-8; // Epsilon for floating point comparisons

            if (isLong) { // For long positions, stop should only move up (increase)
                // Check if candidate is an improvement at all (moved in the favorable direction)
                if (candidateStop > prevStop + eps) { 
                    // Check if it crosses an increment boundary
                    if (candidateStop >= prevStop + currentInc - eps) {
                         double steps = Math.floor((candidateStop - prevStop) / currentInc);
                         if (steps > 0) { // Ensure we are actually moving by at least one step
                            return prevStop + steps * currentInc;
                         }
                         return prevStop; // Calculated steps was 0, means not a full increment away
                    } else {
                         return prevStop; // Improved, but not enough for an increment step
                    }
                } else {
                    return prevStop; // Not an improvement or same
                }
            } else { // Short position
                // Check if candidate is an improvement at all
                if (candidateStop < prevStop - eps) { 
                    if (candidateStop <= prevStop - currentInc + eps) {
                        double steps = Math.floor((prevStop - candidateStop) / currentInc);
                        if (steps > 0) { // Ensure we are actually moving by at least one step
                            return prevStop - steps * currentInc;
                        }
                        return prevStop; // Calculated steps was 0, means not a full increment away
                    } else {
                        return prevStop; // Improved, but not enough for an increment step
                    }
                } else {
                    return prevStop; // Not an improvement or same
                }
            }
        }
    }

    /* ===== existing strategies omitted for brevity in this snippet ===== */
    /*
     * For demonstration we include only new/changed ones. Implementations of
     * FixedOffsetStrategy etc. are identical to earlier revision and should be
     * retained in the real file.
     */

    /* ---------- Composite with mode ---------- */
    private static final class CompositeStrategy extends AbstractStrategy {
        private final List<StopStrategy> kids;
        private final CompositeMode mode;

        CompositeStrategy(List<StopStrategy> k, CompositeMode m) {
            kids = k;
            mode = m;
        }

        public boolean isTrailing() {
            return true;
        }

        public double update(Position pos, CandleBar bar) {
            double combinedValueFromChildren = Double.NaN;

            if (mode == CompositeMode.AVERAGE) {
                double sum = 0.0;
                int count = 0;
                for (StopStrategy s : kids) {
                    double childStop = s.update(pos, bar);
                    // Exclude NaN and Infinite values from average calculation
                    if (!Double.isNaN(childStop) && !Double.isInfinite(childStop)) {
                        sum += childStop;
                        count++;
                    }
                }
                if (count > 0) {
                    combinedValueFromChildren = sum / count;
                } else {
                    combinedValueFromChildren = Double.NaN; // No valid children to average
                }
            } else { // TIGHTEST or LOOSEST
                for (StopStrategy s : kids) {
                    double childStop = s.update(pos, bar);
                    if (Double.isNaN(childStop)) {
                        continue;
                    }
                    if (Double.isNaN(combinedValueFromChildren)) {
                        combinedValueFromChildren = childStop;
                    } else if (mode == CompositeMode.TIGHTEST) {
                        combinedValueFromChildren = pos.isLong() ? Math.max(combinedValueFromChildren, childStop) : Math.min(combinedValueFromChildren, childStop);
                    } else { // LOOSEST
                        combinedValueFromChildren = pos.isLong() ? Math.min(combinedValueFromChildren, childStop) : Math.max(combinedValueFromChildren, childStop);
                    }
                }
            }

            if (Double.isNaN(combinedValueFromChildren)) {
                return this.stop; // Hold previous if children are invalid or average could not be computed
            }

            if (this.trailingIncrement > 0 && !Double.isNaN(this.trailingIncrement)) {
                this.stop = applyTrailingIncrement(this.stop, combinedValueFromChildren, pos.isLong());
            } else {
                if (Double.isNaN(this.stop) ||
                    (pos.isLong() && combinedValueFromChildren > this.stop) ||
                    (!pos.isLong() && combinedValueFromChildren < this.stop)) {
                    this.stop = combinedValueFromChildren;
                }
                // Else: this.stop (the previous, tighter or equal value) is retained.
            }
            return this.stop;
        }
    }

    /* ──────────────── Triple-layer wrapper ──────────────── */
    public static final class TripleStopStrategy extends AbstractStrategy {
        private final StopStrategy warnS, triggerS, catS;
        private final TripleStopSpec spec;

        TripleStopStrategy(TripleStopSpec spec, Position pos) {
            this.spec = spec;
            this.warnS = factory(spec.warn, pos);
            this.triggerS = factory(spec.trigger, pos);
            this.catS = factory(spec.catastrophic, pos);
        }

        /* accessor helpers */
        public double warnValue() {
            return warnS.current();
        }

        public double catastrophicValue() {
            return catS.current();
        }

        public boolean isTrailing() {
            return true;
        }

        public double update(Position pos, CandleBar bar) {
            double warn = warnS.update(pos, bar);
            double trig = triggerS.update(pos, bar);
            double cat = catS.update(pos, bar);

            boolean hitCat  = !Double.isNaN(cat)  && (pos.isLong() ? bar.low <= cat  : bar.high >= cat);
            boolean hitTrig = !hitCat && !Double.isNaN(trig) && (pos.isLong() ? bar.low <= trig : bar.high >= trig);

            double effectiveStopCandidate = hitCat ? cat : trig; 
            
            if (hitCat || hitTrig) {
                this.stop = effectiveStopCandidate;
                return this.stop; 
            }

            this.stop = applyTrailingIncrement(this.stop, effectiveStopCandidate, pos.isLong());
            return this.stop;
        }
    }

    /* ---------- ATR-HighLow strategy ---------- */
    private static final class ATRHighLowStrategy extends AbstractStrategy {
        private final int lookback;
        private final double k, minWidth;
        private final WilderATR atr;
        private final Deque<Double> highs = new ArrayDeque<>(), lows = new ArrayDeque<>();

        ATRHighLowStrategy(int look, int atrPeriod, double k, double minOffset, double inc) {
            if (look <= 0) throw new IllegalArgumentException("lookback <= 0 for ATRHighLowStrategy");
            this.lookback = look;
            this.k = k;
            this.minWidth = minOffset;
            this.atr = new WilderATR(atrPeriod);
            this.trailingIncrement = inc;
        }

        public boolean isTrailing() {
            return true;
        }

        public double update(Position pos, CandleBar bar) {
            // Detect position flip to reset stop. ATR, highs/lows deques persist.
            if (previousPositionSide == null || previousPositionSide != pos.side) {
                this.stop = Double.NaN;    // Reset stop on flip
                this.previousPositionSide = pos.side;
            }

            // Update market data windows (highs, lows) and ATR
            // Guard against NaN in bar data before adding to deques
            if (!Double.isNaN(bar.high)) highs.addLast(bar.high);
            if (!Double.isNaN(bar.low)) lows.addLast(bar.low);
            
            while (highs.size() > lookback) highs.removeFirst();
            while (lows.size() > lookback) lows.removeFirst();
            
            double currentAtr = atr.update(bar); // ATR internal state persists

            if (highs.size() < lookback || lows.size() < lookback) {
                return this.stop; // Insufficient HH/LL lookback data, return current stop (could be NaN)
            }
            if (Double.isNaN(currentAtr) || Double.isInfinite(currentAtr)) {
                return this.stop; // Insufficient ATR data or invalid ATR, return current stop
            }

            double highestHigh = Collections.max(highs);
            double lowestLow = Collections.min(lows);

            if (Double.isNaN(highestHigh) || Double.isNaN(lowestLow)) { // Should not happen if deques are guarded
                 return this.stop;
            }

            double bandLow = lowestLow - k * currentAtr;
            double bandHigh = highestHigh + k * currentAtr;
            
            // Min width guard
            if (minWidth > 0) {
                double currentWidth = bandHigh - bandLow;
                if (currentWidth < minWidth) {
                    double expand = (minWidth - currentWidth) / 2;
                    bandLow -= expand;
                    bandHigh += expand;
                }
            }
            
            double candStop = pos.isLong() ? bandLow : bandHigh;

            // ATRHighLow is always treated as trailing for the purpose of stop updates.
            // applyTrailingIncrement handles initialization (if this.stop is NaN) and trailing.
            this.stop = applyTrailingIncrement(this.stop, candStop, pos.isLong());
            
            return this.stop;
        }
    }

    /*
     * ================ Time-decay, Grid, etc. unchanged from rev-2 ================
     */
    /* For brevity they are omitted here but should remain the same in full file. */

    /* ================ factory & cache ================ */
    private static final Map<String, StopStrategy> CACHE = new ConcurrentHashMap<>();

    public static double getStopPrice(Position pos, CandleBar bar, StopSpec spec) {
        // Monitor cache size and log warnings if threshold exceeded
        int currentCacheSize = CACHE.size();
        if (currentCacheSize > cacheWarningThreshold) {
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastCacheWarningTime > CACHE_WARNING_COOLDOWN_MS) {
                Log.info("TrailingStopControllerV3 cache size warning: current size=" + currentCacheSize + 
                         ", threshold=" + cacheWarningThreshold + 
                         ". Consider reviewing cache usage patterns for memory optimization.");
                lastCacheWarningTime = currentTime;
            }
        }
        
        String cacheKey;
        if (spec.type == Type.MA_OFFSET ||
            spec.type == Type.ATR ||
            spec.type == Type.CHANDELIER ||
            spec.type == Type.VOL_STOP ||
            spec.type == Type.WEIGHTED_ATR ||
            spec.type == Type.ATR_HIGH_LOW ||
            spec.type == Type.TIME_DECAY_WRAPPER) {
            // For these types, use only spec.key() to reuse instance & its internal data.
            // The strategy itself now handles resetting its state on a flip.
            cacheKey = spec.key();
        } else if (spec.type == Type.GIVEBACK) {
            /*  Stand‑alone GIVEBACK and the GIVEBACK used inside the composite
                stop must *not* share state.  Appending the object identity to
                the key guarantees each builder call gets its own instance.   */
            cacheKey = spec.key() + "|" + System.identityHashCode(spec);
        } else {
            // All other types keep the original behaviour
            cacheKey = spec.key() + "|" + pos.entry + "|" + pos.side;
        }
        StopStrategy s = CACHE.computeIfAbsent(cacheKey, k -> factory(spec, pos));
        
        // Log cache statistics periodically for debugging (every 100 calls when size > 500)
        if (currentCacheSize > 500 && currentCacheSize % 100 == 0) {
            Log.info("TrailingStopControllerV3 cache statistics: size=" + currentCacheSize + 
                     ", latest key type=" + spec.type);
        }
        
        // === NEW: update MAE stats for MAE stops ===
        if (spec.type == Type.MAE && pos != null && bar != null) {
            pos.updateMae(bar.high, bar.low);
        }
        return s.update(pos, bar);
    }

    /**
     * Get the current cache size for monitoring purposes.
     * @return The number of entries currently in the strategy cache
     */
    public static int getCacheSize() {
        return CACHE.size();
    }
    
    /**
     * Clear the strategy cache. Use with caution as this will reset all cached strategies.
     * This can be useful for memory management in long-running applications.
     */
    public static void clearCache() {
        int sizeBefore = CACHE.size();
        CACHE.clear();
        Log.info("TrailingStopControllerV3 cache cleared. Previous size: " + sizeBefore);
    }
    
    /**
     * Get cache statistics as a formatted string for debugging.
     * @return String containing cache size and threshold information
     */
    public static String getCacheStatistics() {
        return String.format("TrailingStopControllerV3 Cache Stats - Size: %d, Threshold: %d, Utilization: %.1f%%",
                           CACHE.size(), cacheWarningThreshold, 
                           (double) CACHE.size() / cacheWarningThreshold * 100.0);
    }

    public static StopStrategy factory(StopSpec s, Position pos) {
        StopStrategy base;
        switch (s.type) {
            case FIXED_OFFSET:
                base = new FixedOffsetStrategy(s.priceOffset, s.trailing, s.trailingIncrement, s.confluence);
                break;
            case FIXED_PERCENT:
                base = new FixedPercentStrategy(s.percent, s.trailing, s.trailingIncrement);
                break;
            case ATR:
                base = new ATRStrategy(s.atrPeriod, s.atrMult, s.trailing, s.trailingIncrement);
                break;
            case CHANDELIER:
                base = new ChandelierStrategy(s.atrPeriod, s.atrMult, s.trailingIncrement);
                break;
            case VOL_STOP:
                base = new VolStopStrategy(s.atrPeriod, s.atrMult, s.trailingIncrement);
                break;
            case WEIGHTED_ATR:
                base = new WeightedATRStrategy(s.atrPeriods, s.atrWeights, s.atrMult, s.trailing, s.trailingIncrement);
                break;
            case MA_OFFSET:
                base = new MAOffsetStrategy(s.maPeriod, s.maOffset, s.offsetPercent, s.trailing, s.trailingIncrement);
                break;
            case TIME_DECAY_WRAPPER:
                base = new TimeDecayWrapper(factory(s.baseSpec, pos), s.decayFactor, s.decayBars, s.trailingIncrement);
                break;
            case ATR_HIGH_LOW:
                base = new ATRHighLowStrategy(s.atrHighLowLookback, s.atrPeriod, s.atrHLFactor, s.minWidthOffset,
                        s.trailingIncrement);
                break;
            case MAE: {
                // MAE stop logic: only activate after worst adverse excursion >= offset
                base = new StopStrategy() {
                    private double stop = Double.NaN;
                    public boolean isTrailing() { return false; }
                    public double current() { return stop; }
                    public double update(Position pos, CandleBar bar) {
                        if (pos == null) return Double.NaN;
                        // Defensive: update MAE stats (should also be done in trading loop)
                        if (bar != null) pos.updateMae(bar.high, bar.low);
                        double offset = s.maeOffset;
                        double candidate = (pos.side == Position.Side.LONG)
                                ? pos.entry - offset
                                : pos.entry + offset;
                        if (pos.getWorstAdverse() + 1e-9 >= offset) {
                            stop = candidate;
                            return stop;
                        }
                        return Double.NaN;
                    }
                };
                break;
            }
            case COMPOSITE: {
                List<StopStrategy> list = new ArrayList<>();
                for (StopSpec c : s.children)
                    list.add(factory(c, pos));
                CompositeStrategy cs = new CompositeStrategy(list, s.compositeMode);
                cs.trailingIncrement = s.trailingIncrement;
                base = cs;
                break;
            }
            case GRID:
                GridStopStrategy gss = new GridStopStrategy(factory(s.baseSpec, pos), s.gridStepOffset, s.gridLevels);
                gss.trailingIncrement = s.trailingIncrement;
                base = gss;
                break;
            case TIME_STOP:
                base = new TimeStopStrategy(s.timeStopBars > 0 ? s.timeStopBars : 1, pos);
                break;
            case TRIPLE_STOP:
                TripleStopStrategy tss = new TripleStopStrategy(s.tripleSpec, pos);
                tss.trailingIncrement = s.trailingIncrement; // <- add
                base = tss;
                break;
            case MEGA_AVERAGE_COMPOSITE:
                {
                    List<StopStrategy> list = new ArrayList<>();
                    if (s.children != null) { // Ensure children are defined in the spec
                        for (StopSpec c : s.children)
                            list.add(factory(c, pos)); // Recursively create children strategies
                    }
                    CompositeStrategy mcs = new CompositeStrategy(list, CompositeMode.AVERAGE); // Use AVERAGE mode
                    mcs.trailingIncrement = s.trailingIncrement; // Apply overall trailing increment from spec
                    base = mcs;
                    break;
                }
            case DRAWDOWN: {
                base = new StopStrategy() {
                    private double stop = Double.NaN;
                    public boolean isTrailing() { return false; }
                    public double current() { return stop; }
                    public double update(Position pos, CandleBar bar) {
                        if (pos == null || bar == null) return Double.NaN;
                        // User must call pos.updateEquity(bar.close) externally before this
                        double trigCash = !Double.isNaN(s.ddAmount)
                            ? s.ddAmount
                            : s.ddPercent * pos.getPeakUnreal();
                        if (pos.getPeakUnreal() - pos.getCurrentUnreal() + 1e-9 < trigCash)
                            return Double.NaN;
                        double pxOffset = trigCash / (pos.contractMultiplier * pos.size);
                        stop = (pos.side == Position.Side.LONG)
                            ? bar.close - pxOffset
                            : bar.close + pxOffset;
                        return stop;
                    }
                };
                break;
            }
            case GIVEBACK: {
                base = new GivebackStrategy(s.percent, s.trailingIncrement, s.minProfitForGivebackOffset);
                break;
            }
            case SMART_TRIPLE: {
                base = new StopStrategy() {
                    private double stop = Double.NaN;
                    public boolean isTrailing() { return false; }
                    public double current() { return stop; }
                    public double update(Position pos, CandleBar bar) {
                        if (pos == null || bar == null) return Double.NaN;
                        double a = getStopPrice(pos, bar, s.childA);
                        double b = getStopPrice(pos, bar, s.childB);
                        double inner;
                        if (Double.isNaN(a)) inner = b;
                        else if (Double.isNaN(b)) inner = a;
                        else
                            inner = (pos.side==Position.Side.LONG)
                                    ? Math.max(a,b)
                                    : Math.min(a,b);
                        double c = getStopPrice(pos, bar, s.childC);
                        if (Double.isNaN(c)) {
                            stop = inner;
                            return stop;
                        }
                        if (Double.isNaN(inner)) {
                            stop = c;
                            return stop;
                        }
                        stop = (pos.side==Position.Side.LONG)
                                ? Math.min(inner,c)
                                : Math.max(inner,c);
                        return stop;
                    }
                };
                break;
            }
            case DOUBLE_STOP_GIVEBACK_MAE: {
                /* ── Robust validation — fail fast & self‑explanatory ───────────────── */
                if (s.childA == null || s.childB == null) {
                    throw new IllegalArgumentException(
                            "DOUBLE_STOP_GIVEBACK_MAE requires childA (GIVEBACK) and childB (MAE)");
                }
                if (s.childA.type != Type.GIVEBACK) {
                    throw new IllegalArgumentException("childA must be GIVEBACK (was " + s.childA.type + ')');
                }
                if (s.childB.type != Type.MAE) {
                    throw new IllegalArgumentException("childB must be MAE (was " + s.childB.type + ')');
                }
                if (Double.isNaN(s.minProfitForGivebackOffset) || s.minProfitForGivebackOffset <= 0) {
                    throw new IllegalArgumentException(
                            "Positive minProfitForGivebackOffset is required for DOUBLE_STOP_GIVEBACK_MAE");
                }
                base = new DoubleStopGivebackMaeStrategy(s, pos);
                break;
            }
            default:
                throw new IllegalArgumentException("Unsupported type " + s.type);
        }
        // Wrap with activation if needed
        if (s.activationBars > 0 || s.activationMove > 0) {
            base = new ActivationWrapper(base, s.activationBars, s.activationMove, pos);
        }
        // Wrap with break-even if needed
        if (s.breakEvenEnabled && s.breakEvenTrigger > 0) {
            base = new BreakEvenWrapper(base, s.breakEvenTrigger, s.breakEvenBuffer, pos);
        }
        return base;
    }

    // Overload for backward compatibility
    private static StopStrategy factory(StopSpec s) {
        return factory(s, null);
    }

    /**
     * Overload to accept a Velox Bar object, extract HLC, create a CandleBar,
     * and call the original getStopPrice method.
     */
    public static double getStopPrice(Position pos, velox.api.layer1.simplified.Bar bar, StopSpec spec) {
        if (bar == null) {
            // Consider logging this case
            return Double.NaN;
        }
        double high = bar.getHigh();
        double low = bar.getLow();
        double close = bar.getClose();

        // Validate extracted values
        if (Double.isNaN(high) || Double.isInfinite(high) ||
            Double.isNaN(low) || Double.isInfinite(low) ||
            Double.isNaN(close) || Double.isInfinite(close)) {
            // Consider logging this case: "Invalid HLC extracted from Bar for stop calculation."
            return Double.NaN; // Cannot calculate stop with invalid data
        }

        // Create the CandleBar required by the core logic
        CandleBar candleBar = new CandleBar(high, low, close);

        // Call the original method that uses CandleBar
        // Note: This assumes the factory caching mechanism within the original
        // getStopPrice handles the state appropriately even when called via this overload.
        // If strategies rely heavily on persistent state *linked* to the bar object itself,
        // this might need adjustment, but for HLC-based stops, it should work.
        return getStopPrice(pos, candleBar, spec);
    }

    private TrailingStopControllerV3() {
    }

    // ==== Begin: Copied unchanged strategies from V2 ====
    private static final class FixedOffsetStrategy extends AbstractStrategy {
        private final double priceOffset;
        private final boolean trailing;
        private double extreme;
        private final ConfluenceProvider confluence;

        FixedOffsetStrategy(double priceOffset, boolean t, double inc) {
            this(priceOffset, t, inc, null);
        }

        FixedOffsetStrategy(double priceOffset, boolean t, double inc, ConfluenceProvider confluence) {
            this.priceOffset = priceOffset;
            trailing = t;
            this.trailingIncrement = inc;
            this.confluence = confluence;
        }

        public boolean isTrailing() {
            return trailing;
        }

        public double update(Position pos, CandleBar bar) {
            if (Double.isNaN(stop))
                extreme = pos.entry;
            if (trailing)
                extreme = pos.isLong() ? Math.max(extreme, bar.high)
                        : Math.min(extreme, bar.low);
            double offset = priceOffset;
            if (confluence != null) {
                double score = confluence.getConfluenceScore((int) pos.entry, System.nanoTime());
                offset = priceOffset * (score >= 0 ? 1.0 + Math.min(score, 2.0) * 0.25
                        : 1.0 + Math.max(score, -2.0) * 0.25);
                offset = Math.max(1e-12, offset);
            }
            double candidate = pos.isLong() ? extreme - offset : extreme + offset;
            candidate = applyTrailingIncrement(stop, candidate, pos.isLong());
            if (Double.isNaN(stop) || (pos.isLong() ? candidate > stop : candidate < stop))
                stop = candidate;
            return stop;
        }
    }

    private static final class FixedPercentStrategy extends AbstractStrategy {
        private final double pct;
        private final boolean trailing;
        private double extreme;

        FixedPercentStrategy(double pct, boolean t, double inc) {
            this.pct = pct;
            trailing = t;
            this.trailingIncrement = inc;
        }

        public boolean isTrailing() {
            return trailing;
        }

        public double update(Position pos, CandleBar bar) {
            if (Double.isNaN(stop))
                extreme = pos.entry;
            if (trailing)
                extreme = pos.isLong() ? Math.max(extreme, bar.high)
                        : Math.min(extreme, bar.low);
            double candidate = pos.isLong() ? extreme * (1 - pct) : extreme * (1 + pct);
            candidate = applyTrailingIncrement(stop, candidate, pos.isLong());
            if (Double.isNaN(stop) || (pos.isLong() ? candidate > stop : candidate < stop))
                stop = candidate;
            return stop;
        }
    }

    private abstract static class AbstractATRStrategy extends AbstractStrategy {
        protected final WilderATR atr;
        protected final double k;
        protected final boolean trailing;
        protected double extreme;

        AbstractATRStrategy(int period, double k, boolean t, double inc) {
            atr = new WilderATR(period);
            this.k = k;
            trailing = t;
            this.trailingIncrement = inc;
        }
    }

    private static final class ATRStrategy extends AbstractATRStrategy {
        ATRStrategy(int p, double k, boolean t, double inc) {
            super(p, k, t, inc);
        }

        public boolean isTrailing() {
            return trailing;
        }

        public double update(Position pos, CandleBar bar) {
            // Detect position flip to reset stop and extreme price, but keep ATR data
            if (previousPositionSide == null || previousPositionSide != pos.side) {
                this.stop = Double.NaN;    // Reset stop on flip
                this.extreme = pos.entry;  // Reset extreme price for the new position
                this.previousPositionSide = pos.side;
            } else if (Double.isNaN(this.stop)) {
                // If stop is NaN (e.g. initial run for this side), initialize extreme
                this.extreme = pos.entry;
            }

            double a = atr.update(bar); // atr's internal state (WilderATR) persists
            if (Double.isNaN(a) || Double.isInfinite(a)) {
                return stop; // ATR is not available, return current stop (could be NaN)
            }

            // Update extreme if it's a trailing stop, or if it's the first calculation for this position side
            if (this.trailing) { // 'trailing' is a field in AbstractATRStrategy
                this.extreme = pos.isLong() ? Math.max(this.extreme, bar.high) : Math.min(this.extreme, bar.low);
            } // If not trailing, extreme remains pos.entry (set at flip/initialization)
            
            double cand = pos.isLong() ? this.extreme - k * a : this.extreme + k * a;
            
            // Apply stop logic based on whether it's a trailing or fixed stop type
            if (this.trailing) {
                this.stop = applyTrailingIncrement(this.stop, cand, pos.isLong());
            } else {
                // Standard non-trailing logic: update if NaN or tighter.
                // (Assumes trailingIncrement is 0 or not applicable for non-trailing)
                if (Double.isNaN(this.stop) ||
                    (pos.isLong() && cand > this.stop) ||
                    (!pos.isLong() && cand < this.stop)) {
                    this.stop = cand;
                }
            }
            return this.stop;
        }
    }

    private static final class ChandelierStrategy extends AbstractATRStrategy {
        ChandelierStrategy(int p, double k, double inc) {
            super(p, k, true, inc);
        }

        public boolean isTrailing() {
            return true;
        }

        public double update(Position pos, CandleBar bar) {
            // Detect position flip to reset stop and extreme price, but keep ATR data
            if (previousPositionSide == null || previousPositionSide != pos.side) {
                this.stop = Double.NaN;    // Reset stop on flip
                this.extreme = pos.entry;  // Reset extreme price for the new position
                this.previousPositionSide = pos.side;
            } else if (Double.isNaN(this.stop)) {
                // If stop is NaN (e.g. initial run for this side), initialize extreme
                this.extreme = pos.entry;
            }

            double a = atr.update(bar); // atr's internal state (WilderATR) persists
            if (Double.isNaN(a) || Double.isInfinite(a)) {
                return stop; // ATR is not available, return current stop (could be NaN)
            }

            // Chandelier is always trailing, so always update extreme
            this.extreme = pos.isLong() ? Math.max(this.extreme, bar.high) : Math.min(this.extreme, bar.low);
            
            double cand = pos.isLong() ? this.extreme - k * a : this.extreme + k * a;
            
            // Chandelier is always trailing, use applyTrailingIncrement
            this.stop = applyTrailingIncrement(this.stop, cand, pos.isLong());
            return this.stop;
        }
    }

    private static final class VolStopStrategy extends AbstractStrategy {
        private final WilderATR atr;
        private final double k;
        private double band = Double.NaN;
        private int trend = 0; // 0: undetermined, 1: up-trend (stop is lower band), -1: down-trend (stop is upper band)

        VolStopStrategy(int p, double k, double inc) {
            this.atr = new WilderATR(p);
            this.k = k;
            this.trailingIncrement = inc;
        }

        public boolean isTrailing() {
            return true; // VolStop is inherently a trailing concept
        }

        public double update(Position pos, CandleBar bar) {
            // --- Flip Detection & State Reset ---
            if (previousPositionSide == null || previousPositionSide != pos.side) {
                this.stop = Double.NaN;    // Reset the strategy's stop value
                this.band = Double.NaN;   // Reset band, it will be re-initialized based on the new position
                this.trend = 0;         // Reset trend, it will be re-initialized
                this.previousPositionSide = pos.side; // Update the side being tracked
            }

            // --- ATR Calculation ---
            double currentAtr = this.atr.update(bar); // ATR state persists across calls
            if (Double.isNaN(currentAtr)) {
                return this.stop; 
            }

            // --- Calculate Volatility Bands for the current bar ---
            double midPoint = (bar.high + bar.low) / 2;
            double upperBandCurrentBar = midPoint + this.k * currentAtr;
            double lowerBandCurrentBar = midPoint - this.k * currentAtr;

            // --- Initialize Band and Trend (if needed, e.g., after a flip or first run) ---
            if (Double.isNaN(this.band)) { 
                if (pos.isLong()) {
                    this.band = lowerBandCurrentBar;
                    this.trend = 1; 
                } else { // Position is Short
                    this.band = upperBandCurrentBar;
                    this.trend = -1; 
                }
            }

            // --- Trend Change Logic (SuperTrend style) ---
            // This logic determines if the trend itself should flip.
            // If it flips, it also updates this.band to the new band for the new trend.
            if (this.trend <= 0 && bar.close > upperBandCurrentBar) { // Potential flip to up-trend
                this.trend = 1;
                this.band = lowerBandCurrentBar; // Active band becomes the lower one
            } else if (this.trend >= 0 && bar.close < lowerBandCurrentBar) { // Potential flip to down-trend
                this.trend = -1;
                this.band = upperBandCurrentBar; // Active band becomes the upper one
            } else {
                // NO TREND FLIP: Update this.band to the current bar's calculated band based on the existing trend.
                // This makes the stop line follow the ATR band movement bar-by-bar if no trend flip.
                if (this.trend == 1) {
                    this.band = lowerBandCurrentBar;
                } else if (this.trend == -1) {
                    this.band = upperBandCurrentBar;
                }
                // If trend is 0 (only briefly during init), this.band would have been set in the init block.
            }
            
            // --- Determine Candidate Stop and Apply Trailing Logic ---
            double candidateStop = this.band; // candidateStop is now always based on the current bar's bands & active trend
            
            this.stop = applyTrailingIncrement(this.stop, candidateStop, this.trend > 0);
            
            return this.stop;
        }
    }

    private static final class WeightedATRStrategy extends AbstractStrategy {
        private final WilderATR[] atrs;
        private final double[] weights;
        private final double k;
        private final boolean trailing;
        private double extreme;

        WeightedATRStrategy(int[] periods, double[] w, double k, boolean t, double inc) {
            if (periods.length != w.length)
                throw new IllegalArgumentException("periods/weights len mismatch");
            double sumW = 0.0;
            for (double v : w)
                sumW += v;
            if (sumW < 1e-12)
                throw new IllegalArgumentException("All weights zero");
            this.weights = Arrays.copyOf(w, w.length); // Use a copy
            if (Math.abs(sumW - 1.0) > 1e-6) { // Normalize weights if not already sum to 1
                for (int i = 0; i < this.weights.length; i++)
                    this.weights[i] /= sumW;
            }
            this.atrs = Arrays.stream(periods).mapToObj(WilderATR::new).toArray(WilderATR[]::new);
            this.k = k;
            this.trailing = t;
            this.trailingIncrement = inc;
        }

        public boolean isTrailing() {
            return trailing;
        }

        public double update(Position pos, CandleBar bar) {
            // Detect position flip to reset stop and extreme price, but keep ATRs' states
            if (previousPositionSide == null || previousPositionSide != pos.side) {
                this.stop = Double.NaN;    // Reset stop on flip
                this.extreme = pos.entry;  // Reset extreme price for the new position
                this.previousPositionSide = pos.side;
            } else if (Double.isNaN(this.stop)) {
                // If stop is NaN (e.g. initial run for this side), initialize extreme
                this.extreme = pos.entry;
            }

            double watr = 0.0;
            boolean anyAtrInvalid = false;
            for (int i = 0; i < atrs.length; i++) {
                double atrVal = atrs[i].update(bar); // ATRs' internal states persist
                if (Double.isNaN(atrVal)) {
                    anyAtrInvalid = true;
                    break;
                }
                watr += atrVal * weights[i];
            }

            if (anyAtrInvalid || Double.isNaN(watr)) {
                return this.stop; // Weighted ATR not available, return current stop (could be NaN)
            }
            
            // Update extreme if this strategy instance is set to trailing
            if (this.trailing) {
                this.extreme = pos.isLong() ? Math.max(this.extreme, bar.high) : Math.min(this.extreme, bar.low);
            } // If not trailing, extreme remains pos.entry (set at flip/initialization)
            
            double cand = pos.isLong() ? this.extreme - k * watr : this.extreme + k * watr;
            
            if (this.trailing) {
                this.stop = applyTrailingIncrement(this.stop, cand, pos.isLong());
            } else {
                if (Double.isNaN(this.stop) ||
                    (pos.isLong() && cand > this.stop) ||
                    (!pos.isLong() && cand < this.stop)) {
                    this.stop = cand;
                }
            }
            return this.stop;
        }
    }

    private static final class MAOffsetStrategy extends AbstractStrategy {
        private final int period;
        private final double maOffset, offsetPct;
        private final boolean trailing;
        private final Deque<Double> window = new ArrayDeque<>();
        private double sum = 0.0;

        MAOffsetStrategy(int period, double offset, double pct, boolean t, double inc) {
            this.period = period;
            this.maOffset = offset;
            this.offsetPct = pct;
            this.trailing = t;
            this.trailingIncrement = inc;
        }

        public boolean isTrailing() {
            return trailing;
        }

        public double update(Position pos, CandleBar bar) {
            // Detect position flip to reset stop, but keep MA data (window, sum)
            if (previousPositionSide == null || previousPositionSide != pos.side) {
                this.stop = Double.NaN; // Reset stop on flip or first run for this side
                // this.extreme = pos.entry; // Reset extreme if MAOffsetStrategy used it like ATR strategies
                                         // MAOffsetStrategy doesn't use 'extreme' in the same way, MA is based on window of closes.
                this.previousPositionSide = pos.side;
            }

            // Guard against bar.close being NaN, though upstream should also guard this.
            if (Double.isNaN(bar.close)) {
                // If bar.close is NaN, we cannot update MA. Return current stop.
                // This prevents adding NaN to window and sum.
                return this.stop; 
            }

            window.addLast(bar.close);
            sum += bar.close;

            if (window.size() > period) {
                double removedVal = window.removeFirst();
                // Only subtract if removedVal was a valid number. 
                // If removedVal was NaN, sum might have already become NaN.
                if (!Double.isNaN(removedVal)) {
                    sum -= removedVal;
                }
            }

            // If sum has become NaN (e.g., due to a previous NaN in window or arithmetic issue),
            // and window is not empty, try to recover by recalculating sum from window contents.
            // This assumes elements in 'window' are themselves not NaN due to upstream guards on bar.close.
            if (Double.isNaN(sum) && !window.isEmpty()) {
                sum = 0.0;
                for (Double val : window) {
                    if (Double.isNaN(val)) { // Should not happen if bar.close is guarded
                        sum = Double.NaN; // If window contains NaN, sum must be NaN
                        break;
                    }
                    sum += val;
                }
            }

            if (window.size() < period || Double.isNaN(sum)) {
                // If not enough data OR if sum is NaN (MA cannot be calculated), return previous stop if valid, else NaN.
                // This prevents line disappearing if it was previously plotted and MA temporarily unavailable.
                // However, if this.stop is already NaN, it will return NaN (line disappears/stays disappeared).
                return (window.size() < period) ? Double.NaN : this.stop; 
            }

            double ma = sum / window.size(); // sma()

            if (Double.isNaN(ma)) { // Should be caught by sum check, but as a fallback.
                return this.stop; 
            }

            double delta = maOffset > 0 ? maOffset : ma * offsetPct;
            if (delta <= 0) {
                return this.stop; // Invalid offset, return current stop
            }

            double candidateValue = pos.isLong() ? ma - delta : ma + delta;

            if (this.trailing) {
                // If trailing, applyTrailingIncrement handles initialization (if this.stop is NaN)
                // and trailing (tighten only, by increment).
                this.stop = applyTrailingIncrement(this.stop, candidateValue, pos.isLong());
            } else {
                // If not trailing, standard update: initialize if NaN, or update if candidate is tighter.
                // TrailingIncrement is not used here (or should be 0 for non-trailing stops).
                if (Double.isNaN(this.stop) ||
                    (pos.isLong() && candidateValue > this.stop) ||
                    (!pos.isLong() && candidateValue < this.stop)) {
                    this.stop = candidateValue;
                }
            }
            return this.stop;
        }
    }

    private static final class TimeDecayWrapper extends AbstractStrategy {
        private final StopStrategy inner;
        private final double decay;
        private final int maxBars;
        private int bars = 0; // Counts bars within the current decay cycle
        private double initDist = Double.NaN; // Initial distance for the current decay cycle

        TimeDecayWrapper(StopStrategy s, double factor, int barsDecayPeriod, double inc) {
            inner = s;
            decay = factor;
            maxBars = barsDecayPeriod; // Renamed param for clarity
            this.trailingIncrement = inc;
        }

        public boolean isTrailing() {
            // The wrapper itself behaves like a trailing stop, but relies on inner strategy's trailing nature for base value.
            return true; 
        }

        // current() is inherited from AbstractStrategy (returns this.stop)

        public double update(Position pos, CandleBar bar) {
            // Detect position flip to reset stop and decay-specific state (initDist, bars)
            if (previousPositionSide == null || previousPositionSide != pos.side) {
                this.stop = Double.NaN;         // Reset the wrapper's stop value
                this.initDist = Double.NaN;    // Reset initial distance for the new position's decay cycle
                this.bars = 0;                // Reset bar counter for the new decay cycle
                this.previousPositionSide = pos.side; // Update the side
            }

            double baseStopFromInner = inner.update(pos, bar);

            if (Double.isNaN(baseStopFromInner)) {
                // If inner strategy can't provide a stop, wrapper can't either. 
                // If this.stop was valid, it means inner just became NaN. If this.stop was NaN (e.g. after flip), it stays NaN.
                return this.stop; 
            }

            if (Double.isNaN(this.initDist)) {
                // This will be true after a flip (initDist was reset to NaN) or on the very first call.
                // Calculate initial distance based on the new position and the inner strategy's current stop.
                this.initDist = pos.isLong() ? pos.entry - baseStopFromInner : baseStopFromInner - pos.entry;
                if (this.initDist < 0) { // Should ideally not happen if baseStopFromInner is a valid stop
                    this.initDist = 0; // Ensure non-negative distance; decay won't push past entry aggressively
                }
            }
            
            this.bars++; // Increment bars for the current decay cycle
            
            double decayProgressFactor = Math.pow(decay, Math.min(this.bars, maxBars));
            double decayedTargetStop = pos.isLong() ? 
                                       pos.entry - (this.initDist * decayProgressFactor) :
                                       pos.entry + (this.initDist * decayProgressFactor);
            
            // The candidate stop is the tighter of the inner strategy's stop and the decayed target.
            // For a long position, tighter is MAX; for short, tighter is MIN.
            double candidateStop = pos.isLong() ? 
                                   Math.max(baseStopFromInner, decayedTargetStop) :
                                   Math.min(baseStopFromInner, decayedTargetStop);
            
            // Apply the TimeDecayWrapper's own trailingIncrement to the candidate stop
            if (this.trailingIncrement > 0.0 && !Double.isNaN(this.trailingIncrement)) {
                this.stop = applyTrailingIncrement(this.stop, candidateStop, pos.isLong());
            } else {
                // If no specific trailing increment for the wrapper itself,
                // adopt candidateStop only if it's tighter than current this.stop or if this.stop is NaN.
                if (Double.isNaN(this.stop) ||
                    (pos.isLong() && candidateStop > this.stop) ||
                    (!pos.isLong() && candidateStop < this.stop)) {
                     this.stop = candidateStop;
                }
                // Else: this.stop (previous wrapper value) is retained if candidateStop is looser and no wrapper inc.
            }
            return this.stop;
        }
    }

    private static final class GridStopStrategy extends AbstractStrategy {
        private final StopStrategy base;
        private final double step;
        private final int maxLvl;
        private int level = 0;

        GridStopStrategy(StopStrategy base, double step, int levels) {
            this.base = base;
            this.step = step;
            this.maxLvl = levels - 1;
        }

        public boolean isTrailing() {
            return true;
        }

        public double update(Position pos, CandleBar bar) {
            double baseStopValue = base.update(pos, bar);
            if (Double.isNaN(baseStopValue)) {
                // If base is invalid, hold the previous GridStop value if it exists, otherwise NaN.
                return this.stop; 
            }

            int targetLevel;
            if (pos.isLong()) {
                double move = bar.high - pos.entry;
                targetLevel = (int) Math.min(maxLvl, Math.floor(move / step));
            } else {
                double move = pos.entry - bar.low;
                targetLevel = (int) Math.min(maxLvl, Math.floor(move / step));
            }

            if (targetLevel > this.level) {
                this.level = targetLevel;
            }

            double currentGridLevelValue;
            if (this.level == 0) {
                currentGridLevelValue = pos.isLong() ? Double.NEGATIVE_INFINITY : Double.POSITIVE_INFINITY;
            } else {
                currentGridLevelValue = pos.isLong() ? pos.entry - step * this.level : pos.entry + step * this.level;
            }

            double candidateStopForThisTick;
            if (pos.isLong()) {
                candidateStopForThisTick = Math.max(currentGridLevelValue, baseStopValue);
            } else {
                candidateStopForThisTick = Math.min(currentGridLevelValue, baseStopValue);
            }
            
            // 'this.stop' currently holds the stop value from the previous tick.
            // 'candidateStopForThisTick' is the raw calculated stop for this tick before applying overall trailing.
            this.stop = applyTrailingIncrement(this.stop, candidateStopForThisTick, pos.isLong());

            return this.stop;
        }
    }

    // ================= WRAPPERS =====================
    private static final class ActivationWrapper implements StopStrategy {
        private final StopStrategy inner;
        private final int activationBars;
        private final double activationMove;
        private int bars = 0;
        private double maxFavorable = 0.0;

        public ActivationWrapper(StopStrategy inner, int activationBars, double activationMove, Position pos) {
            this.inner = inner;
            this.activationBars = activationBars;
            this.activationMove = activationMove;
        }

        public boolean isTrailing() {
            return inner.isTrailing();
        }

        public double current() {
            return inner.current();
        }

        public double update(Position pos, CandleBar bar) {
            bars++;
            double move = pos.isLong() ? bar.high - pos.entry : pos.entry - bar.low;
            if (move > maxFavorable) {
                maxFavorable = move;
            }
            boolean currentActivationStatus = false;
            if ((activationBars > 0 && bars >= activationBars) ||
                    (activationMove > 0 && maxFavorable >= activationMove)) {
                currentActivationStatus = true;
            }
            return currentActivationStatus ? inner.update(pos, bar) : Double.NaN;
        }
    }

    private static final class BreakEvenWrapper implements StopStrategy {
        private final StopStrategy inner;
        private final double trigger;
        private final double buffer;
        private boolean triggered = false;
        private double breakEvenStop = Double.NaN;

        public BreakEvenWrapper(StopStrategy inner, double trigger, double buffer, Position pos) {
            this.inner = inner;
            this.trigger = trigger;
            this.buffer = buffer;
        }

        public boolean isTrailing() {
            return inner.isTrailing();
        }

        public double current() {
            if (triggered) {
                return breakEvenStop;
            }
            return inner.current();
        }

        public double update(Position pos, CandleBar bar) {
            double move = pos.isLong() ? bar.high - pos.entry : pos.entry - bar.low;
            if (!triggered && move >= trigger) {
                breakEvenStop = pos.isLong() ? pos.entry + buffer : pos.entry - buffer;
                triggered = true;
            }
            double innerStop = inner.update(pos, bar);
            if (triggered) {
                if (pos.isLong()) {
                    return Math.max(breakEvenStop, innerStop);
                } else {
                    return Math.min(breakEvenStop, innerStop);
                }
            } else {
                return innerStop;
            }
        }
    }

    private static final class TimeStopStrategy implements StopStrategy {
        private final int maxBars;
        private int bars = 0;
        private final boolean isLong;
        private final double entry;
        private double stop = Double.NaN;

        public TimeStopStrategy(int maxBars, Position pos) {
            this.maxBars = maxBars;
            this.isLong = pos.isLong();
            this.entry = pos.entry;
        }

        public boolean isTrailing() {
            return false;
        }

        public double current() {
            return stop;
        }

        public double update(Position pos, CandleBar bar) {
            bars++;
            if (bars >= maxBars) {
                // Set stop to a value that will always be triggered
                stop = isLong ? Double.POSITIVE_INFINITY : Double.NEGATIVE_INFINITY;
            }
            return stop;
        }
    }

    /** -------------------------------------------------------------
     *  Give-Back Stop (Profit-Based)
     *  • Calculates a stop price based on giving back a percentage (`pct`) of the unrealized profit.
     *  • Activation: Only outputs a stop if the unrealized profit from `entryPrice` to the current `extreme` favorable price
     *    is greater than or equal to `minProfitThreshold` (an absolute price amount, e.g., 10 ticks).
     *    If this condition is not met, it returns `Double.NaN` (no indicator line).
     *  • Calculation (if active):
     *    - For longs, if profit is (extremeHigh – entryPrice), the stop is:
     *      `extremeHigh – (profit × pct)` which is `extremePrice * (1 – pct) + entryPrice * pct`.
     *    - For shorts, if profit is (entryPrice – extremeLow), the stop is:
     *      `extremeLow + (profit × pct)` which is `extremePrice * (1 – pct) + entryPrice * pct`.
     *  • `extremeHigh` is the highest high since position start/flip (for longs).
     *  • `extremeLow` is the lowest low since position start/flip (for shorts).
     *  • `pct` is the percentage of profit to give back (e.g., 0.5 for 50%).
     *  • Does not trail independently and is not tighten-only by default.
     * ------------------------------------------------------------ */
    /* ---- NEW Giveback Strategy with minProfitThreshold and internal state ----
     *  • Giveback percentage `pct` (e.g., 0.2 for 20%) of profit from `extreme`.
     *  • `minProfitThreshold` (absolute amount) must be met for activation.
     *  • Resets `extreme` and `stop` on position flip.
     *  • Inherently trails favorable price movements by recalculating from 'extreme'.
     *  • Ensures stop only tightens (and optionally moves in discrete increments) via AbstractStrategy.applyTrailingIncrement.
     * ------------------------------------------------------------ */
    private static final class GivebackStrategy extends AbstractStrategy {
        private final double pct;            // fraction, e.g., 0.02 for 2%
        private final double minProfitThreshold; // Absolute profit amount required to activate
        private double extreme = Double.NaN; // peak high or trough low

        GivebackStrategy(double pct, double inc, double minProfitThreshold) {
            this.pct = Math.max(0, Math.min(1.0, pct)); // Ensure pct is between 0 and 1
            this.trailingIncrement = inc;    // Kept for signature, but not used by this strategy's core logic
            this.minProfitThreshold = Math.max(0, minProfitThreshold); // Ensure threshold is not negative
        }

        public boolean isTrailing() { return true; }

        public double update(Position pos, CandleBar bar) {
            if (pos == null || bar == null) {
                extreme = Double.NaN;
                stop = Double.NaN;
                return Double.NaN;
            }

            // Detect position flip to reset strategy state (extreme and stop)
            if (previousPositionSide == null || previousPositionSide != pos.side) {
                extreme = Double.NaN; 
                stop = Double.NaN;    
                this.previousPositionSide = pos.side; // Update the side for the next bar
            }

            // Initialize or update extreme price
            if (Double.isNaN(extreme)) {
                extreme = pos.isLong() ? bar.high : bar.low;
            } else {
                extreme = pos.isLong()
                          ? Math.max(extreme, bar.high)
                          : Math.min(extreme, bar.low);
            }

            // Calculate unrealized profit at the extreme price point
            double currentUnrealizedProfitAtExtreme;
            if (pos.isLong()) {
                currentUnrealizedProfitAtExtreme = extreme - pos.entry;
            } else { // Short position
                currentUnrealizedProfitAtExtreme = pos.entry - extreme;
            }

            // Check if profit meets the minimum threshold for activation
            if (currentUnrealizedProfitAtExtreme < this.minProfitThreshold) {
                this.stop = Double.NaN; // Set internal stop to NaN, so current() also reflects no output
                return Double.NaN;      // Return NaN to indicate no output line
            }

            // If threshold met, calculate the giveback stop price
            // Formula: extreme * (1.0 - this.pct) + pos.entry * this.pct
            double candidate = extreme * (1.0 - this.pct) + pos.entry * this.pct;

            // Use applyTrailingIncrement to ensure the stop never loosens
            this.stop = applyTrailingIncrement(this.stop, candidate, pos.isLong());

            return this.stop;
        }
    }

    /**
    * Combined Giveback and MAE stop strategy.
    * Uses "tightest" logic between an MAE stop and a Giveback stop.
    * The Giveback component is only considered if the position is in profit by at least `minProfitForGivebackOffset` (in price units).
    * The overall trailing behavior (e.g., `trailingIncrement`) is taken from the parent `DOUBLE_STOP_GIVEBACK_MAE` spec.
    *
    * Example Usage in `TrailingStopControllerTestIndicator.initialize()`:
    * <pre>{@code
    * // Define the Giveback component
    * StopSpec givebackChildSpec = new StopSpec.Builder(Type.GIVEBACK)
    *     .giveback(5.0) // 5% giveback
    *     .trailingIncrement(1.0 * this.pips) // Optional: trailing for giveback part
    *     .build();
    *
    * // Define the MAE component
    * StopSpec maeChildSpec = new StopSpec.Builder(Type.MAE)
    *     .mae(20.0 * this.pips) // MAE offset of 20 pips
    *     // MAE is typically not .trailing(true) in its own spec, its nature is fixed once armed.
    *     .build();
    *
    * // Define the Double Stop (Giveback + MAE)
    * // Assuming specBuilder is for Type.DOUBLE_STOP_GIVEBACK_MAE
    * specBuilder.doubleStop(givebackChildSpec, maeChildSpec, 10.0 * this.pips) // Giveback active if 10 pips in profit
    *            .trailing(true) // Overall double-stop can trail
    *            .trailingIncrement(0.5 * this.pips); // Trailing increment for the combined result
    * // StopSpec finalDoubleStopSpec = specBuilder.build();
    * }</pre>
    */
    private static final class DoubleStopGivebackMaeStrategy extends AbstractStrategy {
        private final StopStrategy givebackStrategy;
        private final StopStrategy maeStrategy;
        private final double minProfitOffset;
        private boolean hasActivated = false;  // Track if stop has ever been activated

        DoubleStopGivebackMaeStrategy(StopSpec parentSpec, Position pos) {
            if (parentSpec.type != Type.DOUBLE_STOP_GIVEBACK_MAE || parentSpec.childA == null || parentSpec.childB == null) {
                throw new IllegalArgumentException("Invalid spec for DoubleStopGivebackMaeStrategy");
            }
            if (parentSpec.childA.type != Type.GIVEBACK) {
                throw new IllegalArgumentException("Child A for DoubleStopGivebackMae must be of Type.GIVEBACK");
            }
            if (parentSpec.childB.type != Type.MAE) {
                throw new IllegalArgumentException("Child B for DoubleStopGivebackMae must be of Type.MAE");
            }
            this.givebackStrategy = factory(parentSpec.childA, pos); // Use factory to get potentially cached/wrapped instances
            this.maeStrategy = factory(parentSpec.childB, pos);      // Use factory for MAE strategy
            this.minProfitOffset = parentSpec.minProfitForGivebackOffset;
            this.trailingIncrement = parentSpec.trailingIncrement; // Inherit trailingIncrement from the parent DOUBLE_STOP_GIVEBACK_MAE spec
        }

        @Override
        public boolean isTrailing() {
            // The overall strategy is considered trailing if either of its components is, or if it has its own trailingIncrement.
            // For simplicity, let's assume it's trailing if the parent spec says so, or if it has a trailing increment.
            return this.trailingIncrement > 0 || (givebackStrategy.isTrailing() || maeStrategy.isTrailing());
        }

        @Override
        public double update(Position pos, CandleBar bar) {
            if (pos == null || bar == null) return Double.NaN;

            // Detect position flip to reset state
            if (previousPositionSide == null || previousPositionSide != pos.side) {
                this.stop = Double.NaN;           // Reset inherited stop field
                this.hasActivated = false;       // Reset activation state
                this.previousPositionSide = pos.side; // Update the side being tracked
            }

            double maeStopValue = maeStrategy.update(pos, bar);
            double givebackStopValue = givebackStrategy.update(pos, bar);

            boolean isGivebackActive = false;
            if (!Double.isNaN(givebackStopValue)) {
                // Only check min profit offset if we haven't activated yet
                if (!hasActivated) {
                    double currentProfit = 0;
                    if (pos.isLong()) {
                        currentProfit = bar.close - pos.entry; // Or bar.high for more aggressive activation
                    } else {
                        currentProfit = pos.entry - bar.close; // Or pos.entry - bar.low
                    }
                    if (currentProfit >= this.minProfitOffset) {
                        isGivebackActive = true;
                        hasActivated = true;  // Mark as activated once we meet the condition
                    }
                } else {
                    // Once activated, always consider giveback active
                    isGivebackActive = true;
                }
            }

            double candidateStop;
            if (isGivebackActive && !Double.isNaN(maeStopValue)) {
                // Both active → take tightest
                candidateStop = pos.isLong()
                    ? Math.max(givebackStopValue, maeStopValue)
                    : Math.min(givebackStopValue, maeStopValue);
            } else if (isGivebackActive) {
                // Only Giveback is active (MAE might be NaN or not met its conditions)
                candidateStop = givebackStopValue;
            } else if (!Double.isNaN(maeStopValue)) {
                candidateStop = maeStopValue;
            } else {
                // Neither is effectively active or yielding a valid stop number
                candidateStop = Double.NaN;
            }

            // Apply overall trailing increment from the DOUBLE_STOP_GIVEBACK_MAE spec
            this.stop = applyTrailingIncrement(this.stop, candidateStop, pos.isLong());
            return this.stop;
        }
    }
}
