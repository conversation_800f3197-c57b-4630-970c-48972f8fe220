package com.bookmap.api.simple.demo.utils.gui;

import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.Polygon;
import java.awt.RenderingHints; // Added for clarity
import java.awt.geom.Ellipse2D;
import java.awt.image.BufferedImage;

// Add import for the Indicator interface
import velox.api.layer1.simplified.Indicator;

/**
 * Factory class for creating various trading indicator icons used in Bookmap.
 * This class provides methods to create arrows, squares, and lines for different
 * trading indicators like execution markers, signals, and time indicators.
 * * ===== ICON PLACEMENT GUIDE =====
 * * When placing icons near price points, follow these principles for intuitive visualization:
 * * 1. DIRECTIONAL ICONS POINT NATURALLY BY THEIR POSITION:
 * - DOWN triangles with Position.ABOVE_OF appear above the price and point DOWN toward it
 * - UP triangles with Position.BELOW_OF appear below the price and point UP toward it
 * - LEFT arrows with Position.RIGHT_OF appear to the right and point LEFT toward the price
 * - RIGHT arrows with Position.LEFT_OF appear to the left and point RIGHT toward the price
 * * 2. POSITIONING IN BOOKMAP:
 * - Position.ABOVE_OF: Places icon visually above the price point (lower price value)
 * - Position.BELOW_OF: Places icon visually below the price point (higher price value)
 * - Position.LEFT_OF: Places icon to the left of the time point (earlier time)
 * - Position.RIGHT_OF: Places icon to the right of the time point (later time)
 * * 3. PROPER POSITIONING EXAMPLES:
 * ```
 * // For a downward pointing triangle (showing downtrend from upper band)
 * IconFactory.addIconWithAutoClearance(
 * indicator,
 * IconFactory.ShapeType.DOWN_TRIANGLE, // Triangle pointing DOWN
 * Color.RED,
 * IconFactory.IconSize.SMALL,
 * IconFactory.Position.ABOVE_OF,       // Place ABOVE the reference price
 * price);                              // The icon will point toward this price
 * * // For an upward pointing triangle (showing uptrend from lower band)
 * IconFactory.addIconWithAutoClearance(
 * indicator,
 * IconFactory.ShapeType.UP_TRIANGLE,   // Triangle pointing UP
 * Color.GREEN,
 * IconFactory.IconSize.SMALL,
 * IconFactory.Position.BELOW_OF,       // Place BELOW the reference price
 * price);                              // The icon will point toward this price
 * ```
 * * 3. BEST PRACTICES:
 * - Use the `addIconWithAutoClearance` method when possible to automatically
 * calculate the proper distance from data points
 * - Use the enum-based shape methods (ShapeType, IconSize, Position) for cleaner code
 * rather than direct BufferedImage creation
 * - For icons that don't inherently have direction (circles, squares), consider
 * where they'll be least likely to obscure important data
 *
 * 4. SHAPE SELECTION:
 * - Use triangles to mark direction-sensitive events (crossings, breakouts)
 * - Use circles/diamonds for points of interest without strong directional bias
 * - Use squares for status indicators or labels
 * - Use crosses for cancellations or invalidations
 */
public class IconFactory {
    // Constants for visual styling
    private static final int STROKE_WIDTH = 5;
    private static final int THIN_STROKE_WIDTH = 2;
    private static final float BORDER_STROKE_WIDTH = 1.5f; // For consistent borders on shapes
    private static final int ARROW_OFFSET = 3;
    private static final int ARROW_EXTENSION = 20;
    private static final String DEFAULT_FONT_NAME = "SansSerif"; // Renamed for clarity
    
    // Text positioning constants
    private static final double TEXT_X_RATIO = 0.2; // width / 5
    private static final double TEXT_Y_RATIO = 0.9; // (9 * height) / 10
    private static final double SQUARE_TEXT_Y_RATIO = 0.7; // 7 * height / 10
    private static final double FONT_SIZE_RATIO = 1.5; // For arrow text
    private static final int MIN_FONT_SIZE = 10; // Minimum font size for general text
    private static final double GENERAL_TEXT_FONT_SIZE_RATIO = 0.25; // height / 4 for general text

    // Default colors
    private static final Color DEFAULT_TEXT_COLOR = Color.YELLOW;
    private static final Color DEFAULT_BORDER_COLOR = Color.WHITE; // General border color
    private static final Color SQUARE_BORDER_COLOR = Color.WHITE; // Kept for specific square if needed, else unify
    private static final Color SQUARE_TEXT_COLOR = Color.BLACK;
    
    // Trading specific colors
    private static final Color BULLISH_COLOR = new Color(0, 180, 0); // Deeper green
    private static final Color BEARISH_COLOR = new Color(220, 0, 0); // Deeper red
    private static final Color NEUTRAL_COLOR = new Color(220, 180, 0); // Amber
    private static final Color CLOSE_COLOR = new Color(200, 0, 200); // Magenta
    private static final Color ALERT_COLOR = new Color(255, 140, 0); // Orange
    
    // New bright colors for unfilled triangles
    private static final Color BRIGHT_GREEN = new Color(0, 255, 0); // Bright green for upward unfilled triangle
    private static final Color BRIGHT_RED = new Color(255, 0, 0);   // Bright red for downward unfilled triangle

    // Colors for trade and order icons
    private static final Color TRADE_LONG_ENTRY_COLOR = new Color(0, 180, 0);
    private static final Color TRADE_SHORT_ENTRY_COLOR = new Color(220, 0, 0);
    private static final Color TRADE_LONG_EXIT_COLOR = new Color(0, 255, 0);
    private static final Color TRADE_SHORT_EXIT_COLOR = new Color(255, 0, 0);
    private static final Color CANCELLED_ORDER_LONG_COLOR = new Color(100, 180, 100);
    private static final Color CANCELLED_ORDER_SHORT_COLOR = new Color(180, 100, 100);
    private static final Color DEFAULT_LEFT_RIGHT_TRIANGLE_COLOR = new Color(30, 144, 255); // Dodger Blue
    
    // Shape constants
    private static final int PADDING = 4;
    private static final double TRIANGLE_WIDTH_RATIO = 0.85;
    
    // Default minimum clearance from reference point in pixels
    private static final int DEFAULT_MIN_CLEARANCE = 2;
    
    public enum IconSize { 
        VERY_SMALL(12), SMALL(16), MEDIUM(20), LARGE(24), VERY_LARGE(30);        
        private final int size;
        IconSize(int size) { this.size = size; }
        public int getSize() { return size; }
    }
    
    public enum ShapeType { 
        ARROW, UP_TRIANGLE, DOWN_TRIANGLE, SIDEWAYS_TRIANGLES, 
        DIAMOND, CROSS, CIRCLE, CHECKMARK, FLAG, SQUARE,
        LEFT_TRIANGLE, RIGHT_TRIANGLE, REJECTED
    }
    
    public enum Position { 
        ABOVE_OF(0, 1), BELOW_OF(0, -1), LEFT_OF(-1, 0), RIGHT_OF(1, 0), CENTER_OF(0, 0),
        TOP_LEFT_DIAG_OF(-1, 1), TOP_RIGHT_DIAG_OF(1, 1),
        BOTTOM_LEFT_DIAG_OF(-1, -1), BOTTOM_RIGHT_DIAG_OF(1, -1);
        private final int xDir, yDir;
        Position(int xDir, int yDir) { this.xDir = xDir; this.yDir = yDir; }
        public int getXDir() { return xDir; }
        public int getYDir() { return yDir; }
    }

    private static void validateDimensions(int width, int height) {
        if (width <= 0 || height <= 0) {
            throw new IllegalArgumentException("Icon dimensions must be positive: width=" + width + ", height=" + height);
        }
    }
    
    private static void checkNotNull(Object obj, String message) {
        if (obj == null) {
            throw new IllegalArgumentException(message);
        }
    }

    // --- Start of Renamed/Deprecated Arrow Methods ---
    /**
     * @deprecated Use {@link #makePrimarySecondaryColorArrow(boolean, int, int)} or preferably directional shapes.
     */
    @Deprecated
    public static BufferedImage makeRandomArrow(boolean isBid, int width, int height) {
        return makePrimarySecondaryColorArrow(isBid, width, height);
    }

    /**
     * @deprecated Use {@link #makePrimarySecondaryColorArrow(boolean, int, int, String)} or preferably directional shapes with text.
     */
    @Deprecated
    public static BufferedImage makeRandomArrow(boolean isBid, int width, int height, String text) {
        return makePrimarySecondaryColorArrow(isBid, width, height, text);
    }

    /**
     * @deprecated Use {@link #makeStandardCyanMagentaArrow(boolean, int, int)} or preferably directional shapes.
     */
    @Deprecated
    public static BufferedImage makeRandomArrow2(boolean isBid, int width, int height) {
        return makeStandardCyanMagentaArrow(isBid, width, height);
    }

    /**
     * @deprecated Use {@link #makeStandardCyanMagentaArrow(boolean, int, int, String)} or preferably directional shapes with text.
     */
    @Deprecated
    public static BufferedImage makeRandomArrow2(boolean isBid, int width, int height, String text) {
        return makeStandardCyanMagentaArrow(isBid, width, height, text);
    }

    /**
     * @deprecated Use {@link #makeCustomColorArrow(boolean, int, int, Color, Color)} or preferably directional shapes.
     */
    @Deprecated
    public static BufferedImage makeRandomArrow3(boolean isBid, int width, int height, Color color1, Color color2) {
        return makeCustomColorArrow(isBid, width, height, color1, color2);
    }

    /**
     * @deprecated Use {@link #makeCustomColorArrow(boolean, int, int, Color, Color, String)} or preferably directional shapes with text.
     */
    @Deprecated
    public static BufferedImage makeRandomArrow3(boolean isBid, int width, int height, Color color1, Color color2, String text) {
        return makeCustomColorArrow(isBid, width, height, color1, color2, text);
    }
    // --- End of Renamed/Deprecated Arrow Methods ---

    /**
     * Creates a basic diagonal arrow with green/red coloring for bid/ask indicators.
     * This arrow is diagonal and non-standard. Consider using specific directional shapes like triangles.
     */
    public static BufferedImage makePrimarySecondaryColorArrow(boolean isBid, int width, int height) {
        validateDimensions(width, height);
        return createArrow(isBid, width, height, Color.GREEN, Color.RED);
    }

    /**
     * Creates a diagonal arrow with green/red coloring and text label.
     * This arrow is diagonal and non-standard. Consider using specific directional shapes with text.
     */
    public static BufferedImage makePrimarySecondaryColorArrow(boolean isBid, int width, int height, String text) {
        validateDimensions(width, height);
        return createArrowWithText(isBid, width, height, Color.GREEN, Color.RED, text);
    }

    /**
     * Creates a diagonal arrow with cyan/magenta coloring for secondary indicators.
     * This arrow is diagonal and non-standard. Consider using specific directional shapes.
     */
    public static BufferedImage makeStandardCyanMagentaArrow(boolean isBid, int width, int height) {
        validateDimensions(width, height);
        return createArrow(isBid, width, height, Color.CYAN, Color.MAGENTA);
    }

    /**
     * Creates a diagonal arrow with cyan/magenta coloring and text label.
     * This arrow is diagonal and non-standard. Consider using specific directional shapes with text.
     */
    public static BufferedImage makeStandardCyanMagentaArrow(boolean isBid, int width, int height, String text) {
        validateDimensions(width, height);
        return createArrowWithText(isBid, width, height, Color.CYAN, Color.MAGENTA, text);
    }

    /**
     * Creates a diagonal arrow with custom colors for flexible indicator styling.
     * This arrow is diagonal and non-standard. Consider using specific directional shapes.
     */
    public static BufferedImage makeCustomColorArrow(boolean isBid, int width, int height, Color color1, Color color2) {
        validateDimensions(width, height);
        checkNotNull(color1, "Color1 cannot be null.");
        checkNotNull(color2, "Color2 cannot be null.");
        return createArrow(isBid, width, height, color1, color2);
    }

    /**
     * Creates a diagonal arrow with custom colors and text label.
     * This arrow is diagonal and non-standard. Consider using specific directional shapes with text.
     */
    public static BufferedImage makeCustomColorArrow(boolean isBid, int width, int height, Color color1, Color color2, String text) {
        validateDimensions(width, height);
        checkNotNull(color1, "Color1 cannot be null.");
        checkNotNull(color2, "Color2 cannot be null.");
        return createArrowWithText(isBid, width, height, color1, color2, text);
    }

    public static BufferedImage makeSquare(boolean isBid, int width, int height, Color color1, Color color2, String longShortSignalNo) {
        validateDimensions(width, height);
        checkNotNull(color1, "Color1 cannot be null.");
        checkNotNull(color2, "Color2 cannot be null.");
        BufferedImage icon = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D graphics = icon.createGraphics();
        
        try {
            drawSquareWithText(graphics, isBid, width, height, color1, color2, longShortSignalNo);
            return icon;
        } finally {
            graphics.dispose();
        }
    }

    public static BufferedImage OR_lines1(int width, int height) {
        validateDimensions(width, height); 
        return createHorizontalLine(width, height, Color.GREEN, STROKE_WIDTH);
    }

    public static BufferedImage OR_lines2(int width, int height) {
        return OR_lines1(width, height); 
    }

    public static BufferedImage time_vertical_lines1(int width, int length) {
        validateDimensions(width, length); 
        return createVerticalLine(width, length, Color.GRAY, THIN_STROKE_WIDTH);
    }

    private static BufferedImage createArrow(boolean isBid, int width, int height, Color color1, Color color2) {
        BufferedImage icon = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D graphics = icon.createGraphics();
        
        try {
            graphics.setColor(isBid ? color1 : color2);
            graphics.setStroke(new BasicStroke(STROKE_WIDTH));
            drawArrow(graphics, width, height);
            return icon;
        } finally {
            graphics.dispose();
        }
    }

    private static BufferedImage createArrowWithText(boolean isBid, int width, int height, Color color1, Color color2, String text) {
        BufferedImage icon = createArrow(isBid, width, height, color1, color2); 
        Graphics2D graphics = icon.createGraphics(); 
        
        try {
            graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            int fontSize = (int)Math.round(height * FONT_SIZE_RATIO);
            graphics.setFont(new Font(DEFAULT_FONT_NAME, Font.BOLD, fontSize));
            graphics.setColor(DEFAULT_TEXT_COLOR); 
            graphics.drawString(text, 
                (int)(width * TEXT_X_RATIO), 
                (int)(height * TEXT_Y_RATIO));
            return icon;
        } finally {
            graphics.dispose();
        }
    }

    private static void drawSquareWithText(Graphics2D graphics, boolean isBid, int width, int height, 
            Color color1, Color color2, String text) {
        graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        graphics.setColor(isBid ? color1 : color2);
        graphics.fillRect(ARROW_OFFSET, ARROW_OFFSET, width - ARROW_OFFSET, height - ARROW_OFFSET); 
        
        graphics.setColor(SQUARE_BORDER_COLOR); 
        graphics.setStroke(new BasicStroke(BORDER_STROKE_WIDTH)); 
        graphics.drawRect(0, 0, width - 1, height - 1); 
        
        graphics.setColor(SQUARE_TEXT_COLOR); 
        int fontSize = Math.max(MIN_FONT_SIZE, (int)(height * GENERAL_TEXT_FONT_SIZE_RATIO * 0.8)); 
        graphics.setFont(new Font(DEFAULT_FONT_NAME, Font.BOLD, fontSize));

        int textWidth = graphics.getFontMetrics().stringWidth(text);
        int x = (width - textWidth) / 2; 
        graphics.drawString(text, x, (int)(height * SQUARE_TEXT_Y_RATIO));
    }

    private static void drawArrow(Graphics2D graphics, int width, int height) {
        graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        graphics.drawLine(ARROW_OFFSET, ARROW_OFFSET, width, height);
        graphics.drawLine(ARROW_OFFSET, ARROW_OFFSET, ARROW_EXTENSION, ARROW_OFFSET);
        graphics.drawLine(ARROW_OFFSET, ARROW_OFFSET, ARROW_OFFSET, ARROW_EXTENSION);
    }

    private static BufferedImage createHorizontalLine(int width, int height, Color color, int strokeWidth) {
        BufferedImage icon = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D graphics = icon.createGraphics();
        
        try {
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            graphics.setColor(color);
            graphics.setStroke(new BasicStroke(strokeWidth));
            int lineY = Math.max(strokeWidth / 2, height / 2); 
            graphics.drawLine(0, lineY, width, lineY); 
            return icon;
        } finally {
            graphics.dispose();
        }
    }

    private static BufferedImage createVerticalLine(int width, int length, Color color, int strokeWidth) {
        BufferedImage icon = new BufferedImage(width, length, BufferedImage.TYPE_INT_ARGB);
        Graphics2D graphics = icon.createGraphics();
        
        try {
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            graphics.setColor(color);
            graphics.setStroke(new BasicStroke(strokeWidth));
            int lineX = Math.max(strokeWidth / 2, width / 2);
            graphics.drawLine(lineX, 0, lineX, length); 
            return icon;
        } finally {
            graphics.dispose();
        }
    }
    
    public static BufferedImage makeUpTriangle(int width, int height) {
        validateDimensions(width, height);
        return createTriangle(width, height, true, BULLISH_COLOR, null);
    }
    
    public static BufferedImage makeUpTriangle(int width, int height, String text) {
        validateDimensions(width, height);
        return createTriangle(width, height, true, BULLISH_COLOR, text);
    }
    
    public static BufferedImage makeDownTriangle(int width, int height) {
        validateDimensions(width, height);
        return createTriangle(width, height, false, BEARISH_COLOR, null);
    }
    
    public static BufferedImage makeDownTriangle(int width, int height, String text) {
        validateDimensions(width, height);
        return createTriangle(width, height, false, BEARISH_COLOR, text);
    }
    
    public static BufferedImage makeSidewaysTriangles(int width, int height) {
        validateDimensions(width, height);
        return createSidewaysTriangles(width, height, NEUTRAL_COLOR, null);
    }
    
    public static BufferedImage makeSidewaysTriangles(int width, int height, String text) {
        validateDimensions(width, height);
        return createSidewaysTriangles(width, height, NEUTRAL_COLOR, text);
    }
    
    public static BufferedImage makeDiamond(boolean isEntry, int width, int height) {
        validateDimensions(width, height);
        return createDiamond(width, height, isEntry ? BULLISH_COLOR : CLOSE_COLOR, null);
    }
    
    public static BufferedImage makeDiamond(boolean isEntry, int width, int height, String text) {
        validateDimensions(width, height);
        return createDiamond(width, height, isEntry ? BULLISH_COLOR : CLOSE_COLOR, text);
    }
    
    public static BufferedImage makeCross(int width, int height) {
        validateDimensions(width, height);
        return createCross(width, height, CLOSE_COLOR, null);
    }
    
    public static BufferedImage makeCross(int width, int height, String text) {
        validateDimensions(width, height);
        return createCross(width, height, CLOSE_COLOR, text);
    }
    
    public static BufferedImage makeCircle(int width, int height) {
        validateDimensions(width, height);
        return createCircle(width, height, ALERT_COLOR, null);
    }
    
    public static BufferedImage makeCircle(int width, int height, String text) {
        validateDimensions(width, height);
        return createCircle(width, height, ALERT_COLOR, text);
    }
    
    public static BufferedImage makeCheckmark(int width, int height) {
        validateDimensions(width, height);
        return createCheckmark(width, height, BULLISH_COLOR, null);
    }
    
    public static BufferedImage makeCheckmark(int width, int height, String text) {
        validateDimensions(width, height);
        return createCheckmark(width, height, BULLISH_COLOR, text);
    }
    
    public static BufferedImage makeFlag(boolean isBullish, int width, int height) {
        validateDimensions(width, height);
        return createFlag(width, height, isBullish ? BULLISH_COLOR : BEARISH_COLOR, null);
    }
    
    public static BufferedImage makeFlag(boolean isBullish, int width, int height, String text) {
        validateDimensions(width, height);
        return createFlag(width, height, isBullish ? BULLISH_COLOR : BEARISH_COLOR, text);
    }
    
    public static BufferedImage makeUnfilledUpTriangle(int width, int height) {
        validateDimensions(width, height);
        return createUnfilledTriangle(width, height, true, BRIGHT_GREEN, null);
    }
    
    public static BufferedImage makeUnfilledUpTriangle(int width, int height, String text) {
        validateDimensions(width, height);
        return createUnfilledTriangle(width, height, true, BRIGHT_GREEN, text);
    }
    
    public static BufferedImage makeUnfilledDownTriangle(int width, int height) {
        validateDimensions(width, height);
        return createUnfilledTriangle(width, height, false, BRIGHT_RED, null);
    }
    
    public static BufferedImage makeUnfilledDownTriangle(int width, int height, String text) {
        validateDimensions(width, height);
        return createUnfilledTriangle(width, height, false, BRIGHT_RED, text);
    }
    
    public static BufferedImage makeDiamond(boolean isEntry, IconSize iconSize, String text) {
        checkNotNull(iconSize, "IconSize cannot be null.");
        int size = iconSize.getSize();
        return createDiamond(size, size, isEntry ? BULLISH_COLOR : CLOSE_COLOR, text);
    }
    
    public static BufferedImage makeDiamond(boolean isEntry, IconSize iconSize) {
        return makeDiamond(isEntry, iconSize, null);
    }
    
    public static BufferedImage makeUpTriangle(IconSize iconSize, String text) {
        checkNotNull(iconSize, "IconSize cannot be null.");
        int size = iconSize.getSize();
        return createTriangle(size, size, true, BULLISH_COLOR, text);
    }
    
    public static BufferedImage makeUpTriangle(IconSize iconSize) {
        return makeUpTriangle(iconSize, null);
    }
    
    public static BufferedImage makeDownTriangle(IconSize iconSize, String text) {
        checkNotNull(iconSize, "IconSize cannot be null.");
        int size = iconSize.getSize();
        return createTriangle(size, size, false, BEARISH_COLOR, text);
    }
    
    public static BufferedImage makeDownTriangle(IconSize iconSize) {
        return makeDownTriangle(iconSize, null);
    }
    
    private static BufferedImage createIcon(ShapeType shapeType, Color color, int width, int height, String text) {
        switch (shapeType) {
            case ARROW: 
                return createRightTriangle(width, height, color, text); 
            case UP_TRIANGLE:
                return createTriangle(width, height, true, color, text);
            case DOWN_TRIANGLE:
                return createTriangle(width, height, false, color, text);
            case SIDEWAYS_TRIANGLES:
                return createSidewaysTriangles(width, height, color, text);
            case DIAMOND:
                return createDiamond(width, height, color, text);
            case CROSS:
                return createCross(width, height, color, text);
            case CIRCLE:
                return createCircle(width, height, color, text);
            case CHECKMARK:
                return createCheckmark(width, height, color, text);
            case FLAG:
                return createFlag(width, height, color, text);
            case SQUARE:
                return makeSquare(true, width, height, color, color, text != null ? text : "");
            case LEFT_TRIANGLE:
                return createLeftTriangle(width, height, color, text);
            case RIGHT_TRIANGLE:
                return createRightTriangle(width, height, color, text);
            case REJECTED:
                return createRejectedShape(width, height, color, text);
            default:
                return createCircle(width, height, color, text);
        }
    }
    
    public static BufferedImage createShape(ShapeType shapeType, Color color, 
                                           IconSize iconSize, String text) {
        checkNotNull(shapeType, "ShapeType cannot be null.");
        checkNotNull(color, "Color cannot be null.");
        checkNotNull(iconSize, "IconSize cannot be null.");
        int size = iconSize.getSize();
        return createIcon(shapeType, color, size, size, text);
    }
    
    public static BufferedImage createShape(ShapeType shapeType, Color color, IconSize iconSize) {
        return createShape(shapeType, color, iconSize, null);
    }
    
    public static void addIconToIndicator(Indicator indicator, ShapeType shapeType, Color color, 
                                    int width, int height, Position position, 
                                    double x, int offset, String text) {
        checkNotNull(indicator, "Indicator cannot be null.");
        checkNotNull(shapeType, "ShapeType cannot be null.");
        checkNotNull(color, "Color cannot be null.");
        checkNotNull(position, "Position cannot be null.");
        validateDimensions(width, height); 

        BufferedImage icon = createIcon(shapeType, color, width, height, text);
        int xOffset = position.getXDir() * offset;
        int yOffset = position.getYDir() * offset;
        
        indicator.addIcon(x, icon, xOffset, yOffset);
    }
    
    public static void addIconToIndicator(Indicator indicator, ShapeType shapeType, Color color, 
                                   int width, int height, Position position, 
                                   double x, int offset) {
        addIconToIndicator(indicator, shapeType, color, width, height, position, x, offset, null);
    }
    
    public static void addIconToIndicator(Indicator indicator, ShapeType shapeType, Color color, 
                                    IconSize iconSize, Position position, 
                                    double x, int offset, String text) {
        checkNotNull(indicator, "Indicator cannot be null.");
        checkNotNull(shapeType, "ShapeType cannot be null.");
        checkNotNull(color, "Color cannot be null.");
        checkNotNull(iconSize, "IconSize cannot be null.");
        checkNotNull(position, "Position cannot be null.");
        int size = iconSize.getSize();
        addIconToIndicator(indicator, shapeType, color, size, size, position, x, offset, text);
    }
    
    public static void addIconToIndicator(Indicator indicator, ShapeType shapeType, Color color, 
                                   IconSize iconSize, Position position, 
                                   double x, int offset) {
        addIconToIndicator(indicator, shapeType, color, iconSize, position, x, offset, null);
    }
    
    public static void addIconWithAutoClearance(Indicator indicator, ShapeType shapeType, Color color,
                                             IconSize iconSize, Position position, double x,
                                             int minClearance, String text) {
        checkNotNull(indicator, "Indicator cannot be null.");
        checkNotNull(shapeType, "ShapeType cannot be null.");
        checkNotNull(color, "Color cannot be null.");
        checkNotNull(iconSize, "IconSize cannot be null.");
        checkNotNull(position, "Position cannot be null.");
        int size = iconSize.getSize();
        int offset = calculateClearanceOffset(size, position, minClearance);
        
        addIconToIndicator(indicator, shapeType, color, size, size, position, x, offset, text);
    }
    
    public static void addIconWithAutoClearance(Indicator indicator, ShapeType shapeType, Color color,
                                             IconSize iconSize, Position position, double x,
                                             int minClearance) {
        addIconWithAutoClearance(indicator, shapeType, color, iconSize, position, x, minClearance, null);
    }
    
    public static void addIconWithAutoClearance(Indicator indicator, ShapeType shapeType, Color color,
                                             IconSize iconSize, Position position, double x,
                                             String text) {
        addIconWithAutoClearance(indicator, shapeType, color, iconSize, position, x, DEFAULT_MIN_CLEARANCE, text);
    }
    
    public static void addIconWithAutoClearance(Indicator indicator, ShapeType shapeType, Color color,
                                             IconSize iconSize, Position position, double x) {
        addIconWithAutoClearance(indicator, shapeType, color, iconSize, position, x, DEFAULT_MIN_CLEARANCE, null);
    }
    
    private static int calculateClearanceOffset(int size, Position position, int minClearance) {
        int halfSize = size / 2;
        
        int sizeRelatedMinClearance = Math.max(5, size / 5); 
        int effectiveMinClearance = Math.max(minClearance, sizeRelatedMinClearance); 
        
        if (position == Position.TOP_LEFT_DIAG_OF || 
            position == Position.TOP_RIGHT_DIAG_OF ||
            position == Position.BOTTOM_LEFT_DIAG_OF || 
            position == Position.BOTTOM_RIGHT_DIAG_OF) {
            
            int centerToCornerDistance = (int)Math.ceil(halfSize * Math.sqrt(2)); 
            return centerToCornerDistance + effectiveMinClearance;
        }
        
        if (position == Position.CENTER_OF) {
            return 0; 
        }
        
        return halfSize + effectiveMinClearance;
    }
    
    private static BufferedImage createTriangle(int width, int height, boolean isUp, Color color, String text) {
        BufferedImage icon = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = icon.createGraphics();
        
        try {
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            int[] xPoints = new int[3];
            int[] yPoints = new int[3];
            int triangleWidth = (int)(width * TRIANGLE_WIDTH_RATIO);
            
            if (isUp) {
                xPoints[0] = width / 2; yPoints[0] = PADDING;
                xPoints[1] = (width - triangleWidth) / 2; yPoints[1] = height - PADDING;
                xPoints[2] = width - (width - triangleWidth) / 2; yPoints[2] = height - PADDING;
            } else {
                xPoints[0] = width / 2; yPoints[0] = height - PADDING;
                xPoints[1] = (width - triangleWidth) / 2; yPoints[1] = PADDING;
                xPoints[2] = width - (width - triangleWidth) / 2; yPoints[2] = PADDING;
            }
            
            Polygon triangle = new Polygon(xPoints, yPoints, 3);
            g.setColor(color);
            g.fill(triangle);
            
            g.setColor(DEFAULT_BORDER_COLOR);
            g.setStroke(new BasicStroke(BORDER_STROKE_WIDTH));
            g.draw(triangle);
            
            if (text != null && !text.isEmpty()) { drawText(g, text, width, height); }
            return icon;
        } finally { g.dispose(); }
    }
    
    private static BufferedImage createSidewaysTriangles(int width, int height, Color color, String text) {
        BufferedImage icon = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = icon.createGraphics();
        try {
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            int[] xLeft = {PADDING, width / 2, PADDING};
            int[] yLeft = {PADDING, height / 2, height - PADDING};
            int[] xRight = {width - PADDING, width / 2, width - PADDING};
            int[] yRight = {PADDING, height / 2, height - PADDING};
            
            Polygon leftTriangle = new Polygon(xLeft, yLeft, 3);
            Polygon rightTriangle = new Polygon(xRight, yRight, 3);
            
            g.setColor(color);
            g.fill(leftTriangle); g.fill(rightTriangle);
            
            g.setColor(DEFAULT_BORDER_COLOR);
            g.setStroke(new BasicStroke(BORDER_STROKE_WIDTH));
            g.draw(leftTriangle); g.draw(rightTriangle);
            
            if (text != null && !text.isEmpty()) { drawText(g, text, width, height); }
            return icon;
        } finally { g.dispose(); }
    }
    
    private static BufferedImage createDiamond(int width, int height, Color color, String text) {
        BufferedImage icon = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = icon.createGraphics();
        try {
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            int[] xPoints = {width / 2, width - PADDING, width / 2, PADDING};
            int[] yPoints = {PADDING, height / 2, height - PADDING, height / 2};
            
            Polygon diamond = new Polygon(xPoints, yPoints, 4);
            g.setColor(color);
            g.fill(diamond);
            
            g.setColor(DEFAULT_BORDER_COLOR);
            g.setStroke(new BasicStroke(BORDER_STROKE_WIDTH));
            g.draw(diamond);
            
            if (text != null && !text.isEmpty()) { drawText(g, text, width, height); }
            return icon;
        } finally { g.dispose(); }
    }
    
    private static BufferedImage createCross(int width, int height, Color color, String text) {
        BufferedImage icon = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = icon.createGraphics();
        try {
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g.setColor(color);
            g.setStroke(new BasicStroke(THIN_STROKE_WIDTH, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND)); 
            
            g.drawLine(PADDING, PADDING, width - PADDING, height - PADDING);
            g.drawLine(width - PADDING, PADDING, PADDING, height - PADDING);
            
            if (text != null && !text.isEmpty()) { drawText(g, text, width, height); } 
            return icon;
        } finally { g.dispose(); }
    }
    
    private static BufferedImage createCircle(int width, int height, Color color, String text) {
        BufferedImage icon = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = icon.createGraphics();
        try {
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            int diameter = Math.min(width, height) - PADDING * 2;
            int x = (width - diameter) / 2;
            int y = (height - diameter) / 2;
            
            Ellipse2D.Double circle = new Ellipse2D.Double(x, y, diameter, diameter);
            g.setColor(color);
            g.fill(circle);
            
            g.setColor(DEFAULT_BORDER_COLOR);
            g.setStroke(new BasicStroke(BORDER_STROKE_WIDTH));
            g.draw(circle);
            
            if (text != null && !text.isEmpty()) { drawText(g, text, width, height); }
            return icon;
        } finally { g.dispose(); }
    }
    
    private static BufferedImage createCheckmark(int width, int height, Color color, String text) {
        BufferedImage icon = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = icon.createGraphics();
        try {
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g.setColor(color);
            g.setStroke(new BasicStroke(THIN_STROKE_WIDTH, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND)); 
            
            int[] xPoints = {width / 4, width / 2, width - PADDING}; 
            int[] yPoints = {height / 2, height - (height / 3), PADDING}; 
            g.drawPolyline(xPoints, yPoints, 3);
            
            if (text != null && !text.isEmpty()) { drawText(g, text, width, height); } 
            return icon;
        } finally { g.dispose(); }
    }
    
    private static BufferedImage createFlag(int width, int height, Color color, String text) {
        BufferedImage icon = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = icon.createGraphics();
        try {
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g.setColor(DEFAULT_BORDER_COLOR); 
            g.setStroke(new BasicStroke(THIN_STROKE_WIDTH));
            g.drawLine(PADDING * 2, PADDING, PADDING * 2, height - PADDING);
            
            int[] xPoints = {PADDING * 2, width - PADDING, width - PADDING, PADDING * 2};
            int[] yPoints = {PADDING, PADDING + (height / 5), height / 2, height / 3};
            Polygon flagShape = new Polygon(xPoints, yPoints, 4); 
            
            g.setColor(color);
            g.fill(flagShape);
            g.setColor(DEFAULT_BORDER_COLOR);
            g.setStroke(new BasicStroke(BORDER_STROKE_WIDTH)); 
            g.draw(flagShape);
            
            if (text != null && !text.isEmpty()) {
                g.setColor(DEFAULT_TEXT_COLOR);
                int fontSize = Math.max(MIN_FONT_SIZE - 2, (int)(height * GENERAL_TEXT_FONT_SIZE_RATIO * 0.7)); 
                g.setFont(new Font(DEFAULT_FONT_NAME, Font.BOLD, fontSize));
                int textWidth = g.getFontMetrics().stringWidth(text);
                g.drawString(text, PADDING * 2 + (width - PADDING - PADDING * 2 - textWidth) / 2, height - PADDING); 
            }
            return icon;
        } finally { g.dispose(); }
    }
    
    private static void drawText(Graphics2D g, String text, int width, int height) {
        g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g.setColor(DEFAULT_TEXT_COLOR);
        int fontSize = Math.max(MIN_FONT_SIZE, (int)(height * GENERAL_TEXT_FONT_SIZE_RATIO)); 
        g.setFont(new Font(DEFAULT_FONT_NAME, Font.BOLD, fontSize));
        
        int textWidth = g.getFontMetrics().stringWidth(text);
        int x = (width - textWidth) / 2; 
        int y = height - PADDING - g.getFontMetrics().getDescent(); 
        
        g.drawString(text, x, y);
    }
    
    private static BufferedImage createUnfilledTriangle(int width, int height, boolean isUp, Color color, String text) {
        BufferedImage icon = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = icon.createGraphics();
        try {
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            int[] xPoints = new int[3];
            int[] yPoints = new int[3];
            int triangleWidth = (int)(width * TRIANGLE_WIDTH_RATIO);
            
            if (isUp) {
                xPoints[0] = width / 2; yPoints[0] = PADDING;
                xPoints[1] = (width - triangleWidth) / 2; yPoints[1] = height - PADDING;
                xPoints[2] = width - (width - triangleWidth) / 2; yPoints[2] = height - PADDING;
            } else {
                xPoints[0] = width / 2; yPoints[0] = height - PADDING;
                xPoints[1] = (width - triangleWidth) / 2; yPoints[1] = PADDING;
                xPoints[2] = width - (width - triangleWidth) / 2; yPoints[2] = PADDING;
            }
            
            Polygon triangle = new Polygon(xPoints, yPoints, 3);
            g.setStroke(new BasicStroke(THIN_STROKE_WIDTH + 1)); 
            g.setColor(color);
            g.draw(triangle);
            
            if (text != null && !text.isEmpty()) { drawText(g, text, width, height); }
            return icon;
        } finally { g.dispose(); }
    }

    private static BufferedImage createLeftTriangle(int width, int height, Color color, String text) {
        BufferedImage icon = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = icon.createGraphics();
        try {
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            int[] xPoints = {PADDING, width - PADDING, width - PADDING};
            int[] yPoints = {height / 2, PADDING, height - PADDING};
            
            Polygon triangle = new Polygon(xPoints, yPoints, 3);
            g.setColor(color);
            g.fill(triangle);
            
            g.setColor(DEFAULT_BORDER_COLOR);
            g.setStroke(new BasicStroke(BORDER_STROKE_WIDTH));
            g.draw(triangle);
            
            if (text != null && !text.isEmpty()) { drawText(g, text, width, height); }
            return icon;
        } finally { g.dispose(); }
    }
    
    private static BufferedImage createRightTriangle(int width, int height, Color color, String text) {
        BufferedImage icon = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = icon.createGraphics();
        try {
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            int[] xPoints = {width - PADDING, PADDING, PADDING};
            int[] yPoints = {height / 2, PADDING, height - PADDING};
            
            Polygon triangle = new Polygon(xPoints, yPoints, 3);
            g.setColor(color);
            g.fill(triangle);
            
            g.setColor(DEFAULT_BORDER_COLOR);
            g.setStroke(new BasicStroke(BORDER_STROKE_WIDTH));
            g.draw(triangle);
            
            if (text != null && !text.isEmpty()) { drawText(g, text, width, height); }
            return icon;
        } finally { g.dispose(); }
    }

    private static BufferedImage createRejectedShape(int width, int height, Color color, String text) {
        BufferedImage icon = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D graphics = icon.createGraphics();
        try {
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            int padding = width / 5; 
            int circleSize = width - 2 * padding;
            
            graphics.setColor(color);
            graphics.setStroke(new BasicStroke(THIN_STROKE_WIDTH)); 
            graphics.draw(new Ellipse2D.Double(padding, padding, circleSize, circleSize));

            double angle = Math.PI / 4; 
            int centerX = width / 2;
            int centerY = height / 2;
            double radius = circleSize / 2.0;
            
            int x1 = (int) (centerX - radius * Math.cos(angle));
            int y1 = (int) (centerY - radius * Math.sin(angle));
            int x2 = (int) (centerX + radius * Math.cos(angle));
            int y2 = (int) (centerY + radius * Math.sin(angle));
            graphics.drawLine(x1, y1, x2, y2); 
            
            if (text != null && !text.isEmpty()) { drawText(graphics, text, width, height); }
            return icon;
        } finally { graphics.dispose(); }
    }

    public static BufferedImage makeRejectedIcon(IconSize iconSize, Color color) {
        checkNotNull(iconSize, "IconSize cannot be null.");
        checkNotNull(color, "Color cannot be null.");
        int size = iconSize.getSize();
        return createRejectedShape(size, size, color, null);
    }

    public static BufferedImage makeRejectedIcon(IconSize iconSize, Color color, String text) {
        checkNotNull(iconSize, "IconSize cannot be null.");
        checkNotNull(color, "Color cannot be null.");
        int size = iconSize.getSize();
        return createRejectedShape(size, size, color, text);
    }

    public static BufferedImage makeRejectedIcon(int width, int height, Color color) {
        validateDimensions(width, height);
        checkNotNull(color, "Color cannot be null.");
        return createRejectedShape(width, height, color, null);
    }

    public static BufferedImage makeRejectedIcon(int width, int height, Color color, String text) {
        validateDimensions(width, height);
        checkNotNull(color, "Color cannot be null.");
        return createRejectedShape(width, height, color, text);
    }

    public static void addDefaultDownIcon(Indicator indicator, double value) {
        checkNotNull(indicator, "Indicator cannot be null.");
        addIconWithAutoClearance(indicator, ShapeType.DOWN_TRIANGLE, BEARISH_COLOR, 
            IconSize.SMALL, Position.ABOVE_OF, value);
    }
    
    public static void addDefaultUpIcon(Indicator indicator, double value) {
        checkNotNull(indicator, "Indicator cannot be null.");
        addIconWithAutoClearance(indicator, ShapeType.UP_TRIANGLE, BULLISH_COLOR, 
            IconSize.SMALL, Position.BELOW_OF, value);
    }
    
    public static void addDefaultSidewaysIcon(Indicator indicator, double value) {
        checkNotNull(indicator, "Indicator cannot be null.");
        addIconWithAutoClearance(indicator, ShapeType.SIDEWAYS_TRIANGLES, NEUTRAL_COLOR, 
            IconSize.SMALL, Position.CENTER_OF, value);
    }
    
    public static void addCustomDownIcon(Indicator indicator, double value, Color color) {
        checkNotNull(indicator, "Indicator cannot be null.");
        checkNotNull(color, "Color cannot be null.");
        addIconWithAutoClearance(indicator, ShapeType.DOWN_TRIANGLE, color,
            IconSize.SMALL, Position.ABOVE_OF, value);
    }
    
    public static void addCustomUpIcon(Indicator indicator, double value, Color color) {
        checkNotNull(indicator, "Indicator cannot be null.");
        checkNotNull(color, "Color cannot be null.");
        addIconWithAutoClearance(indicator, ShapeType.UP_TRIANGLE, color,
            IconSize.SMALL, Position.BELOW_OF, value);
    }

    public static void addDefaultLeftIcon(Indicator indicator, double value) {
        checkNotNull(indicator, "Indicator cannot be null.");
        addIconWithAutoClearance(indicator, ShapeType.LEFT_TRIANGLE, DEFAULT_LEFT_RIGHT_TRIANGLE_COLOR,
            IconSize.SMALL, Position.RIGHT_OF, value);
    }
    
    public static void addDefaultRightIcon(Indicator indicator, double value) {
        checkNotNull(indicator, "Indicator cannot be null.");
        addIconWithAutoClearance(indicator, ShapeType.RIGHT_TRIANGLE, DEFAULT_LEFT_RIGHT_TRIANGLE_COLOR,
            IconSize.SMALL, Position.LEFT_OF, value);
    }
    
    public static void addCustomLeftIcon(Indicator indicator, double value, Color color) {
        checkNotNull(indicator, "Indicator cannot be null.");
        checkNotNull(color, "Color cannot be null.");
        addIconWithAutoClearance(indicator, ShapeType.LEFT_TRIANGLE, color,
            IconSize.SMALL, Position.RIGHT_OF, value);
    }
    
    public static void addCustomRightIcon(Indicator indicator, double value, Color color) {
        checkNotNull(indicator, "Indicator cannot be null.");
        checkNotNull(color, "Color cannot be null.");
        addIconWithAutoClearance(indicator, ShapeType.RIGHT_TRIANGLE, color,
            IconSize.SMALL, Position.LEFT_OF, value);
    }

    public static void addTradeIcon(Indicator indicator, double price, boolean isEntry, boolean isLong, boolean debugLogging) {
        checkNotNull(indicator, "Indicator cannot be null.");
        ShapeType shapeType;
        Color iconColor;
        Position position;

        if (isEntry) {
            if (isLong) {
                shapeType = ShapeType.UP_TRIANGLE;
                iconColor = TRADE_LONG_ENTRY_COLOR;
                position = Position.BELOW_OF;
            } else {
                shapeType = ShapeType.DOWN_TRIANGLE;
                iconColor = TRADE_SHORT_ENTRY_COLOR;
                position = Position.ABOVE_OF;
            }
        } else { 
            shapeType = ShapeType.CROSS; 
            if (isLong) {
                iconColor = TRADE_LONG_EXIT_COLOR;
            } else {
                iconColor = TRADE_SHORT_EXIT_COLOR;
            }
            position = isLong ? Position.ABOVE_OF : Position.BELOW_OF; 
        }

        addIconWithAutoClearance(indicator, shapeType, iconColor, IconSize.MEDIUM, position, price);

        if (debugLogging) {
            System.out.println("Added trade icon: type=" + (isEntry ? "Entry" : "Exit") +
                    ", direction=" + (isLong ? "Long" : "Short") +
                    ", price=" + price + ", shape=" + shapeType + ", position=" + position);
        }
    }

    public static void addCancelledOrderIcon(Indicator indicator, double price, boolean wasLong, boolean debugLogging) {
        checkNotNull(indicator, "Indicator cannot be null.");
        ShapeType shapeType;
        Color iconColor;
        Position position;

        if (wasLong) { 
            shapeType = ShapeType.RIGHT_TRIANGLE; 
            iconColor = CANCELLED_ORDER_LONG_COLOR;
            position = Position.LEFT_OF; 
        } else { 
            shapeType = ShapeType.LEFT_TRIANGLE; 
            iconColor = CANCELLED_ORDER_SHORT_COLOR;
            position = Position.RIGHT_OF; 
        }

        addIconWithAutoClearance(indicator, shapeType, iconColor, IconSize.SMALL, position, price);

        if (debugLogging) {
            System.out.println("Added cancelled order icon: direction=" + (wasLong ? "Long" : "Short") +
                    ", price=" + price + ", shape=" + shapeType + ", position=" + position);
        }
    }
}