package com.bookmap.api.simple.demo.indicators;

// Added imports for VPASnapshot integration
import java.util.*;
import java.time.Instant;

// Added import for Health Tracker
import com.bookmap.api.simple.demo.indicators.InputDataHealthTrackerV2;
import velox.api.layer1.common.Log; // Import Log if not already present

/**
 * <h2>VolumeProfileAnalyzer – 1‑tick fixed‑bin version (2025‑05‑05)</h2>
 *
 * Volume Profile analyzer that calculates POC, Value Area, and identifies
 * HVN/LVN nodes.
 * Uses standard volume-ordered approach for Value Area calculation.
 * Improved LVN detection by scanning from VAL to VAH and finding local minima.
 * Enhanced HVN detection to ensure we identify the highest volume zones.
 * Added proximity analysis to merge related HVN/LVN nodes.
 * Robust POC detection with HVN cross-validation.
 * Optimized with reusable buffers and collection pooling (2025-05-24).
 * Introduced debug logging helpers (2025-05-24).
 *
 * <p>
 * No instrument metadata is required. Simply feed
 * <code>onTrade(price, size)</code> and
 * call <code>refresh(nowNanos)</code> periodically.
 * </p>
 *
 * <p>
 * <b>Thread‑safety</b>: write‑single / read‑many: {@link #onTrade} and
 * {@link #refresh} should be
 * called from the market‑data thread; getters are wait‑free and safe for other
 * threads.
 * </p>
 */
public final class VolumeProfileAnalyzer {

    /* ===== User‑tunable parameters ===== */
    private final int volAreaPercent; // e.g. 70
    private final int maxNodeCount;
    private static final boolean DEBUG = true; // Set to true to enable debug prints
    private static final int TICK_SIZE = 1; // 1 tick for CME ES
    private static final double RELATIVE_HVN_FLOOR = 0.75; // 75% of max-bin-volume
    private static final long MIN_LVN_VOLUME = 1; // new minimum LVN volume floor
    private static final int LVN_BUFFER_TICKS = 10; // Buffer from VA edges
    private static final int PROXIMITY_THRESHOLD = 15; // Max distance to check for related nodes
    private static final double STRUCTURE_THRESHOLD = 0.6; // Volume change required to indicate separate structures
    private static final long MIN_SESSION_VOLUME_FOR_VALUE_AREA = 1000; // Guard for early sessions
    private static final int HYSTERESIS_BPS = 10; // Basis points for POC hysteresis (e.g., 10 for 0.1%)

    /** Number of ticks on each side when validating HVN nodes */
    private static final int HVN_SCAN_RANGE = 15;
    /** Number of ticks on each side when validating LVN nodes */
    private static final int LVN_SCAN_RANGE = 15;

    /** Draft‑to‑production tuning constants (2025‑05‑06 cleanup) */
    private static final int MIN_NODE_WIDTH = 1; // ticks
    private static final int STABILITY_THRESHOLD = 2; // refreshes before promoting primary LVN

    // New constants for MAD based gates and re-centering
    private static final double HVN_MAD_MULTIPLIER = 2.0; // Multiplier for MAD for HVN gate
    private static final double LVN_MAD_MULTIPLIER = 1.5; // Multiplier for MAD for LVN gate
    private static final int NODE_RECENTER_EXPANSION_TICKS = 2; // Ticks to expand search for true center in
                                                                // consolidation

    /* ===== Fixed bin width ===== */
    private final int binWidth = TICK_SIZE;
    private long totalTrades = 0; // trades seen since session start
    private long lastTradeNanos = 0L;

    /* ===== Live profile ===== */
    private final TickHistogram volAtPrice = new TickHistogram();
    private long sessionVolume = 0;
    private volatile int lastPrice = Integer.MIN_VALUE;

    /* ===== Cached outputs ===== */
    private final Object updateLock = new Object(); // For thread safety
    private volatile int pocPrice = Integer.MIN_VALUE;
    private long pocVol = 0;
    private volatile int developingVAL = 0;
    private volatile int developingVAH = 0;
    private volatile int developingLVN = 0; // new LVN logic
    private int lvnCandidate = Integer.MIN_VALUE;
    private int lvnAge = 0;
    private volatile List<VolumeNode> hvnNodes = Collections.emptyList();
    private volatile List<VolumeNode> lvnNodes = Collections.emptyList();
    private String lastDebugInfo = "";
    private volatile boolean sufficientDataForProfile = false; // Added flag for snapshot readiness
    private volatile long lastRefreshTimestampNanos = 0L; // Added timestamp tracking
    // Health Tracking Fields
    private InputDataHealthTrackerV2 inputDataHealthTracker;
    private boolean healthMonitoringEnabled = true; // Default to enabled

    // In the field declaration section of VolumeProfileAnalyzer
    /* ===== Optimization: Reusable buffers for refresh() method ===== */
    private int[] reusableBins = new int[0];
    private long[] reusableVolumes = new long[0];
    // Changed from List<Long> to ArrayList<Long> to allow trimToSize()
    private final ArrayList<Long> reusableNonZeroVolumes = new ArrayList<>();

    /*
     * ===== Optimization: Collection pools for reducing allocation pressure =====
     */
    private static final class CollectionPool {
        private static final int MAX_POOL_SIZE = 10; // Max number of lists to keep in pool
        private final List<List<VolumeNode>> nodeListPool = new ArrayList<>(MAX_POOL_SIZE);
        private final List<List<Integer>> intListPool = new ArrayList<>(MAX_POOL_SIZE); // Example for integer lists

        List<VolumeNode> acquireNodeList() {
            if (!nodeListPool.isEmpty()) {
                List<VolumeNode> list = nodeListPool.remove(nodeListPool.size() - 1);
                // list.clear(); // Already cleared on release, but good for safety if used
                // elsewhere
                return list;
            }
            return new ArrayList<>();
        }

        void releaseNodeList(List<VolumeNode> list) {
            if (list != null && nodeListPool.size() < MAX_POOL_SIZE) {
                list.clear();
                nodeListPool.add(list);
            }
            // If pool is full, or list is null, it will be GC'd if not referenced elsewhere
        }

        List<Integer> acquireIntList() {
            if (!intListPool.isEmpty()) {
                List<Integer> list = intListPool.remove(intListPool.size() - 1);
                return list;
            }
            return new ArrayList<>();
        }

        void releaseIntList(List<Integer> list) {
            if (list != null && intListPool.size() < MAX_POOL_SIZE) {
                list.clear();
                intListPool.add(list);
            }
        }
    }

    private final CollectionPool collectionPool = new CollectionPool();

    /* ===== Session tracking ===== */
    private Instant sessionStart = Instant.EPOCH;

    /* ===== VolumeNode DTO ===== */
    public static final class VolumeNode {
        public final int startPrice, endPrice, centerPrice;
        public final long cumVolume;
        public final long lastUpdatedNanos;

        private VolumeNode(int startPrice, int endPrice, int centerPrice, long cumVolume, long ts) {
            this.startPrice = startPrice;
            this.endPrice = endPrice;
            this.centerPrice = centerPrice;
            this.cumVolume = cumVolume;
            this.lastUpdatedNanos = ts;
        }
    }

    /* ===== Constructors ===== */

    /**
     * Builds a volume profile with sane defaults:
     * 70% value area, at most 10 HVN/LVN nodes.
     */
    public VolumeProfileAnalyzer() {
        this(70, 10);
    }

    /**
     * Constructor with value area percent and max node count.
     *
     * @param volAreaPercent % of volume to include in value area (typ 70)
     * @param maxNodeCount   max HVN / LVN nodes to keep
     */
    public VolumeProfileAnalyzer(int volAreaPercent, int maxNodeCount) {
        this.volAreaPercent = Math.min(Math.max(volAreaPercent, 50), 95); // clamp 50‑95
        this.maxNodeCount = Math.max(1, maxNodeCount);

        // Initialize Health Tracker
        try {
            this.inputDataHealthTracker = new InputDataHealthTrackerV2("VPA-Tracker"); // Unique name
        } catch (Exception e) {
            Log.error("VolumeProfileAnalyzer: Failed to initialize InputDataHealthTrackerV2", e);
            this.inputDataHealthTracker = null; // Ensure it's null if init fails
        }
    }

    /* ===== Debug Logging Helpers ===== */
    private static void logDebug(String message) {
        if (DEBUG) {
            System.out.println(message);
        }
    }

    private static void logDebugFormat(String format, Object... args) {
        if (DEBUG) {
            System.out.println(String.format(format, args));
        }
    }

    /* ===== Public API ===== */

    /**
     * @return The current bin width used for price binning.
     */
    public int getBinWidth() {
        return binWidth;
    }

    /**
     * Feed every trade (tick) to the profile.
     *
     * @param price    raw trade price (integer ticks)
     * @param qty      trade size / volume (>0). Non-positive sizes are ignored.
     * @param nowNanos current timestamp in nanoseconds
     */
    public void onTrade(int price, int qty, long nowNanos) {
        if (qty <= 0)
            return;

        synchronized (updateLock) {
            this.lastTradeNanos = nowNanos;
            lastPrice = price;
            volAtPrice.add(price, qty);
            sessionVolume += qty;
            totalTrades++;

            // no POC detection here
        }
    }

    /**
     * Ensures reusable buffers have adequate capacity and populates them with
     * histogram data.
     * This avoids array allocations on every refresh() call.
     * * @param requiredCapacity    minimum required capacity for bins and volumes
     * arrays
     * @param currentHistogramLow the low price of the current histogram from
     * volAtPrice.low()
     */
    private void ensureBufferCapacityAndPopulate(int requiredCapacity, int currentHistogramLow) {
        if (reusableBins.length < requiredCapacity) {
            int newSize = Math.max(requiredCapacity, Math.max(512, reusableBins.length * 2)); // Exponential growth
            reusableBins = new int[newSize];
            reusableVolumes = new long[newSize];
        }

        reusableNonZeroVolumes.clear(); // Clear before populating
        for (int i = 0; i < requiredCapacity; i++) {
            int price = currentHistogramLow + i;
            // Assuming volAtPrice.get() is efficient enough here.
            // If volAtPrice.vol and volAtPrice.offset were directly accessible
            // (package-private)
            // and this method was in the same package, it could be faster.
            // However, volAtPrice.get() encapsulates bounds checking.
            long volume = volAtPrice.get(price);
            reusableBins[i] = price;
            reusableVolumes[i] = volume;
            if (volume > 0) {
                reusableNonZeroVolumes.add(volume);
            }
        }
    }

    /**
     * Recomputes POC, value‑area, HVN & LVN lists using percentile-based gating.
     * Should be called periodically (e.g. once per second or UI frame).
     *
     * @param nowNanos current wall‑clock time in nanoseconds
     */
    public void refresh(long nowNanos) {
        synchronized (updateLock) {
            this.lastRefreshTimestampNanos = nowNanos; // Update timestamp

            // Update Health Tracker
            if (inputDataHealthTracker != null) {
                boolean isValidTrade = !Double.isNaN(lastPrice) && !Double.isInfinite(lastPrice);
                inputDataHealthTracker.update(lastPrice, nowNanos, isValidTrade);
            }

            if (volAtPrice.span() < 5) {
                handleSmallProfile();
                this.sufficientDataForProfile = false;
                return;
            }

            this.sufficientDataForProfile = (sessionVolume >= MIN_SESSION_VOLUME_FOR_VALUE_AREA && totalTrades > 10);

            findGlobalMaximumPOC(); // First attempt at POC detection

            int n = volAtPrice.span() + 1;
            if (n <= 0) { // Should be caught by span() < 5, but as a safeguard
                handleSmallProfile(); // Or similar logic if n is too small
                this.sufficientDataForProfile = false;
                return;
            }
            ensureBufferCapacityAndPopulate(n, volAtPrice.low());
            // reusableBins, reusableVolumes, and reusableNonZeroVolumes are now populated
            // for 'n' elements.

            calculateVolumeOrderedValueArea(reusableBins, reusableVolumes, n);

            VolumeStats stats = VolumeStats.of(reusableNonZeroVolumes); // Use optimized list

            double hvnGate = stats.median + HVN_MAD_MULTIPLIER * stats.mad;
            double lvnGate = Math.max(MIN_LVN_VOLUME, stats.median - LVN_MAD_MULTIPLIER * stats.mad);
            if (lvnGate < MIN_LVN_VOLUME)
                lvnGate = MIN_LVN_VOLUME;
            hvnGate = Math.max(hvnGate, RELATIVE_HVN_FLOOR * pocVol);

            if (DEBUG) {
                lastDebugInfo = String.format(
                        "Median: %.2f, MAD: %.2f, HVN Gate: %.2f, LVN Gate: %.2f, POC Vol: %d",
                        stats.median, stats.mad, hvnGate, lvnGate, pocVol);
                logDebug("HVN/LVN Gates - " + lastDebugInfo);
                logDebugFormat("Stats from reusableNonZeroVolumes size: %d", reusableNonZeroVolumes.size());
            }

            List<VolumeNode> hvnCollector = collectionPool.acquireNodeList();
            List<VolumeNode> hvnTmp;
            try {
                findHighestVolumeNodes(reusableBins, reusableVolumes, n, hvnGate, hvnCollector, nowNanos);
                hvnTmp = consolidateNodes(hvnCollector, true, reusableBins, reusableVolumes, n, nowNanos);
            } finally {
                collectionPool.releaseNodeList(hvnCollector);
            }

            int hvnLimit = Math.max(12, maxNodeCount * 2);
            if (hvnTmp.size() > hvnLimit) {
                if (hvnTmp instanceof ArrayList) { 
                    hvnTmp.subList(hvnLimit, hvnTmp.size()).clear();
                } else { 
                    hvnTmp = new ArrayList<>(hvnTmp.subList(0, hvnLimit));
                }
            }

            ensurePocMatchesHighestVolume(hvnTmp, reusableBins, reusableVolumes, n); 

            List<VolumeNode> lvnCollector = collectionPool.acquireNodeList();
            List<VolumeNode> lvnTmp;
            try {
                findLvnsBetweenHvns(lvnCollector, hvnTmp, reusableBins, reusableVolumes, n, lvnGate, nowNanos);
                if (lvnCollector.size() <= 1) {
                    findLvnsInValueArea(reusableBins, reusableVolumes, n, lvnGate, lvnCollector, nowNanos);
                }
                if (lvnCollector.size() <= 1) {
                    logDebug("No LVNs found in primary method, trying simpler approach");
                    findLvnsSimpleMethod(reusableBins, reusableVolumes, n, lvnGate * 1.5, lvnCollector, nowNanos);
                }
                if (lvnCollector.size() <= 1) {
                    logDebug("Still no LVNs found, using original fallback method");
                    clusterNodesExact(reusableBins, reusableVolumes, n, lvnGate * 2.0, false, lvnCollector, nowNanos);
                }
                lvnTmp = consolidateNodes(lvnCollector, false, reusableBins, reusableVolumes, n, nowNanos);
            } finally {
                collectionPool.releaseNodeList(lvnCollector);
            }

            lvnTmp.sort(Comparator.comparingLong(a -> a.cumVolume));
            if (lvnTmp.size() > maxNodeCount) {
                if (lvnTmp instanceof ArrayList) {
                    lvnTmp.subList(maxNodeCount, lvnTmp.size()).clear();
                } else {
                    lvnTmp = new ArrayList<>(lvnTmp.subList(0, maxNodeCount));
                }
            }

            if (DEBUG) {
                logDebugFormat("Found %d LVN nodes (pre-clip):", lvnTmp.size());
                for (VolumeNode node : lvnTmp) {
                    logDebugFormat("LVN (pre-clip): %d-%d, center=%d, volume=%d",
                            node.startPrice, node.endPrice, node.centerPrice, node.cumVolume);
                }
            }

            developingLVN = updateDevelopingLvn(lvnTmp); 

            hvnTmp.removeIf(node -> node.centerPrice < developingVAL || node.centerPrice > developingVAH);
            lvnTmp.removeIf(node -> node.centerPrice < developingVAL || node.centerPrice > developingVAH);

            if (hvnTmp.isEmpty() && !this.hvnNodes.isEmpty()) { 
                hvnTmp = new ArrayList<>(this.hvnNodes);
            }
            if (lvnTmp.isEmpty() && !this.lvnNodes.isEmpty()) { 
                lvnTmp = new ArrayList<>(this.lvnNodes);
            }

            this.hvnNodes = Collections.unmodifiableList(new ArrayList<>(hvnTmp)); 
            this.lvnNodes = Collections.unmodifiableList(new ArrayList<>(lvnTmp)); 

            // --- ENFORCE GLOBAL MAXIMUM CHECK AT END OF REFRESH ---
            pocVol = volAtPrice.get(pocPrice); 

            long absoluteMax = pocVol; 
            int absoluteMaxPrice = pocPrice;

            final long[] currentVolArray = volAtPrice.vol;
            final int currentOffset = volAtPrice.offset;
            final int currentLength = currentVolArray.length;

            for (int i = 0; i < currentLength; i++) {
                final long vol = currentVolArray[i];
                if (vol > absoluteMax) {
                    absoluteMax = vol;
                    absoluteMaxPrice = currentOffset + i;
                }
            }
            if (absoluteMax > pocVol) {
                logDebugFormat("CRITICAL POC CORRECTION: Found true global maximum at %d with volume %d (previous POC was %d with volume %d)",
                        absoluteMaxPrice, absoluteMax, pocPrice, pocVol);
                pocPrice = absoluteMaxPrice;
                pocVol = absoluteMax;
            }

            if (pocPrice < developingVAL)
                developingVAL = pocPrice;
            if (pocPrice > developingVAH)
                developingVAH = pocPrice;
        }
    }

    /**
     * Guaranteed method to find the true global maximum POC
     * by directly accessing the raw histogram data
     */
    private void findGlobalMaximumPOC() {
        int histogramSize = volAtPrice.vol.length;
        if (histogramSize == 0)
            return;

        int histogramOffset = volAtPrice.offset;
        long[] volumeArray = volAtPrice.vol;

        int maxIndex = 0;
        long maxVolume = 0;
        for (int i = 0; i < histogramSize; i++) {
            if (volumeArray[i] > maxVolume) {
                maxVolume = volumeArray[i];
                maxIndex = i;
            }
        }
        int globalMaxPrice = histogramOffset + maxIndex;

        if (DEBUG) {
            logDebugFormat("Global POC scan: Found maximum at price %d with volume %d (index %d of %d, histogram offset %d)",
                    globalMaxPrice, maxVolume, maxIndex, histogramSize, histogramOffset);

            // Simplified instantiation for debug-only list
            List<Map.Entry<Integer, Long>> topVolumes = new ArrayList<>();
            for (int i = 0; i < histogramSize; i++) {
                if (volumeArray[i] > 0) {
                    topVolumes.add(new AbstractMap.SimpleEntry<>(histogramOffset + i, volumeArray[i]));
                }
            }
            topVolumes.sort((a, b) -> Long.compare(b.getValue(), a.getValue()));
            logDebug("Top 5 volume prices:");
            for (int i = 0; i < Math.min(5, topVolumes.size()); i++) {
                Map.Entry<Integer, Long> entry = topVolumes.get(i);
                logDebugFormat("%d. Price: %d, Volume: %d", (i + 1), entry.getKey(), entry.getValue());
            }
        }

        if (maxVolume > pocVol) {
            pocPrice = globalMaxPrice;
            pocVol = maxVolume;
        } else if (maxVolume > 0 && pocPrice == Integer.MIN_VALUE) {
            pocPrice = globalMaxPrice;
            pocVol = maxVolume;
        }
    }

    /**
     * Consolidate nearby nodes by analyzing volume structure between them
     */
    private List<VolumeNode> consolidateNodes(List<VolumeNode> nodes, boolean isHvn,
            int[] bins, long[] volumes, int n, long nowNanos) { 
        if (nodes.size() <= 1) {
            return new ArrayList<>(nodes); 
        }
        List<VolumeNode> allNodes = new ArrayList<>(nodes); 
        boolean changed;
        do {
            changed = false;
            VolumeNode bestA = null, bestB = null;
            int closestDistance = Integer.MAX_VALUE;
            for (int i = 0; i < allNodes.size(); i++) {
                for (int j = i + 1; j < allNodes.size(); j++) {
                    VolumeNode a = allNodes.get(i);
                    VolumeNode b = allNodes.get(j);
                    int distance = Math.abs(a.centerPrice - b.centerPrice);
                    if (distance < closestDistance && distance <= PROXIMITY_THRESHOLD &&
                            analyzeVolumeBetween(a, b, isHvn, bins, volumes, n)) { 
                        closestDistance = distance;
                        bestA = a;
                        bestB = b;
                    }
                }
            }
            if (bestA != null && bestB != null) {
                List<VolumeNode> toMerge = new ArrayList<>(); 
                toMerge.add(bestA);
                toMerge.add(bestB);
                VolumeNode merged = mergeNodes(toMerge, isHvn, nowNanos);
                allNodes.remove(bestA);
                allNodes.remove(bestB);
                allNodes.add(merged);
                changed = true;
            }
        } while (changed);

        for (int i = 0; i < allNodes.size(); i++) {
            VolumeNode node = allNodes.get(i);
            int trueCenter = node.centerPrice;
            long bestVol = volAtPrice.get(trueCenter);

            int scanStart = Math.max(volAtPrice.low(), node.startPrice - NODE_RECENTER_EXPANSION_TICKS);
            int scanEnd = Math.min(volAtPrice.high(), node.endPrice + NODE_RECENTER_EXPANSION_TICKS);

            if (isHvn) {
                for (int p = scanStart; p <= scanEnd; p++) {
                    long v = volAtPrice.get(p);
                    if (v > bestVol) {
                        bestVol = v;
                        trueCenter = p;
                    }
                }
            } else {
                for (int p = scanStart; p <= scanEnd; p++) {
                    long v = volAtPrice.get(p);
                    if (v > 0 && (v < bestVol || bestVol == 0)) {
                        bestVol = v;
                        trueCenter = p;
                    }
                }
            }
            if (trueCenter != node.centerPrice) {
                allNodes.set(i, new VolumeNode(node.startPrice, node.endPrice, trueCenter, node.cumVolume,
                        node.lastUpdatedNanos));
            }
        }
        return allNodes;
    }

    /**
     * Analyze the volume profile between two nodes to determine if they're part of
     * the same structure
     */
    private boolean analyzeVolumeBetween(VolumeNode a, VolumeNode b, boolean isHvn,
            int[] bins, long[] volumes, int n) { 
        int minPrice = Math.min(a.centerPrice, b.centerPrice);
        int maxPrice = Math.max(a.centerPrice, b.centerPrice);
        if (maxPrice - minPrice <= 1 ||
                (a.endPrice >= b.startPrice && a.startPrice <= b.endPrice)) {
            return true;
        }
        long volA = getVolumeAtPrice(a.centerPrice, bins, volumes, n); 
        long volB = getVolumeAtPrice(b.centerPrice, bins, volumes, n); 
        long referenceVol = Math.min(volA, volB);
        double threshold = referenceVol * STRUCTURE_THRESHOLD;
        boolean hasSignificantChange = false;
        for (int i = 0; i < n; i++) { 
            if (bins[i] > minPrice && bins[i] < maxPrice) {
                long vol = volumes[i];
                if (isHvn) {
                    if (vol < threshold) {
                        hasSignificantChange = true;
                        break;
                    }
                } else {
                    if (vol > threshold) {
                        hasSignificantChange = true;
                        break;
                    }
                }
            }
        }
        double slope = calculateVolumeSlope(a.centerPrice, b.centerPrice, bins, volumes, n); 
        boolean validShape = isHvn ? (slope > -0.2 && slope < 0.2) || hasSimpleValleyShape(a, b, bins, volumes, n) : 
                (slope > -0.2 && slope < 0.2) || hasSimplePeakShape(a, b, bins, volumes, n); 
        return !hasSignificantChange && validShape;
    }

    /**
     * Get volume at a specific price from the provided bins/volumes snapshot
     */
    private long getVolumeAtPrice(int price, int[] bins, long[] volumes, int n) {
        if (n == 0) { 
            return 0L;
        }
        int firstPriceInSnapshot = bins[0];
        int index = price - firstPriceInSnapshot;

        if (index >= 0 && index < n) {
            if (bins[index] == price) {
                return volumes[index];
            }
        }
        return 0L;
    }

    /**
     * Merge multiple nodes into a single representative node
     */
    private VolumeNode mergeNodes(List<VolumeNode> nodes, boolean isHvn, long nowNanos) {
        if (nodes.isEmpty())
            return null;
        if (nodes.size() == 1)
            return nodes.get(0); 

        int minStart = Integer.MAX_VALUE;
        int maxEnd = Integer.MIN_VALUE;
        long totalVol = 0;
        long weightedSum = 0;
        for (VolumeNode node : nodes) {
            minStart = Math.min(minStart, node.startPrice);
            maxEnd = Math.max(maxEnd, node.endPrice);
            totalVol += node.cumVolume;
            weightedSum += (long) node.centerPrice * node.cumVolume;
        }
        if (totalVol == 0) { 
            return new VolumeNode(minStart, maxEnd, nodes.get(0).centerPrice, 0, nowNanos); 
        }
        int centerPrice = (int) Math.round((double) weightedSum / totalVol);
        return new VolumeNode(minStart, maxEnd, centerPrice, totalVol, nowNanos);
    }

    /**
     * Improved method to find highest volume nodes
     */
    private void findHighestVolumeNodes(int[] bins, long[] volumes, int n, double hvnGate,
            List<VolumeNode> hvnNodesList, long nowNanos) { 
        if (n == 0)
            return;

        List<Integer> localMaxIndices = collectionPool.acquireIntList();
        try {
            if (n > 1) { 
                for (int i = 1; i < n - 1; i++) {
                    if (volumes[i] > 0 && volumes[i] >= volumes[i - 1] && volumes[i] >= volumes[i + 1]) {
                        localMaxIndices.add(i);
                    }
                }
            }
            if (n > 0 && volumes[0] > 0 && (n == 1 || volumes[0] >= volumes[1])) { 
                localMaxIndices.add(0);
            }
            if (n > 1 && volumes[n - 1] > 0 && volumes[n - 1] >= volumes[n - 2]) {
                if (n - 1 != 0 || !localMaxIndices.contains(0)) {
                    if (!localMaxIndices.contains(n - 1))
                        localMaxIndices.add(n - 1);
                }
            }
            Set<Integer> uniqueIndices = new HashSet<>(localMaxIndices);
            localMaxIndices.clear();
            localMaxIndices.addAll(uniqueIndices);

            localMaxIndices.sort((a, b) -> Long.compare(volumes[b], volumes[a]));

            Set<Integer> processedIndices = new HashSet<>();
            for (int peakIdx : localMaxIndices) {
                if (processedIndices.contains(peakIdx))
                    continue;

                long peakVol = volumes[peakIdx];
                if (peakVol <= hvnGate)
                    continue; 

                int startIdx = peakIdx;
                int endIdx = peakIdx;
                for (int i = peakIdx - 1; i >= 0; i--) {
                    if (volumes[i] <= hvnGate || processedIndices.contains(i))
                        break; 
                    startIdx = i;
                }
                for (int i = peakIdx + 1; i < n; i++) {
                    if (volumes[i] <= hvnGate || processedIndices.contains(i))
                        break; 
                    endIdx = i;
                }

                for (int i = startIdx; i <= endIdx; i++) {
                    processedIndices.add(i);
                }

                long cumVol = 0;
                for (int i = startIdx; i <= endIdx; i++) {
                    cumVol += volumes[i];
                }

                int truePeakPriceInSegment = bins[startIdx];
                long maxVolInSegment = volumes[startIdx];
                if (startIdx <= endIdx) {
                    for (int i = startIdx; i <= endIdx; i++) {
                        if (volumes[i] > maxVolInSegment) {
                            maxVolInSegment = volumes[i];
                            truePeakPriceInSegment = bins[i];
                        }
                    }
                } else {
                    logDebugFormat("Warning: Invalid segment in findHighestVolumeNodes: startIdx=%d, endIdx=%d", startIdx, endIdx);
                    continue;
                }

                if (validateHvnNode(truePeakPriceInSegment, maxVolInSegment, bins, volumes, n)) { 
                    hvnNodesList.add(
                            new VolumeNode(bins[startIdx], bins[endIdx], truePeakPriceInSegment, cumVol, nowNanos));
                    logDebugFormat("Added HVN: %d-%d, center=%d, peakVol=%d, cumVol=%d",
                            bins[startIdx], bins[endIdx], truePeakPriceInSegment, maxVolInSegment, cumVol);
                } else if (DEBUG) { // Keep specific logic for this debug message
                    System.out.println("Rejected HVN candidate at " + truePeakPriceInSegment +
                            " (segment " + bins[startIdx] + "-" + bins[endIdx] + ") due to validation failure");
                }
            }
        } finally {
            collectionPool.releaseIntList(localMaxIndices);
        }
    }

    /**
     * Validates that an HVN is truly a local maximum by checking surrounding
     * ±HVN_SCAN_RANGE ticks
     */
    private boolean validateHvnNode(int centerPrice, long centerVolume, int[] bins, long[] volumes, int n) { 
        long actualCenterVolume = volAtPrice.get(centerPrice); 
        int scanRange = HVN_SCAN_RANGE;
        for (int i = 0; i < n; i++) { 
            int price = bins[i];
            if (price == centerPrice)
                continue;
            if (Math.abs(price - centerPrice) <= scanRange) {
                if (volumes[i] > actualCenterVolume) { 
                    logDebugFormat("HVN at %d rejected: higher volume %d found at %d",
                            centerPrice, volumes[i], price);
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Simple method for finding LVNs when other methods fail
     */
    private void findLvnsSimpleMethod(int[] bins, long[] volumes, int n, double lvnGate,
            List<VolumeNode> lvnNodesList, long nowNanos) { 
        if (developingVAL == 0 || developingVAH == 0)
            return;

        boolean building = false;
        int clusterStart = 0;
        int clusterEnd = 0;
        long clusterVol = 0;

        for (int i = 0; i < n; i++) { 
            int price = bins[i];
            if (price < developingVAL || price > developingVAH)
                continue;

            long vol = volumes[i];
            boolean qualifies = vol > 0 && vol <= lvnGate;

            if (qualifies) {
                if (!building) {
                    building = true;
                    clusterStart = price;
                    clusterVol = 0;
                }
                clusterVol += vol;
                clusterEnd = price;
            } else {
                if (building) {
                    int trueTroughPriceInCluster = clusterStart;
                    long minVolInCluster = Long.MAX_VALUE;
                    boolean foundValidTrough = false;
                    if (clusterStart <= clusterEnd) {
                        for (int p = clusterStart; p <= clusterEnd; p++) {
                            long v = volAtPrice.get(p);
                            if (v > 0 && v < minVolInCluster) {
                                minVolInCluster = v;
                                trueTroughPriceInCluster = p;
                                foundValidTrough = true;
                            }
                        }
                        if (!foundValidTrough && clusterVol > 0) {
                            long firstPointVol = volAtPrice.get(clusterStart);
                            if (firstPointVol > 0 && firstPointVol <= lvnGate) {
                                trueTroughPriceInCluster = clusterStart;
                                minVolInCluster = firstPointVol;
                                foundValidTrough = true;
                            }
                        }
                    }
                    if (foundValidTrough
                            && validateLvnNode(trueTroughPriceInCluster, minVolInCluster, bins, volumes, n)) { 
                        lvnNodesList.add(new VolumeNode(clusterStart, clusterEnd, trueTroughPriceInCluster, clusterVol,
                                nowNanos));
                        logDebugFormat("Simple method added LVN: %d-%d, center=%d, vol=%d",
                                clusterStart, clusterEnd, trueTroughPriceInCluster, clusterVol);
                    } else if (DEBUG) {
                         System.out.println("Simple method rejected LVN candidate at "
                                + (foundValidTrough ? trueTroughPriceInCluster : clusterStart) + " (segment "
                                + clusterStart + "-" + clusterEnd + ") due to validation failure. FoundTrough: "
                                + foundValidTrough + " MinVolInCluster: " + minVolInCluster);
                    }
                    building = false;
                }
            }
        }
        if (building) { 
            int trueTroughPriceInCluster = clusterStart;
            long minVolInCluster = Long.MAX_VALUE;
            boolean foundValidTrough = false;
            if (clusterStart <= clusterEnd) {
                for (int p = clusterStart; p <= clusterEnd; p++) {
                    long v = volAtPrice.get(p);
                    if (v > 0 && v < minVolInCluster) {
                        minVolInCluster = v;
                        trueTroughPriceInCluster = p;
                        foundValidTrough = true;
                    }
                }
                if (!foundValidTrough && clusterVol > 0) {
                    long firstPointVol = volAtPrice.get(clusterStart);
                    if (firstPointVol > 0 && firstPointVol <= lvnGate) {
                        trueTroughPriceInCluster = clusterStart;
                        minVolInCluster = firstPointVol;
                        foundValidTrough = true;
                    }
                }
            }
            if (foundValidTrough && validateLvnNode(trueTroughPriceInCluster, minVolInCluster, bins, volumes, n)) { 
                lvnNodesList
                        .add(new VolumeNode(clusterStart, clusterEnd, trueTroughPriceInCluster, clusterVol, nowNanos));
                logDebugFormat("Simple method added final LVN: %d-%d, center=%d, vol=%d",
                        clusterStart, clusterEnd, trueTroughPriceInCluster, clusterVol);
            } else if (DEBUG) {
                 System.out.println("Simple method rejected final LVN candidate at "
                        + (foundValidTrough ? trueTroughPriceInCluster : clusterStart) + " (segment " + clusterStart
                        + "-" + clusterEnd + ") due to validation failure. FoundTrough: " + foundValidTrough
                        + " MinVolInCluster: " + minVolInCluster);
            }
        }
    }

    /**
     * Detect LVNs that lie in the valleys *between* successive HVN peaks.
     */
    private void findLvnsBetweenHvns(List<VolumeNode> out, List<VolumeNode> hvnInputNodes, 
            int[] bins, long[] volumes, int n, double lvnGate, long nowNanos) { 
        if (hvnInputNodes.size() < 2)
            return;

        List<VolumeNode> byPx = new ArrayList<>(hvnInputNodes); 
        byPx.sort(Comparator.comparingInt(node -> node.centerPrice));

        for (int i = 0; i < byPx.size() - 1; i++) {
            VolumeNode left = byPx.get(i);
            VolumeNode right = byPx.get(i + 1);

            int start = left.endPrice + 1;
            int end = right.startPrice - 1;
            if (start >= end)
                continue;

            long bestVol = Long.MAX_VALUE;
            int bestPx = -1;
            for (int p = start; p <= end; p++) {
                long v = volAtPrice.get(p); 
                if (v > 0 && v <= lvnGate && v < bestVol) {
                    bestVol = v;
                    bestPx = p;
                }
            }
            if (bestPx != -1 && validateLvnNode(bestPx, bestVol, bins, volumes, n)) { 
                out.add(new VolumeNode(bestPx, bestPx, bestPx, bestVol, nowNanos));
                logDebugFormat("Between‑HVN LVN @%d vol=%d between HVN@%d & HVN@%d",
                        bestPx, bestVol, left.centerPrice, right.centerPrice);
            }
        }
    }

    /**
     * Validates that an LVN is truly a local minimum
     */
    private boolean validateLvnNode(int centerPrice, long centerVolCandidate, int[] bins, long[] volumes, int n) { 
        long centerActualVol = volAtPrice.get(centerPrice);
        if (centerActualVol == 0 || centerActualVol == Long.MAX_VALUE) {
            logDebugFormat("LVN at %d rejected: centerActualVol is 0 or uninitialized (%d)",
                    centerPrice, centerActualVol);
            return false;
        }

        int scanRange = LVN_SCAN_RANGE;
        boolean foundHigherLeft = false;
        boolean foundHigherRight = false;

        for (int i = 0; i < n; i++) { 
            int price = bins[i];
            if (price == centerPrice)
                continue;

            if (Math.abs(price - centerPrice) <= scanRange) {
                long volAtIndex = volumes[i];
                if (volAtIndex > centerActualVol) {
                    if (price < centerPrice)
                        foundHigherLeft = true;
                    else
                        foundHigherRight = true;
                }
            }
        }

        if (!(foundHigherLeft && foundHigherRight)) {
            if (DEBUG) { // Keep specific logic for this debug message
                System.out.println("LVN at " + centerPrice + " (vol " + centerActualVol
                        + ") rejected: missing higher volumes on " +
                        (!foundHigherLeft ? "left" : "")
                        + (!foundHigherRight ? (!foundHigherLeft ? " & right" : "right") : "") + " side within "
                        + scanRange + " ticks.");
            }
            return false;
        }
        for (int i = 0; i < n; i++) { 
            int price = bins[i];
            if (price == centerPrice)
                continue;

            if (Math.abs(price - centerPrice) <= scanRange) {
                long volAtIndex = volumes[i];
                if (volAtIndex > 0 && volAtIndex < centerActualVol) {
                    logDebugFormat("LVN at %d (vol %d) rejected: found lower non-zero volume %d at %d within %d ticks.",
                            centerPrice, centerActualVol, volAtIndex, price, scanRange);
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Find LVNs within the Value Area
     */
    private void findLvnsInValueArea(int[] bins, long[] volumes, int n, double lvnGate,
            List<VolumeNode> lvnNodesList, long nowNanos) { 
        if (developingVAL == 0 || developingVAH == 0) {
            logDebug("VAL/VAH not set, can't detect LVNs in Value Area");
            return;
        }

        int valIdx = -1, vahIdx = -1;
        for (int i = 0; i < n; i++) { 
            if (bins[i] == developingVAL)
                valIdx = i;
            if (bins[i] == developingVAH)
                vahIdx = i;
        }

        if (valIdx == -1 || vahIdx == -1 || valIdx >= vahIdx) {
            logDebug("Invalid VAL/VAH indices, can't detect LVNs in Value Area");
            return;
        }

        int valueAreaWidth = vahIdx - valIdx + 1;
        int adaptiveBuffer = Math.min(LVN_BUFFER_TICKS, Math.max(1, valueAreaWidth / 10));
        logDebugFormat("Value Area: %d to %d, width: %d, using buffer: %d",
                developingVAL, developingVAH, valueAreaWidth, adaptiveBuffer);

        int effectiveValIdx = Math.min(valIdx + adaptiveBuffer, vahIdx);
        int effectiveVahIdx = Math.max(vahIdx - adaptiveBuffer, valIdx);

        if (effectiveValIdx >= effectiveVahIdx) {
            logDebugFormat("Value area too narrow after buffers: %d to %d", bins[valIdx], bins[vahIdx]);
            return;
        }

        double avgVol = calculateAverageVolume(volumes, n, effectiveValIdx, effectiveVahIdx); 
        double localLvnGate = Math.max(lvnGate, 0.5 * avgVol);
        logDebugFormat("Scanning for LVNs from: %d to %d, avgVol: %.2f, localLvnGate: %.2f",
                bins[effectiveValIdx], bins[effectiveVahIdx], avgVol, localLvnGate);

        boolean building = false;
        int clusterStart = 0, clusterEnd = 0;
        long clusterVol = 0;

        for (int i = effectiveValIdx; i <= effectiveVahIdx; i++) {
            boolean qualifies = volumes[i] > 0 && volumes[i] <= localLvnGate;
            if (DEBUG && qualifies) { // Keep specific logic for this debug message structure
                 System.out.println("Found qualifying LVN price at " + bins[i] + " with volume " + volumes[i]
                        + " (threshold: " + localLvnGate + ")");
            }


            if (qualifies) {
                if (!building) {
                    building = true;
                    clusterStart = bins[i];
                    clusterVol = 0;
                }
                clusterVol += volumes[i];
                clusterEnd = bins[i];
            } else {
                if (building) {
                    int trueTroughPriceInCluster = clusterStart;
                    long minVolInCluster = Long.MAX_VALUE;
                    boolean foundValidTrough = false;
                    if (clusterStart <= clusterEnd) {
                        for (int p = clusterStart; p <= clusterEnd; p++) {
                            long v = volAtPrice.get(p);
                            if (v > 0 && v < minVolInCluster) {
                                minVolInCluster = v;
                                trueTroughPriceInCluster = p;
                                foundValidTrough = true;
                            }
                        }
                        if (!foundValidTrough && clusterVol > 0) {
                            long firstPointVol = volAtPrice.get(clusterStart);
                            if (firstPointVol > 0 && firstPointVol <= localLvnGate) {
                                trueTroughPriceInCluster = clusterStart;
                                minVolInCluster = firstPointVol;
                                foundValidTrough = true;
                            }
                        }
                    }
                    if (foundValidTrough
                            && validateLvnNode(trueTroughPriceInCluster, minVolInCluster, bins, volumes, n)) { 
                        lvnNodesList.add(new VolumeNode(clusterStart, clusterEnd, trueTroughPriceInCluster, clusterVol,
                                nowNanos));
                        logDebugFormat("Added LVN: %d-%d, center=%d, vol=%d",
                                clusterStart, clusterEnd, trueTroughPriceInCluster, clusterVol);
                    } else if (DEBUG) { // Keep specific logic for this debug message
                        System.out.println("Rejected LVN candidate at "
                                + (foundValidTrough ? trueTroughPriceInCluster : clusterStart) + " (segment "
                                + clusterStart + "-" + clusterEnd + ") due to validation failure. FoundTrough: "
                                + foundValidTrough + " MinVolInCluster: " + minVolInCluster);
                    }
                    building = false;
                }
            }
        }
        if (building) { 
            int trueTroughPriceInCluster = clusterStart;
            long minVolInCluster = Long.MAX_VALUE;
            boolean foundValidTrough = false;
            if (clusterStart <= clusterEnd) {
                for (int p = clusterStart; p <= clusterEnd; p++) {
                    long v = volAtPrice.get(p);
                    if (v > 0 && v < minVolInCluster) {
                        minVolInCluster = v;
                        trueTroughPriceInCluster = p;
                        foundValidTrough = true;
                    }
                }
                if (!foundValidTrough && clusterVol > 0) {
                    long firstPointVol = volAtPrice.get(clusterStart);
                    if (firstPointVol > 0 && firstPointVol <= localLvnGate) {
                        trueTroughPriceInCluster = clusterStart;
                        minVolInCluster = firstPointVol;
                        foundValidTrough = true;
                    }
                }
            }
            if (foundValidTrough && validateLvnNode(trueTroughPriceInCluster, minVolInCluster, bins, volumes, n)) { 
                lvnNodesList
                        .add(new VolumeNode(clusterStart, clusterEnd, trueTroughPriceInCluster, clusterVol, nowNanos));
                logDebugFormat("Added final LVN: %d-%d, center=%d, vol=%d",
                        clusterStart, clusterEnd, trueTroughPriceInCluster, clusterVol);
            } else if (DEBUG) { // Keep specific logic for this debug message
                System.out.println("Rejected final LVN candidate at "
                        + (foundValidTrough ? trueTroughPriceInCluster : clusterStart) + " (segment " + clusterStart
                        + "-" + clusterEnd + ") due to validation failure. FoundTrough: " + foundValidTrough
                        + " MinVolInCluster: " + minVolInCluster);
            }
        }
    }

    /**
     * Calculate average non-zero volume in a range from the snapshot arrays
     */
    private double calculateAverageVolume(long[] volumes, int n, int startIdx, int endIdx) { // Added n
        long sum = 0;
        int count = 0;
        int actualStart = Math.max(0, Math.min(startIdx, n - 1));
        int actualEnd = Math.max(0, Math.min(endIdx, n - 1));

        for (int i = actualStart; i <= actualEnd; i++) {
            if (volumes[i] > 0) {
                sum += volumes[i];
                count++;
            }
        }
        return count > 0 ? (double) sum / count : 0;
    }

    /**
     * Update the primary developing LVN
     */
    private int updateDevelopingLvn(List<VolumeNode> currentLvnNodes) { 
        if (currentLvnNodes.isEmpty())
            return developingLVN; 

        List<VolumeNode> sortableLvns = (currentLvnNodes instanceof ArrayList) ? currentLvnNodes
                : new ArrayList<>(currentLvnNodes);
        sortableLvns.sort(Comparator.comparingLong(a -> a.cumVolume));

        int bestCandidate = sortableLvns.get(0).centerPrice;
        if (bestCandidate != lvnCandidate) {
            lvnCandidate = bestCandidate;
            lvnAge = 0;
        } else {
            lvnAge++;
            if (lvnAge >= STABILITY_THRESHOLD && bestCandidate != developingLVN) {
                developingLVN = bestCandidate; 
            }
        }
        return developingLVN;
    }

    /**
     * Calculate Value Area using volume-ordered approach
     */
    private void calculateVolumeOrderedValueArea(int[] bins, long[] volumes, int n) { 
        if (sessionVolume < MIN_SESSION_VOLUME_FOR_VALUE_AREA) {
            developingVAL = pocPrice;
            developingVAH = pocPrice;
            return;
        }

        Integer[] priceIndices = new Integer[n]; 
        for (int i = 0; i < n; i++) {
            priceIndices[i] = i;
        }

        Arrays.sort(priceIndices, (idxA, idxB) -> Long.compare(volumes[idxB], volumes[idxA]));

        long targetVA = sessionVolume * volAreaPercent / 100L;
        long cumVA = 0;
        int minPriceInVA = Integer.MAX_VALUE;
        int maxPriceInVA = Integer.MIN_VALUE;
        boolean pricesAddedToVA = false;

        for (int i = 0; i < n && cumVA < targetVA; i++) {
            int originalIndex = priceIndices[i];
            int price = bins[originalIndex];
            long vol = volumes[originalIndex];

            if (vol == 0 && cumVA == 0 && i > 0)
                continue; 

            cumVA += vol;
            minPriceInVA = Math.min(minPriceInVA, price);
            maxPriceInVA = Math.max(maxPriceInVA, price);
            pricesAddedToVA = true;
        }

        if (pricesAddedToVA) { 
            developingVAL = minPriceInVA;
            developingVAH = maxPriceInVA;
        } else if (pocPrice != Integer.MIN_VALUE) { 
            developingVAL = pocPrice;
            developingVAH = pocPrice;
        }

        logDebugFormat("Value Area calculated: VAL=%d, VAH=%d, target volume %%=%d, target vol=%d, actual vol=%d",
                developingVAL, developingVAH, volAreaPercent, targetVA, cumVA);
    }

    /**
     * Handle small profiles
     */
    private void handleSmallProfile() {
        if (volAtPrice.span() <= 0 && totalTrades == 0) { 
            pocPrice = Integer.MIN_VALUE; 
            pocVol = 0;
            developingVAL = 0; 
            developingVAH = 0;
            return;
        }
        findGlobalMaximumPOC(); 
        developingVAL = pocPrice;
        developingVAH = pocPrice;
        logDebugFormat("Small profile: POC, VAL, VAH all set to %d", pocPrice);
    }

    /**
     * Cluster nodes (fallback method)
     */
    private void clusterNodesExact(int[] bins, long[] vols, int n, 
            double gate, boolean high,
            List<VolumeNode> out, long ts) {
        boolean building = false;
        int clusterStart = 0, clusterEnd = 0;
        long clusterVol = 0;
        long wSum = 0;

        for (int i = 0; i < n; i++) { 
            boolean qualifies = high ? (vols[i] > gate) : (vols[i] > 0 && vols[i] <= gate);
            if (qualifies) {
                if (!building) {
                    building = true;
                    clusterStart = bins[i];
                    clusterVol = 0;
                    wSum = 0;
                }
                clusterVol += vols[i];
                wSum += (long) bins[i] * vols[i];
                clusterEnd = bins[i];
            } else {
                if (building) {
                    addExactCluster(out, high, clusterStart, clusterEnd, clusterVol, wSum, ts, bins, vols, n); 
                    building = false;
                }
            }
        }
        if (building) {
            addExactCluster(out, high, clusterStart, clusterEnd, clusterVol, wSum, ts, bins, vols, n); 
        }
        if (DEBUG && !high) { // Keep specific logic for this debug message
            System.out.println("Fallback method found " + out.size() + " LVN nodes");
        }
    }

    /**
     * Adds a cluster (helper for clusterNodesExact)
     */
    private void addExactCluster(List<VolumeNode> out, boolean high,
            int startPrice, int endPrice,
            long clusterVol, long wSum,
            long ts, int[] bins, long[] volumes, int n) { 
        if (clusterVol <= 0)
            return;
        int centerPrice = (int) Math.round((double) wSum / clusterVol);
        centerPrice = Math.max(startPrice, Math.min(centerPrice, endPrice));

        boolean isValid;
        long peakVolAtCenter = volAtPrice.get(centerPrice); 
        if (high)
            isValid = validateHvnNode(centerPrice, peakVolAtCenter, bins, volumes, n); 
        else
            isValid = validateLvnNode(centerPrice, peakVolAtCenter, bins, volumes, n); 

        if (isValid) {
            out.add(new VolumeNode(startPrice, endPrice, centerPrice, clusterVol, ts));
            logDebugFormat("Fallback added %s: %d-%d, vol=%d",
                    (high ? "HVN" : "LVN"), startPrice, endPrice, clusterVol);
        } else if (DEBUG) { // Keep specific logic for this debug message
            System.out.println("Fallback rejected " + (high ? "HVN" : "LVN") + " at " + centerPrice
                    + " due to validation failure");
        }
    }

    /* ===== Getters ===== */
    public int getPocPrice() {
        return pocPrice;
    }

    public int getDevelopingVAL() {
        return developingVAL;
    }

    public int getDevelopingVAH() {
        return developingVAH;
    }

    public List<VolumeNode> getHvnNodes() {
        return hvnNodes;
    }

    public List<VolumeNode> getLvnNodes() {
        return lvnNodes;
    }

    public String getLastDebugInfo() {
        return lastDebugInfo;
    }

    public long getSessionVolume() {
        return sessionVolume;
    }

    /* ===== Session Management ===== */
    public void startNewSession(Instant sessionStartInstant) {
        synchronized (updateLock) {
            sessionStart = sessionStartInstant;
            totalTrades = 0;
            sessionVolume = 0;
            pocVol = 0;
            volAtPrice.vol = new long[0]; 
            volAtPrice.offset = 0;
            pocPrice = Integer.MIN_VALUE;
            developingVAL = 0;
            developingVAH = 0;
            developingLVN = 0;
            lvnCandidate = Integer.MIN_VALUE;
            lvnAge = 0;
            hvnNodes = Collections.emptyList();
            lvnNodes = Collections.emptyList();
            reusableNonZeroVolumes.clear(); 
            reusableNonZeroVolumes.trimToSize(); // Added for consistency with cleanup()
        }
    }

    public void cleanup() {
        synchronized (updateLock) {
            volAtPrice.vol = new long[0];
            volAtPrice.offset = 0;
            pocPrice = Integer.MIN_VALUE;
            developingVAL = 0;
            developingVAH = 0;
            totalTrades = 0L;
            hvnNodes = Collections.emptyList();
            lvnNodes = Collections.emptyList();
            reusableBins = new int[0]; 
            reusableVolumes = new long[0];
            reusableNonZeroVolumes.clear();
            reusableNonZeroVolumes.trimToSize(); 
            collectionPool.nodeListPool.clear(); 
            collectionPool.intListPool.clear();

            if (inputDataHealthTracker != null) {
                try {
                    inputDataHealthTracker.dispose();
                } catch (Exception e) {
                    Log.error("VolumeProfileAnalyzer: Error cleaning up InputDataHealthTrackerV2", e);
                } finally {
                    inputDataHealthTracker = null;
                }
            }
        }
    }

    public SessionSnap endSessionAndReset(Instant sessionEndInstant, long nowNanos) {
        synchronized (updateLock) {
            refresh(nowNanos);
            Map<Integer, Long> histCopy = new HashMap<>();
            volAtPrice.forEach(histCopy::put);
            SessionSnap snap = new SessionSnap(
                    sessionStart, sessionEndInstant, pocPrice, developingVAH, developingVAL,
                    Collections.unmodifiableList(new ArrayList<>(hvnNodes)), 
                    Collections.unmodifiableList(new ArrayList<>(lvnNodes)), 
                    histCopy, sessionVolume);
            startNewSession(sessionEndInstant); 
            return snap;
        }
    }

    public static final class SessionSnap {
        public final Instant sessionStart, sessionEnd;
        public final int pocPrice, developingVAH, developingVAL;
        public final List<VolumeNode> hvnNodes, lvnNodes;
        public final Map<Integer, Long> histogram;
        public final long sessionVolume;

        public SessionSnap(Instant ss, Instant se, int pp, int dvah, int dval, List<VolumeNode> hvn,
                List<VolumeNode> lvn, Map<Integer, Long> hist, long sv) {
            sessionStart = ss;
            sessionEnd = se;
            pocPrice = pp;
            developingVAH = dvah;
            developingVAL = dval;
            hvnNodes = hvn;
            lvnNodes = lvn;
            histogram = hist;
            sessionVolume = sv;
        }
    }

    /* ===== POC helpers ===== */

    /* ===== TickHistogram (Optimized Growth) ===== */
    private static final class TickHistogram {
        /* package */ volatile long[] vol = new long[0];
        /* package */ int offset = 0;

        private static final int INITIAL_CAPACITY = 512;
        private static final double GROWTH_FACTOR = 1.6;
        private static final int MIN_GROWTH = 64;

        int low() {
            return offset;
        }

        int high() {
            return vol.length == 0 ? offset : offset + vol.length - 1;
        } 

        int span() {
            return vol.length == 0 ? 0 : vol.length - 1;
        }

        void add(int price, int qty) {
            ensureRange(price);
            vol[price - offset] += qty;
        }

        long get(int price) {
            int idx = price - offset;
            return (idx < 0 || idx >= vol.length) ? 0L : vol[idx];
        }

        void forEach(IntLongConsumer c) {
            for (int i = 0; i < vol.length; i++) {
                if (vol[i] != 0)
                    c.accept(offset + i, vol[i]);
            }
        }

        private void ensureRange(int price) {
            if (vol.length == 0) {
                offset = price - (INITIAL_CAPACITY / 2);
                vol = new long[INITIAL_CAPACITY];
                return;
            }
            if (price < offset) {
                int currentHighEdge = offset + vol.length - 1;
                int newOffset = price;
                int requiredMinLength = currentHighEdge - newOffset + 1;
                int newActualLength = vol.length;

                if (requiredMinLength > newActualLength) { 
                    newActualLength = Math.max(requiredMinLength,
                            newActualLength + Math.max(MIN_GROWTH, (int) (vol.length * (GROWTH_FACTOR - 1.0))));
                } else { 
                    int neededExpansion = offset - newOffset; 
                    if (vol.length + neededExpansion > newActualLength) { 
                        newActualLength = Math.max(vol.length + neededExpansion,
                                newActualLength + Math.max(MIN_GROWTH, (int) (vol.length * (GROWTH_FACTOR - 1.0))));
                    }
                    // If newActualLength is not increased beyond vol.length + neededExpansion,
                    // we might only be reallocating to shift. Ensure growth if we are reallocating.
                    else {
                         newActualLength = Math.max(requiredMinLength, // Must be at least this
                                Math.max(vol.length + neededExpansion, // Accommodate shift
                                newActualLength + Math.max(MIN_GROWTH, (int) (vol.length * (GROWTH_FACTOR - 1.0))))); // General growth
                    }
                }

                long[] newVol = new long[newActualLength];
                System.arraycopy(vol, 0, newVol, offset - newOffset, vol.length);
                vol = newVol;
                offset = newOffset;

            } else if (price >= offset + vol.length) {
                int requiredMinLength = price - offset + 1;
                int newActualLength = vol.length;

                if (requiredMinLength > newActualLength) {
                    newActualLength = Math.max(requiredMinLength,
                            newActualLength + Math.max(MIN_GROWTH, (int) (vol.length * (GROWTH_FACTOR - 1.0))));
                    vol = java.util.Arrays.copyOf(vol, newActualLength);
                }
            }
        }

        @FunctionalInterface
        private interface IntLongConsumer {
            void accept(int key, long value);
        }
    }

    // --- Structure Analysis Helpers (Pass n) ---
    private double calculateVolumeSlope(int priceA, int priceB, int[] bins, long[] volumes, int n) { 

        int idxA = -1, idxB = -1;
        if (n > 0) {
            int firstPriceInSnapshot = bins[0];

            int tempIdxA = priceA - firstPriceInSnapshot;
            if (tempIdxA >= 0 && tempIdxA < n && bins[tempIdxA] == priceA) {
                idxA = tempIdxA;
            }

            int tempIdxB = priceB - firstPriceInSnapshot;
            if (tempIdxB >= 0 && tempIdxB < n && bins[tempIdxB] == priceB) {
                idxB = tempIdxB;
            }
        }

        if (idxA == -1 || idxB == -1 || idxA == idxB) {
            idxA = -1;
            idxB = -1; 
            for (int i = 0; i < n; i++) {
                if (bins[i] == priceA)
                    idxA = i;
                if (bins[i] == priceB)
                    idxB = i;
                if (idxA != -1 && idxB != -1)
                    break;
            }
            if (idxA == -1 || idxB == -1 || idxA == idxB)
                return 0.0; 
        }

        double sum = 0.0;
        int count = 0;
        int start = Math.min(idxA, idxB), end = Math.max(idxA, idxB);
        for (int i = start; i < end && (i + 1) < n; i++) {
            sum += (volumes[i + 1] - volumes[i]);
            count++;
        }
        return count > 0 ? sum / count : 0.0;
    }

    private boolean hasSimpleValleyShape(VolumeNode a, VolumeNode b, int[] bins, long[] volumes, int n) { 
        int idxA = -1, idxB = -1;
        for (int i = 0; i < n; i++) { 
            if (bins[i] == a.centerPrice)
                idxA = i;
            if (bins[i] == b.centerPrice)
                idxB = i;
            if (idxA != -1 && idxB != -1)
                break;
        }
        if (idxA == -1 || idxB == -1 || idxA == idxB)
            return false;

        int start = Math.min(idxA, idxB), end = Math.max(idxA, idxB);
        if (start + 1 > end - 1)
            return false; 

        long minVol = Long.MAX_VALUE;
        int minIdx = -1;
        for (int i = start; i <= end && i < n; i++) { 
            if (volumes[i] < minVol) {
                minVol = volumes[i];
                minIdx = i;
            }
        }
        return minIdx > start && minIdx < end; 
    }

    private boolean hasSimplePeakShape(VolumeNode a, VolumeNode b, int[] bins, long[] volumes, int n) { 
        int idxA = -1, idxB = -1;
        for (int i = 0; i < n; i++) { 
            if (bins[i] == a.centerPrice)
                idxA = i;
            if (bins[i] == b.centerPrice)
                idxB = i;
            if (idxA != -1 && idxB != -1)
                break;
        }
        if (idxA == -1 || idxB == -1 || idxA == idxB)
            return false;

        int start = Math.min(idxA, idxB), end = Math.max(idxA, idxB);
        if (start + 1 > end - 1)
            return false; 

        long maxVol = Long.MIN_VALUE;
        int maxIdx = -1;
        for (int i = start; i <= end && i < n; i++) { 
            if (volumes[i] > maxVol) {
                maxVol = volumes[i];
                maxIdx = i;
            }
        }
        return maxIdx > start && maxIdx < end; 
    }

    /**
     * Ensure POC matches highest volume (can optionally take bins/volumes/n if it
     * needs them,
     * but current logic uses volAtPrice.get and hvnNodes)
     */
    private void ensurePocMatchesHighestVolume(List<VolumeNode> currentHvnNodes, int[] bins, long[] volumes, int n) { 
        if (currentHvnNodes.isEmpty() && pocPrice == Integer.MIN_VALUE)
            return;

        int currentPocPriceVal = (pocPrice == Integer.MIN_VALUE) ? 0 : pocPrice;
        long currentPocVolumeVal = (pocPrice == Integer.MIN_VALUE) ? 0 : volAtPrice.get(pocPrice);

        int bestPriceCandidate = currentPocPriceVal;
        long bestVolCandidate = currentPocVolumeVal;

        if (!currentHvnNodes.isEmpty()) {
            for (VolumeNode hvn : currentHvnNodes) {
                for (int p = hvn.startPrice; p <= hvn.endPrice; p++) {
                    long v = volAtPrice.get(p);
                    if (v > bestVolCandidate) {
                        bestVolCandidate = v;
                        bestPriceCandidate = p;
                    }
                }
            }
        }

        if (bestVolCandidate > currentPocVolumeVal) { 
            if (DEBUG && (bestPriceCandidate != pocPrice || bestVolCandidate != pocVol)) {
                logDebugFormat("POC updated by ensurePocMatchesHighestVolume to %d with volume %d (was %d with vol %d)",
                     bestPriceCandidate, bestVolCandidate, pocPrice, pocVol);
            }
            pocPrice = bestPriceCandidate;
            pocVol = bestVolCandidate;
        } else if (pocPrice == Integer.MIN_VALUE && bestVolCandidate > 0) {
            logDebugFormat("POC initialized by ensurePocMatchesHighestVolume to %d with volume %d",
                 bestPriceCandidate, bestVolCandidate);
            pocPrice = bestPriceCandidate;
            pocVol = bestVolCandidate;
        }
    }

    /* ===== VolumeStats (Optimized) ===== */
    private static final class VolumeStats {
        final double mean, stdDev, median, mad;

        private VolumeStats(double m, double s, double med, double ma) {
            mean = m;
            stdDev = s;
            median = med;
            mad = ma;
        }

        static VolumeStats of(long[] vols) { 
            List<Long> nonZeroVolumes = new ArrayList<>(); 
            long sum = 0;
            for (long v : vols) {
                if (v > 0) {
                    nonZeroVolumes.add(v);
                    sum += v;
                }
            }
            return calculateStatsFromList(nonZeroVolumes, sum);
        }

        static VolumeStats of(List<Long> nonZeroVolumes) {
            if (nonZeroVolumes.isEmpty())
                return new VolumeStats(0, 0, 0, 0);
            long sum = 0;
            for (long v : nonZeroVolumes)
                sum += v; 
            return calculateStatsFromList(new ArrayList<>(nonZeroVolumes), sum); 
        }

        private static VolumeStats calculateStatsFromList(List<Long> nonZeroVolumesForStats, long sum) {
            int cnt = nonZeroVolumesForStats.size();
            if (cnt == 0)
                return new VolumeStats(0.0, 0.0, 0.0, 0.0);

            double meanVal = (double) sum / cnt;
            double var = 0.0;
            for (long v : nonZeroVolumesForStats)
                var += (v - meanVal) * (v - meanVal);
            double stdDevVal = Math.sqrt(var / cnt);

            Collections.sort(nonZeroVolumesForStats); 

            double medianVal;
            if (cnt % 2 == 1)
                medianVal = nonZeroVolumesForStats.get(cnt / 2);
            else
                medianVal = (nonZeroVolumesForStats.get(cnt / 2 - 1) + nonZeroVolumesForStats.get(cnt / 2)) / 2.0;

            List<Double> absDeviations = new ArrayList<>(cnt); 
            for (long v : nonZeroVolumesForStats)
                absDeviations.add(Math.abs(v - medianVal));
            Collections.sort(absDeviations);

            double madVal;
            if (cnt % 2 == 1)
                madVal = absDeviations.get(cnt / 2);
            else
                madVal = (absDeviations.get(cnt / 2 - 1) + absDeviations.get(cnt / 2)) / 2.0;

            return new VolumeStats(meanVal, stdDevVal, medianVal, madVal);
        }
    }

    public static class VPASnapshot {
        private final int pocPriceTicks;
        private final int valueAreaHighTicks;
        private final int valueAreaLowTicks;

        private final double pocPrice;
        private final double valueAreaHigh;
        private final double valueAreaLow;

        private final List<VolumeNodeSnapshot> hvnNodes;
        private final List<VolumeNodeSnapshot> lvnNodes;
        private final long timestampNanos;
        private final boolean isReady;
        private final long sessionVolume;

        public static class VolumeNodeSnapshot {
            public final int startPriceTicks, endPriceTicks, centerPriceTicks;
            public final double centerPrice;
            public final long volume;

            public VolumeNodeSnapshot(int spt, int ept, int cpt, double cp, long v) {
                startPriceTicks = spt;
                endPriceTicks = ept;
                centerPriceTicks = cpt;
                centerPrice = cp;
                volume = v;
            }

            public int getStartPriceTicks() {
                return startPriceTicks;
            }

            public int getEndPriceTicks() {
                return endPriceTicks;
            }

            public int getCenterPriceTicks() {
                return centerPriceTicks;
            }

            public double getCenterPrice() {
                return centerPrice;
            }

            public long getVolume() {
                return volume;
            }

            @Override
            public boolean equals(Object o) {
                if (this == o)
                    return true;
                if (o == null || getClass() != o.getClass())
                    return false;
                VolumeNodeSnapshot vn = (VolumeNodeSnapshot) o;
                return startPriceTicks == vn.startPriceTicks && endPriceTicks == vn.endPriceTicks &&
                        centerPriceTicks == vn.centerPriceTicks && Double.compare(vn.centerPrice, centerPrice) == 0
                        && volume == vn.volume;
            }

            @Override
            public int hashCode() {
                return Objects.hash(startPriceTicks, endPriceTicks, centerPriceTicks, centerPrice, volume);
            }
        }

        public VPASnapshot(int pocPT, int vahPT, int valPT, List<VolumeProfileAnalyzer.VolumeNode> hvnIn,
                List<VolumeProfileAnalyzer.VolumeNode> lvnIn,
                long tsN, boolean ready, double pips, long sessVol) {
            pocPriceTicks = pocPT;
            valueAreaHighTicks = vahPT;
            valueAreaLowTicks = valPT;
            timestampNanos = tsN;
            isReady = ready;
            sessionVolume = sessVol;
            pocPrice = pocPriceTicks * pips;
            valueAreaHigh = valueAreaHighTicks * pips;
            valueAreaLow = valueAreaLowTicks * pips;
            hvnNodes = new ArrayList<>();
            if (hvnIn != null)
                for (VolumeNode n : hvnIn)
                    hvnNodes.add(new VolumeNodeSnapshot(n.startPrice, n.endPrice, n.centerPrice, n.centerPrice * pips,
                            n.cumVolume));
            lvnNodes = new ArrayList<>();
            if (lvnIn != null)
                for (VolumeNode n : lvnIn)
                    lvnNodes.add(new VolumeNodeSnapshot(n.startPrice, n.endPrice, n.centerPrice, n.centerPrice * pips,
                            n.cumVolume));
        }

        public static VPASnapshot notReady(long tsN) {
            return new VPASnapshot(0, 0, 0, Collections.emptyList(), Collections.emptyList(), tsN, false, 0.01, 0L);
        }

        public int getPocPriceTicks() {
            return pocPriceTicks;
        }

        public int getValueAreaHighTicks() {
            return valueAreaHighTicks;
        }

        public int getValueAreaLowTicks() {
            return valueAreaLowTicks;
        }

        public double getPocPrice() {
            return pocPrice;
        }

        public double getValueAreaHigh() {
            return valueAreaHigh;
        }

        public double getValueAreaLow() {
            return valueAreaLow;
        }

        public List<VolumeNodeSnapshot> getHvnNodes() {
            return Collections.unmodifiableList(hvnNodes);
        }

        public List<VolumeNodeSnapshot> getLvnNodes() {
            return Collections.unmodifiableList(lvnNodes);
        }

        public long getTimestampNanos() {
            return timestampNanos;
        }

        public boolean isReady() {
            return isReady;
        }

        public long getSessionVolume() {
            return sessionVolume;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o)
                return true;
            if (o == null || getClass() != o.getClass())
                return false;
            VPASnapshot s = (VPASnapshot) o;
            return pocPriceTicks == s.pocPriceTicks && valueAreaHighTicks == s.valueAreaHighTicks
                    && valueAreaLowTicks == s.valueAreaLowTicks && timestampNanos == s.timestampNanos &&
                    isReady == s.isReady && sessionVolume == s.sessionVolume
                    && Double.compare(s.pocPrice, pocPrice) == 0 && Double.compare(s.valueAreaHigh, valueAreaHigh) == 0
                    &&
                    Double.compare(s.valueAreaLow, valueAreaLow) == 0 && Objects.equals(hvnNodes, s.hvnNodes)
                    && Objects.equals(lvnNodes, s.lvnNodes);
        }

        @Override
        public int hashCode() {
            return Objects.hash(pocPriceTicks, valueAreaHighTicks, valueAreaLowTicks, pocPrice, valueAreaHigh,
                    valueAreaLow, hvnNodes, lvnNodes, timestampNanos, isReady, sessionVolume);
        }
    }
    public VPASnapshot getSnapshot(double pips) {
        synchronized (updateLock) {
            if (!this.sufficientDataForProfile) {
                return VPASnapshot.notReady(this.lastRefreshTimestampNanos);
            }
            return new VPASnapshot(
                    this.pocPrice, this.developingVAH, this.developingVAL,
                    this.hvnNodes, this.lvnNodes,
                    this.lastRefreshTimestampNanos, this.isReady(),
                    pips, this.sessionVolume);
        }
    }
    public InputDataHealthTrackerV2.Status getDataHealthStatus() {
        return (inputDataHealthTracker != null)
                ? inputDataHealthTracker.getStatus()
                : InputDataHealthTrackerV2.Status.INITIALIZE;
    }

    public boolean isReady() {
        boolean healthOk = !healthMonitoringEnabled ||
                (inputDataHealthTracker != null && inputDataHealthTracker.isHealthy()) ||
                (inputDataHealthTracker == null && !healthMonitoringEnabled);
        synchronized (updateLock) {
            return sufficientDataForProfile && healthOk;
        }
    }

    public void setHealthMonitoringEnabled(boolean enabled) {
        this.healthMonitoringEnabled = enabled;
    }
}