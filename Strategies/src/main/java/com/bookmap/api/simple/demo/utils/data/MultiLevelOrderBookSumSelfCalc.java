package com.bookmap.api.simple.demo.utils.data;

import java.util.Map;
import java.util.TreeMap;
import java.util.ArrayList;
import velox.api.layer1.common.Log;

/**
 * A multi-level order book implementation that tracks the sum of order
 * sizes for different price level depths.
 *
 * Performance-optimized version using only TreeMaps.
 */
public class MultiLevelOrderBookSumSelfCalc {

    // Debug mode flag - controls logging verbosity (set to false by default for performance)
    private static final boolean DEBUG_MODE = false;

    // Sorted maps for efficiently finding the best N levels
    protected final TreeMap<Integer, Integer> sortedBids = new TreeMap<>((a, b) -> b - a); // reverse for bids (highest first)
    protected final TreeMap<Integer, Integer> sortedAsks = new TreeMap<>(); // ascending for asks (lowest first)

    // The maximum number of price levels to include for each depth level
    protected final int[] maxLevels;

    // The number of top levels to skip when summing (defaults to 0)
    protected int skipLevels = 0;

    protected int oldSize = 0;

    // Threshold for triggering cleanup checks - only check every N updates
    private static final int CLEANUP_THRESHOLD = 1000;
    private int updateCounter = 0;

    // Pre-allocated collections for cleanup to avoid allocations during execution
    private final ArrayList<Integer> keysToRemove = new ArrayList<>(50);

    /**
     * Constructor that initializes the order book with specified levels.
     *
     * @param maxLevels An array of different level depths to track
     */
    public MultiLevelOrderBookSumSelfCalc(int[] maxLevels) {
        if (maxLevels == null || maxLevels.length == 0) {
            throw new IllegalArgumentException("Must specify at least one level depth");
        }

        this.maxLevels = maxLevels;
        if (DEBUG_MODE) {
            Log.info("MultiLevelOrderBookSum initialized with levels: " +
                     maxLevels[0] + ", " + (maxLevels.length > 1 ? maxLevels[1] : "N/A") + ", " +
                     (maxLevels.length > 2 ? maxLevels[2] : "N/A"));
        }
    }

    /**
     * Constructor that initializes the order book with specified levels and skip level count.
     *
     * @param maxLevels An array of different level depths to track
     * @param skipLevels Number of top levels to skip when summing (default 0)
     */
    public MultiLevelOrderBookSumSelfCalc(int[] maxLevels, int skipLevels) {
        this(maxLevels);
        this.skipLevels = Math.max(0, skipLevels); // Ensure skipLevels is non-negative
        if (DEBUG_MODE) {
            Log.info("MultiLevelOrderBookSum configured to skip first " + this.skipLevels + " levels");
        }
    }

    /**
     * Set the number of top levels to skip when summing.
     *
     * @param skipLevels Number of top levels to skip
     */
    public void setSkipLevels(int skipLevels) {
        this.skipLevels = Math.max(0, skipLevels); // Ensure skipLevels is non-negative
        if (DEBUG_MODE) {
            Log.info("MultiLevelOrderBookSum updated to skip first " + this.skipLevels + " levels");
        }
    }

    /**
     * Get the current number of top levels being skipped.
     *
     * @return Number of top levels being skipped
     */
    public int getSkipLevels() {
        return this.skipLevels;
    }

    /**
     * Get the sorted map of bid prices and sizes
     * @return TreeMap of bid prices (highest first) and sizes
     */
    public synchronized TreeMap<Integer, Integer> getSortedBids() {
        return this.sortedBids;
    }

    /**
     * Get the sorted map of ask prices and sizes
     * @return TreeMap of ask prices (lowest first) and sizes
     */
    public synchronized TreeMap<Integer, Integer> getSortedAsks() {
        return this.sortedAsks;
    }

    /**
     * Handle a depth update for the order book.
     * Performance-optimized implementation using only TreeMaps.
     * 
     * @param isBid true for bid (buy) orders, false for ask (sell) orders
     * @param price the price level
     * @param size the new size at this price level (0 to remove)
     * @return the previous size at this price level
     */
    public void onDepth(boolean isBid, int price, int size) {
        // Fast rejection of invalid prices - avoids synchronization overhead
        if (price <= 0) {
            // Always log critical errors even in non-debug mode
            Log.error("Invalid price in onDepth: " + price);
           // return 0;
        }

        // Fast rejection of suspicious sizes - avoids synchronization overhead
        if (size < 0 || size > Integer.MAX_VALUE / 10) {
            if (size != 0) { // Size 0 is valid for removal
                Log.error("Rejecting suspicious size value: " + size + " for " + (isBid ? "bid" : "ask"));
               // return 0;
            }
        }

        // Use locals for better performance in synchronized block
        final TreeMap<Integer, Integer> sortedMap = isBid ? sortedBids : sortedAsks;
        oldSize = 0;

        // Use synchronized at most granular level possible
        synchronized(this) {
            try {
                // Get previous size (perform this lookup only once)
                Integer prevSize = sortedMap.get(price);
                oldSize = (prevSize != null) ? prevSize : 0;

                // Special handling for size=0 (cancellations) - optimized path
                if (size == 0) {
                    if (prevSize != null) {
                        sortedMap.remove(price);
                    }
                } else {
                    // Update map directly
                    sortedMap.put(price, size);
                }

                // Periodically clean up entries with non-positive values
                if (++updateCounter >= CLEANUP_THRESHOLD) {
                    updateCounter = 0;
                    cleanupNonPositiveEntries(isBid);
                }
            } catch (Exception e) {
                // Always log exceptions even in non-debug mode
                Log.error("Exception in onDepth(" + isBid + ", " + price + ", " + size + "): " + e.getMessage(), e);
            }
        }
        
       // return oldSize;
    }

    /**
     * Clean up entries with zero or negative values
     */
    private void cleanupNonPositiveEntries(boolean isBid) {
        final TreeMap<Integer, Integer> sortedMap = isBid ? sortedBids : sortedAsks;

        try {
            // Reuse pre-allocated list to avoid allocation during critical operation
            keysToRemove.clear();

            // Find entries with zero or negative values
            for (Map.Entry<Integer, Integer> entry : sortedMap.entrySet()) {
                if (entry.getValue() <= 0) {
                    keysToRemove.add(entry.getKey());
                }
            }

            // Remove identified keys (batch remove for better performance)
            int removeSize = keysToRemove.size();
            for (int i = 0; i < removeSize; i++) {
                sortedMap.remove(keysToRemove.get(i));
            }

            if (removeSize > 0 && DEBUG_MODE) {
                Log.info("Cleaned up " + removeSize + " non-positive entries for " + (isBid ? "bid" : "ask") + " side");
            }
        } catch (Exception e) {
            // Always log cleanup errors even in non-debug mode
            Log.error("Error attempting to clean up non-positive entries: " + e.getMessage());
        }
    }

    /**
     * Get the aggregate sum of order sizes for all price levels up to the specified depth,
     * filtering by size according to provided parameters.
     * Performance-optimized implementation.
     *
     * @param isBid true for bid (buy) side, false for ask (sell) side
     * @param levelIndex the index into the maxLevels array specifying which depth to use
     * @param minSize minimum order size to include in the sum (inclusive)
     * @param maxSize maximum order size to include in the sum (inclusive)
     * @return the sum of order sizes
     */
    public synchronized int getSizeSum(boolean isBid, int levelIndex, int minSize, int maxSize) {
        // Basic validation - fast early exit
        if (levelIndex < 0 || levelIndex >= maxLevels.length) {
            if (DEBUG_MODE) {
                Log.error("Level index out of bounds: " + levelIndex + " (max: " + (maxLevels.length-1) + ")");
            }
            return 0;
        }

        // Use local reference to avoid field lookup in loop
        final TreeMap<Integer, Integer> sortedMap = isBid ? sortedBids : sortedAsks;

        // Fast path for empty maps
        if (sortedMap.isEmpty()) {
            return 0;
        }

        // Normalize filter parameters once - avoid redundant max operations in loop
        final int effectiveMinSize = Math.max(0, minSize);
        final int effectiveMaxSize = Math.max(effectiveMinSize, maxSize);
        final int maxLevel = maxLevels[levelIndex];
        final int effectiveSkipLevels = skipLevels;

        try {
            // Optimized summation algorithm - minimize operations in loop
            int sum = 0;
            int count = 0;
            int skipped = 0;

            // Use direct iteration on map entries for better performance
            for (Map.Entry<Integer, Integer> entry : sortedMap.entrySet()) {
                int size = entry.getValue();

                // Skip non-positive sizes - fast check
                if (size <= 0) continue;

                // Skip levels according to configuration
                if (skipped < effectiveSkipLevels) {
                    skipped++;
                    continue;
                }

                // Apply size filtering - combine conditions for better branch prediction
                if (size >= effectiveMinSize && size <= effectiveMaxSize) {
                    sum += size;
                }

                // Check if we've reached maxLevel - exit loop early
                if (++count >= maxLevel) break;
            }

            return sum;
        } catch (Exception e) {
            // Always log exceptions even in non-debug mode
            Log.error("Exception in getSizeSum: " + e.getMessage());
            return 0;
        }
    }

    /**
     * Original getSizeSum method maintained for backward compatibility.
     * This version uses no size filtering.
     */
    public int getSizeSum(boolean isBid, int levelIndex) {
        return getSizeSum(isBid, levelIndex, 0, Integer.MAX_VALUE);
    }

    /**
     * Simplified version for backward compatibility - uses level index 0.
     */
    public int getSizeSum(boolean isBid) {
        return getSizeSum(isBid, 0);
    }

    /**
     * Performance-optimized method to get size sums for all levels in one pass.
     * This avoids multiple traversals of the order book for each level.
     *
     * @param isBid true for bid side, false for ask side
     * @param minSize minimum order size to include
     * @param maxSize maximum order size to include
     * @return array of sums corresponding to each level in maxLevels
     */
    public synchronized int[] getAllSizeSums(boolean isBid, int minSize, int maxSize) {
        final int levelCount = maxLevels.length;
        int[] results = new int[levelCount];

        final TreeMap<Integer, Integer> sortedMap = isBid ? sortedBids : sortedAsks;

        // Fast path for empty maps
        if (sortedMap.isEmpty()) {
            return results; // Return array of zeros
        }

        // Normalize filter parameters once
        final int effectiveMinSize = Math.max(0, minSize);
        final int effectiveMaxSize = Math.max(effectiveMinSize, maxSize);
        final int effectiveSkipLevels = skipLevels;

        try {
            int count = 0;
            int skipped = 0;

            // Single pass through the order book
            for (Map.Entry<Integer, Integer> entry : sortedMap.entrySet()) {
                int size = entry.getValue();

                // Skip non-positive sizes
                if (size <= 0) continue;

                // Skip levels according to configuration
                if (skipped < effectiveSkipLevels) {
                    skipped++;
                    continue;
                }

                // Only process if within size filter range
                if (size >= effectiveMinSize && size <= effectiveMaxSize) {
                    // Update each level that this price affects
                    for (int i = 0; i < levelCount; i++) {
                        if (count < maxLevels[i]) {
                            results[i] += size;
                        }
                    }
                }

                // Increment count and check if we've reached all max levels
                count++;

                // Optimization: break early if we've processed enough levels for all maxLevels
                boolean allLevelsComplete = true;
                for (int i = 0; i < levelCount; i++) {
                    if (count < maxLevels[i]) {
                        allLevelsComplete = false;
                        break;
                    }
                }

                if (allLevelsComplete) break;
            }

            return results;
        } catch (Exception e) {
            Log.error("Exception in getAllSizeSums: " + e.getMessage());
            return results; // Return array of zeros on error
        }
    }

    /**
     * Get the map of bid sizes for each price level.
     */
    protected synchronized Map<Integer, Integer> getBidSizes() {
        return sortedBids;
    }

    /**
     * Get the map of ask sizes for each price level.
     */
    protected synchronized Map<Integer, Integer> getAskSizes() {
        return sortedAsks;
    }

    /**
     * Get the size at a specific price level.
     * Performance-optimized implementation using TreeMap.
     *
     * @param isBid true for bid side, false for ask side
     * @param price the price level
     * @return the size at the specified price level, or 0 if no orders exist at that level
     */
    public synchronized int getSizeAt(boolean isBid, int price) {
        // Use local reference for field lookup optimization
        final TreeMap<Integer, Integer> sortedMap = isBid ? sortedBids : sortedAsks;
        Integer size = sortedMap.get(price);
        // Use ternary for branch optimization
        return (size != null) ? size : 0;
    }

    public synchronized int getOldSize() {
        return oldSize;
    }
}