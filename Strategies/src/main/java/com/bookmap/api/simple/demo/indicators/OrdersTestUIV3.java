package com.bookmap.api.simple.demo.indicators;

import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.CountDownLatch;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import velox.api.layer1.data.InstrumentInfo;
import velox.api.layer1.data.OrderDuration;
import velox.api.layer1.common.Log;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.prefs.Preferences;
import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import com.bookmap.api.simple.demo.indicators.OrderSenderControllerV2.*;
import java.util.Collection; // Add this import
import com.bookmap.api.simple.demo.indicators.VerticalGauge; // Import the VerticalGauge class
import com.bookmap.api.simple.demo.indicators.BBOAnalysedDataInterface; // Import the new interface
import com.bookmap.api.simple.demo.indicators.OrderUIDataListener; // Import OrderUIDataListener
import java.lang.reflect.InvocationTargetException;   // ← new
import com.bookmap.api.simple.demo.indicators.DrawdownGuard;
import velox.api.layer1.common.RunModeHelper;
import com.bookmap.api.simple.demo.indicators.NubiaHealthStatusUI; // Import the NubiaHealthStatusUI class
import java.util.Arrays;

 

public class OrdersTestUIV3 implements BBOAnalysedDataInterface, OrderUIDataListener {
    
    // ═══ NEW: UI STATE MANAGEMENT ═══
    public enum UIState {
        INITIALIZING,
        RUNNING,
        SHUTTING_DOWN,
        DISPOSED
    }
    
    private static final AtomicReference<UIState> uiState = new AtomicReference<>(UIState.INITIALIZING);
    private static final CountDownLatch shutdownLatch = new CountDownLatch(1);
    private static volatile boolean shutdownInitiated = false;
    
    private static final Object LOCK = new Object();  // Add lock for thread safety
    private static OrdersTestUIV3 instance = null;
    private static final Object instanceLock = new Object();
    private static final Map<String, OrderSenderControllerV2> registeredControllers = new ConcurrentHashMap<>();
    private static JFrame frame = null;
    
    // ═══ REMOVED: Replace boolean flags with state machine ═══
    // private static boolean initialized = false;
    // private static volatile boolean isShuttingDown = false;
    
    private final InstrumentInfo instrumentInfo;
    private final OrderSenderControllerV2 orderSender;
    private JTable positionsTable;
    private DefaultTableModel tableModel;
    private JTextArea logTextArea;  // Separate text area for logs
    private JTextField globalPnLField; // Renamed from priceField
    private JTextField displayPriceField;  // Add field reference
    private JTextField aliasField;  // Changed from instancePosField to aliasField
    private JTextField globalPosField;
    private ScheduledExecutorService scheduler;
    private ScheduledExecutorService fastScheduler; // ← NEW: Fast scheduler for 100ms updates
    private final List<Timer> delayTimers = new ArrayList<>();
    private long lastUpdateTime = 0;
    private static final long MIN_UPDATE_INTERVAL_MS = 500; // Minimum time between updates
    // --- Time Gate fields & colors ---
    private JTextField timeGateField;
    private JTextField capsField;

    private static final Color GATE_ACTIVE  = new Color(120,200,120);
    private static final Color GATE_THIN    = new Color(255,235,120);
    private static final Color GATE_CLOSE   = new Color(255,190,120);
    private static final Color GATE_BLOCKED = new Color(220,100,100);
    // Input fields for order offsets
    private JTextField limitOffsetField;
    private JTextField stopOffsetField;
    private JTextField ocoLimitOffsetField;
    private JTextField ocoStopOffsetField;
    
    // Highlight colors for positions
    private static final Color LONG_POSITION_COLOR = new Color(0, 128, 0); // Green for long
    private static final Color SHORT_POSITION_COLOR = new Color(220, 20, 60); // Crimson for short
    private static final Color NEUTRAL_POSITION_COLOR = new Color(220, 220, 220); // Light gray for neutral

    // Button colors that provide good contrast with black text
    private static final Color BUY_BUTTON_COLOR = new Color(180, 220, 250); // Darker blue for buy
    private static final Color SELL_BUTTON_COLOR = new Color(255, 215, 190); // Darker orange for sell
    private static final Color INSTANCE_FLATTEN_COLOR = new Color(235, 235, 255); // Even lighter purple for instance
    private static final Color GLOBAL_FLATTEN_COLOR = new Color(255, 225, 225); // Even lighter red for global
    private static final Color REVERSE_BUTTON_COLOR = new Color(216, 191, 216); // Light orchid for reverse

    // Keep track of pending update request to batch updates
    private volatile boolean updatePending = false;
    private final Object updateLock = new Object();
    
    // Flag to avoid setting offsets multiple times
    private boolean offsetsInitialized = false;

    // Add at class level after other declarations
    private JScrollPane positionsScrollPane;
    private JLabel statusWarningLabel; // NEW Name

    // Add missing interface definition before any methods
    private interface OffsetButtonHandler {
        void setupButtons(JTextField offsetField, JButton buyButton, JButton sellButton);
    }

    // Replace LT_QD_EMA_5_2_0_cleanedup7 with Object for generic strategy reference
    private Object strategyReference;

    // For internal ATR calculation if strategyReference is null
    private static final int ATR_PERIOD = 14*60;
    private final List<Double> priceHistory = new ArrayList<>(ATR_PERIOD + 2);

    // Map to store order IDs for tracking
    private final Map<String, String> orderClientIdToOrderId = new ConcurrentHashMap<>();
    private final Map<String, Long> orderStartTimes = new ConcurrentHashMap<>();

    // Add a debug flag for logging
    private static final boolean DEBUG_UI = false;
    private static final boolean DEBUG_SCHEDULER = false; // NEW: Debug flag for scheduler issues

    // Add fields for live trading toggle and label
    private JLabel liveTradingStatusLabel;
    private JToggleButton liveTradingToggle;
    private java.util.function.Consumer<Boolean> liveTradingToggleListener;

    // Add these color constants at the top of the class
    private static final Color ENABLED_BG = new Color(120, 200, 120);
    private static final Color DISABLED_BG = new Color(220, 100, 100);

    // --- Trading disable reason and timer fields ---
    private String tradingDisabledReason = "";
    private long tradingDisabledUntil = 0L; // 0 means indefinite/manual

    // --- Bookmap time for countdowns ---
    private long currentBookmapMillis = 0L;

    // Add fields for Account Balance and Active Orders row
    private JTextField accountBalanceField;
    private JLabel activeOrdersLabel;

    private JScrollPane logScrollPane;
    private HorizontalGauge bidAskDiffGauge;
    private HorizontalGauge diffGauge;

    private double globalUnrealizedPnL = 0.0;
    private double globalRealizedPnL = 0.0;
    private boolean isRealtime = false;
    
    // Add new UI fields
    private JTextField globalPositionLimitField;
    private JTextField availableLiquidityField;
    private JTextField runModeField; // NEW: RunMode field to replace Performance Dashboard
    private JTextField orderCapField; // NEW: Separate field for order cap
    private JToggleButton healthStatusToggle; // NEW: Health Status toggle button reference

    // ─── CACHED DATA FIELDS FOR BATCHED UI UPDATES ───
    private volatile double cachedDisplayPrice = 0.0;
    private volatile double cachedGlobalPnL = 0.0;
    private volatile int cachedInstancePosition = 0;
    private volatile int cachedGlobalPosition = 0;
    private volatile double cachedAccountBalance = 0.0;
    private volatile int cachedWorkingSells = 0;
    private volatile int cachedWorkingBuys = 0;
    private volatile int cachedAccountPosition = 0;
    private volatile String cachedAlias = "";
    private volatile boolean cachedLiveTradingEnabled = false;
    private volatile OrderSenderControllerV2.TimeGateState cachedTimeGateState = OrderSenderControllerV2.TimeGateState.ACTIVE;
    private volatile int cachedPosCap = 0;
    private volatile int cachedOrdCap = 0;
    private volatile long cachedSecsToNext = 0;
    
    // Add new cached variables
    private volatile int cachedGlobalPositionLimit = 0;
    private volatile double cachedAvailableLiquidity = 0.0;
    
    // Flags to indicate what needs UI refresh
    private volatile boolean needsPriceUpdate = false;
    private volatile boolean needsPositionUpdate = false;
    private volatile boolean needsAccountUpdate = false;
    private volatile boolean needsAliasUpdate = false;
    private volatile boolean needsLiveTradingUpdate = false;
    private volatile boolean needsTimeGateUpdate = false;
    
    // Add new update flags
    private volatile boolean needsGlobalPositionLimitUpdate = false;
    private volatile boolean needsAvailableLiquidityUpdate = false;
    private volatile boolean needsRunModeUpdate = false; // NEW: RunMode update flag

    // ─── STARTUP PHASE CONTROL ───
    private volatile boolean startupComplete = false;
    private volatile boolean hasInitialPriceData = false;
    private volatile boolean hasInitialPositionData = false;
    private volatile long startupTimestamp = 0L;

    /**
     * Set the trading disabled reason and (optionally) a timestamp until which trading is disabled.
     * @param reason The reason string (e.g. "Profit Target Met - Auto-Disabled").
     * @param disableUntilMillis Epoch ms when trading will be re-enabled, or 0 for indefinite/manual disables.
     */
    @Override
    public void setTradingDisabledReason(String reason, long disableUntilMillis) {
        this.tradingDisabledReason = reason != null ? reason : "";
        this.tradingDisabledUntil = disableUntilMillis;
        
        // Ensure UI update happens on EDT with null safety
        Runnable updateOperation = () -> {
            if (statusWarningLabel != null) {
                updateStatusWarningLabel();
            }
        };
        
        // Use direct EDT invocation during shutdown, safeEDTOperation otherwise
        UIState currentState = uiState.get();
        if (currentState == UIState.SHUTTING_DOWN) {
            if (SwingUtilities.isEventDispatchThread()) {
                updateOperation.run();
            } else {
                SwingUtilities.invokeLater(updateOperation);
            }
        } else {
            safeEDTOperation(updateOperation);
        }
    }

    /**
     * Update the current Bookmap time (in ms) for countdown calculations.
     * This should be called by the controller on every Bookmap timestamp update.
     */
    @Override
    public void setCurrentBookmapMillis(long millis) {
        this.currentBookmapMillis = millis;
    }

    public void updateBidAskDiffGauge(double value) {
        Runnable updateOperation = () -> {
            if (bidAskDiffGauge != null) {
                final int intValue = (int) Math.round(value);
                bidAskDiffGauge.setValue(intValue);
            }
        };
        
        // Use direct EDT invocation during shutdown, safeEDTOperation otherwise
        UIState currentState = uiState.get();
        if (currentState == UIState.SHUTTING_DOWN) {
            if (SwingUtilities.isEventDispatchThread()) {
                updateOperation.run();
            } else {
                SwingUtilities.invokeLater(updateOperation);
            }
        } else {
            safeEDTOperation(updateOperation);
        }
    }

    public void updateBboPriceDiffGauge(double value) {
        Runnable updateOperation = () -> {
            if (diffGauge != null) {
                final int intValue = (int) Math.round(value);
                diffGauge.setValue(intValue);
            }
        };
        
        // Use direct EDT invocation during shutdown, safeEDTOperation otherwise
        UIState currentState = uiState.get();
        if (currentState == UIState.SHUTTING_DOWN) {
            if (SwingUtilities.isEventDispatchThread()) {
                updateOperation.run();
            } else {
                SwingUtilities.invokeLater(updateOperation);
            }
        } else {
            safeEDTOperation(updateOperation);
        }
    }

    // Implementation of BBOAnalysedDataInterface
    @Override
    public void onBboDataUpdate(double bidAskDiff, double bboPriceDiff) {
        updateBidAskDiffGauge(bidAskDiff);
        if (!Double.isNaN(bboPriceDiff)) {
            updateBboPriceDiffGauge(bboPriceDiff);
        }
    }

    // Update the constructor to accept the strategy reference
    public OrdersTestUIV3(InstrumentInfo instrumentInfo, OrderSenderControllerV2 orderSender, Object strategyReference) {
        if (instrumentInfo == null || orderSender == null) {
            throw new IllegalArgumentException("InstrumentInfo and OrderSender cannot be null");
        }
        
        // ═══ MODIFIED: Check state instead of boolean flag ═══
        if (uiState.get() == UIState.SHUTTING_DOWN || uiState.get() == UIState.DISPOSED) {
            throw new IllegalStateException("Cannot create UI instance during shutdown or after disposal");
        }
        
        this.instrumentInfo = instrumentInfo;
        this.orderSender = orderSender;
        this.strategyReference = strategyReference;
        
        // Register with OrderSenderControllerV2 for UI updates
        if (orderSender != null) {
            try {
                appendStatus("OrdersTestUIV3: Initialized and ready to receive order updates");
            } catch (Exception e) {
                appendStatus("OrdersTestUIV3: Error setting up OrderSenderControllerV2: " + e.getMessage());
            }
        }
    }

// Enhanced getInstance method that handles DISPOSED state
public static OrdersTestUIV3 getInstance(InstrumentInfo instrumentInfo, 
                                        OrderSenderControllerV2 mainController, 
                                        Object strategyReference) {
    synchronized (instanceLock) {
        UIState currentState = uiState.get();
        
        // Allow creation from DISPOSED state (fresh start)
        if (currentState == UIState.SHUTTING_DOWN) {
            Log.info("OrdersTestUIV3: getInstance called during shutdown, returning null.");
            return null;
        }
        
        if (instance == null) {
            // Transition from DISPOSED to INITIALIZING if needed
            if (currentState == UIState.DISPOSED) {
                if (!uiState.compareAndSet(UIState.DISPOSED, UIState.INITIALIZING)) {
                    // State changed concurrently, check again
                    if (uiState.get() == UIState.SHUTTING_DOWN) {
                        Log.info("OrdersTestUIV3: State changed to SHUTTING_DOWN during creation");
                        return null;
                    }
                }
            }
            
            uiState.set(UIState.INITIALIZING);
            instance = new OrdersTestUIV3(instrumentInfo, mainController, strategyReference);
        }
        return instance;
    }
}

    // Static getter for registered controllers
    public static Map<String, OrderSenderControllerV2> getRegisteredControllers() {
        return registeredControllers;
    }

    // Register a controller with a name (algoName)
    public void registerController(OrderSenderControllerV2 controller, String name) {
        if (uiState.get() != UIState.RUNNING && uiState.get() != UIState.INITIALIZING) {
            if (DEBUG_UI) {
                Log.info("OrdersTestUIV3: Ignoring controller registration during invalid state: " + name);
            }
            return;
        }
        
        if (!registeredControllers.containsKey(name)) {
            // First add to registered controllers map
            registeredControllers.put(name, controller);
            
            // Add a row to the table for this controller if UI is initialized
            if (tableModel != null && uiState.get() == UIState.RUNNING) {
                // Use invokeAndWait to ensure table is updated synchronously
                final Runnable updateTable = () -> {
                    try {
                        int posLimit = controller.getMaxPositionSize();
                        boolean tradingEnabled = controller.isEnableLiveTrading();
                        String tradingEnabledStr = tradingEnabled ? "Yes" : "No";
                        
                        // Only add a row if the table is still valid
                        if (tableModel != null && uiState.get() == UIState.RUNNING) {
                            tableModel.addRow(new Object[]{
                                name, 
                                posLimit, 
                                tradingEnabledStr, 
                                0, // position
                                "0.0", // unrealized
                                "0.0", // realized
                                String.format("%.2f", controller.getMaxDailyLoss()), // loss limit
                                String.format("%.2f/%.2f", controller.getInstanceMaxDailyProfitRealized(), controller.getInstanceMaxDailyProfitTotal()), // profit limits
                                "-", // dd total
                                "-"  // dd unrealized
                            });
                        }
                    } catch (Exception e) {
                        if (DEBUG_UI) {
                            Log.info("OrdersTestUIV3: Error adding row for " + name + ": " + e.getMessage());
                        }
                    }
                };
                
                if (SwingUtilities.isEventDispatchThread()) {
                    updateTable.run();
                } else {
                    try {
                        SwingUtilities.invokeAndWait(updateTable);
                    } catch (Exception e) {
                        if (DEBUG_UI) {
                            Log.info("OrdersTestUIV3: Error in EDT for " + name + ": " + e.getMessage());
                        }
                    }
                }
            }
            
            // Debug log for registration
            if (DEBUG_UI) {
                Log.info("OrdersTestUIV3: Registered controller: " + name);
            }
        } else {
            // Debug log for duplicate registration
            if (DEBUG_UI) {
                Log.info("OrdersTestUIV3: Controller already registered for: " + name);
            }
        }
    }

    public void show() {
        /* Only change state under LOCK — the heavy work happens outside. */
        synchronized (LOCK) {
            if (uiState.get() == UIState.RUNNING) {
                return;
            }
            uiState.set(UIState.RUNNING);
        }
        
        /* Check if we're already on the EDT to avoid "Cannot call invokeAndWait from the event dispatcher thread" error */
        if (SwingUtilities.isEventDispatchThread()) {
            // Already on EDT, call directly
            createAndShowUI();
        } else {
            /* Build UI synchronously on the EDT so logic after show() can rely on a ready frame. */
            try {
                SwingUtilities.invokeAndWait(this::createAndShowUI);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                Log.error("OrdersTestUIV3: UI build interrupted", ie);
            } catch (InvocationTargetException ite) {
                Log.error("OrdersTestUIV3: UI build failed", ite.getCause());
            }
        }
        
        // ═══ NEW: Mark as fully running after UI creation ═══
        synchronized (LOCK) {
            if (uiState.get() == UIState.INITIALIZING) {
                uiState.set(UIState.RUNNING);
            }
        }
    }

    private void createAndShowUI() {
        long initStart = System.currentTimeMillis();
        /*  Hold LOCK just long enough to create the frame; release it
         *  before the heavyweight Swing construction so strategy logic
         *  in other threads does not stall.                                */
        JFrame localFrame;
        synchronized (LOCK) {
            if (frame != null) {
                frame.setVisible(true);
                frame.toFront();
                return;                        // already built
            }
            frame = new JFrame("Orders Test UI");
            localFrame = frame;               // local ref for use outside LOCK
        }  // 🔓 --------------- LOCK released here --------------------------

        /* ---------- heavy Swing work (panels, models, listeners) ---------- */
        localFrame.setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        localFrame.addWindowListener(new WindowAdapter() {
            @Override public void windowClosing(WindowEvent e) { cleanup(); }
        });
        
        try {
            localFrame.setAlwaysOnTop(false); // Don't keep UI on top
            
            // Create main panel with consistent spacing
            JPanel mainPanel = new JPanel(new BorderLayout(5, 8)); // Adjusted spacing for better balance
            mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10)); // Added padding
            
            // --- Create a vertical box for the top of the UI ---
            Box topBox = Box.createVerticalBox();
            // Add the live trading row first
            JPanel liveTradingTopRow = buildLiveTradingTopRowPanel();
            topBox.add(liveTradingTopRow);
            topBox.add(Box.createVerticalStrut(4)); // Small gap

            // Create input panel for price fields
            JPanel inputPanel = new JPanel(new BorderLayout(5, 5)); // Changed from GridLayout to BorderLayout
            inputPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 10, 5)); // Added padding between sections
            
            // Create top row with alias and display price (2 columns)
            JPanel topRow = new JPanel(new GridLayout(1, 2, 10, 0));
            // Create bottom row with global P&L, global position, and global position limit (3 columns)  
            JPanel bottomRow = new JPanel(new GridLayout(1, 3, 10, 0));
            // --- Third row: Time-Gate/Caps ---
            JPanel thirdRow = new JPanel(new GridLayout(1,3,10,0));
            // Add all rows to input panel
            inputPanel.add(topRow, BorderLayout.NORTH);
            inputPanel.add(bottomRow, BorderLayout.CENTER);
            // Setup all fields in the new layout structure
            setupPriceFields(topRow, bottomRow, thirdRow);
            inputPanel.add(thirdRow, BorderLayout.SOUTH);
            // Add input panel below live trading row
            topBox.add(inputPanel);

            // --- NEW: Add Account Balance & Active Orders row just below trade window ---
            JPanel infoRow = new JPanel(new GridLayout(1, 3, 10, 0)); // Restored to 3 columns
            accountBalanceField = new JTextField();
            accountBalanceField.setEditable(false);
            accountBalanceField.setBorder(BorderFactory.createTitledBorder("Account Balance"));
            
            // Keep Available Liquidity field here as originally requested
            availableLiquidityField = new JTextField();
            availableLiquidityField.setEditable(false);
            availableLiquidityField.setBorder(BorderFactory.createTitledBorder("Available Liquidity"));
            
            activeOrdersLabel = new JLabel();
            activeOrdersLabel.setOpaque(true);
            activeOrdersLabel.setBorder(BorderFactory.createTitledBorder("Active Orders"));
            
            infoRow.add(accountBalanceField);
            infoRow.add(availableLiquidityField);
            infoRow.add(activeOrdersLabel);
            topBox.add(infoRow);
            // --- END NEW ---

            // Add the topBox to the main panel's PAGE_START (NORTH)
            mainPanel.add(topBox, BorderLayout.NORTH);

            // Create positions panel
            JPanel positionsPanel = new JPanel(new BorderLayout());
            positionsPanel.setBorder(BorderFactory.createTitledBorder("Positions"));
            
            createPositionsTable();
            
            // Calculate appropriate table height based on number of rows (include space for total)
            int rowCount = registeredControllers.size() + 3; // main controller + additionals + separator + total
            int rowHeight = positionsTable.getRowHeight();
            int headerHeight = 25; // Estimate for table header
            int tableHeight = (rowCount * rowHeight) + headerHeight + 5; // Add a little extra space
            
            positionsScrollPane.setPreferredSize(new Dimension(600, tableHeight));
            positionsPanel.add(positionsScrollPane, BorderLayout.CENTER);
            mainPanel.add(positionsPanel, BorderLayout.CENTER);
            
            // Create button panel
            JPanel buttonPanel = new JPanel(new BorderLayout(5, 10)); // Increased vertical spacing
            buttonPanel.setBorder(BorderFactory.createEmptyBorder(10, 0, 0, 0)); // Add space above buttons
            
            // Panel for top row buttons (Market and Reverse)
            JPanel topButtonRow = new JPanel(new GridLayout(1, 3, 10, 0)); // Increased horizontal spacing
            
            // Add Reverse Positions button
            JButton reverseButton = new JButton("Reverse Positions");
            reverseButton.addActionListener(e -> {
                try {
                    boolean success = orderSender.instanceReversePositions();
                    if (success) {
                        playBeep(1);
                        appendStatus("Instance reverse positions executed");
                    } else {
                        appendStatus("Failed to reverse positions");
                    }
                } catch (Exception ex) {
                    logError("reverse positions", ex);
                }
            });
            
            // Create market buttons with equal width
            JButton marketBuyButton = new JButton("Market Buy");
            JButton marketSellButton = new JButton("Market Sell");
            
            // Create flatten buttons (will add to bottom row later)
            JButton instanceFlattenButton = new JButton("Main UI Flatten");
            JButton globalFlattenButton = new JButton("Global Flatten");
            
            // Make buttons consistent size and appearance
            Dimension marketButtonSize = new Dimension(120, 30); // Reduced from 140 to 120
            reverseButton.setPreferredSize(marketButtonSize);
            marketBuyButton.setPreferredSize(marketButtonSize);
            marketSellButton.setPreferredSize(marketButtonSize);
            instanceFlattenButton.setPreferredSize(marketButtonSize);
            globalFlattenButton.setPreferredSize(marketButtonSize);
            
            // Add colors to distinguish buttons
            reverseButton.setBackground(REVERSE_BUTTON_COLOR);
            reverseButton.setForeground(Color.BLACK);
            marketBuyButton.setBackground(BUY_BUTTON_COLOR);
            marketBuyButton.setForeground(Color.BLACK);
            marketSellButton.setBackground(SELL_BUTTON_COLOR);
            marketSellButton.setForeground(Color.BLACK);
            instanceFlattenButton.setBackground(INSTANCE_FLATTEN_COLOR);
            instanceFlattenButton.setForeground(Color.BLACK);
            globalFlattenButton.setBackground(GLOBAL_FLATTEN_COLOR);
            globalFlattenButton.setForeground(Color.BLACK);
            
            // Add top row buttons
            topButtonRow.add(reverseButton);
            topButtonRow.add(marketBuyButton);
            topButtonRow.add(marketSellButton);
            
            // Add top button row to panel
            JPanel marketButtonPanel = new JPanel(new BorderLayout(0, 10));
            marketButtonPanel.add(topButtonRow, BorderLayout.NORTH);
            
            buttonPanel.add(marketButtonPanel, BorderLayout.NORTH);
            
            // Add order type panels
            JPanel orderTypesPanel = new JPanel(new GridLayout(3, 1, 0, 8)); // Increased vertical gap for better separation
            orderTypesPanel.setBorder(BorderFactory.createEmptyBorder(5, 0, 5, 0)); // Added padding
            
            // Create limit panel
            JPanel limitPanel = createOrderPanel("Limit", "Buy", "Sell", (offsetField, buyButton, sellButton) -> {
                limitOffsetField = offsetField;
                // ... existing button actions ...
            });
            
            // Create stop panel
            JPanel stopPanel = createOrderPanel("Trailing Stop", "Buy", "Sell", (offsetField, buyButton, sellButton) -> {
                stopOffsetField = offsetField;
                // ... existing button actions ...
            });
            
            // Create OCO panel
            JPanel ocoPanel = createOrderPanel("OCO", "Buy", "Sell", (offsetField, buyButton, sellButton) -> {
                ocoStopOffsetField = offsetField;
                // Remove the action listeners from here, they're already added in createOrderPanel
            });
            
            orderTypesPanel.add(limitPanel);
            orderTypesPanel.add(stopPanel);
            orderTypesPanel.add(ocoPanel);
            
            // Create center panel with order types and flatten buttons
            JPanel centerPanel = new JPanel(new BorderLayout(0, 8));
            centerPanel.add(orderTypesPanel, BorderLayout.CENTER);
            
            // Create bottom row with flatten buttons
            JPanel bottomButtonRow = new JPanel(new GridLayout(1, 2, 10, 0)); // Increased spacing
            bottomButtonRow.setBorder(BorderFactory.createEmptyBorder(5, 0, 5, 0)); // Add padding
            
            // Add flatten buttons to bottom row
            instanceFlattenButton.setEnabled(false); // Temporarily disabled
            globalFlattenButton.setEnabled(false);   // Temporarily disabled
            bottomButtonRow.add(instanceFlattenButton);
            bottomButtonRow.add(globalFlattenButton);
            
            // Add bottom row to center panel
            centerPanel.add(bottomButtonRow, BorderLayout.SOUTH);
            
            // Add center panel to button panel
            buttonPanel.add(centerPanel, BorderLayout.CENTER);
            
            // Add Cancel All Orders buttons in a separate panel
            JPanel cancelAllOrdersPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 5));
            
            // Cancel All Orders button
            JButton cancelAllOrdersButton = new JButton("Cancel All Orders");
            cancelAllOrdersButton.setBackground(new Color(255, 200, 200)); // Light red
            cancelAllOrdersButton.setForeground(Color.BLACK);
            cancelAllOrdersButton.setPreferredSize(new Dimension(150, 30));
            cancelAllOrdersButton.setEnabled(false); // Temporarily disabled
            cancelAllOrdersButton.addActionListener(e -> {
                try {
                    // Only cancel real active orders, not mock/UI orders
                    int canceledCount = orderSender.cancelAllOrders();
                    playBeep(2);
                    appendStatus("Canceled " + canceledCount + " order(s)");
                } catch (Exception ex) {
                    logError("canceling all orders", ex);
                }
            });
            
            // Cancel All Buy Orders button
            JButton cancelAllBuyOrdersButton = new JButton("Cancel All Buys");
            cancelAllBuyOrdersButton.setBackground(new Color(200, 220, 255)); // Light blue
            cancelAllBuyOrdersButton.setForeground(Color.BLACK);
            cancelAllBuyOrdersButton.setPreferredSize(new Dimension(120, 30));
            cancelAllBuyOrdersButton.setEnabled(false); // Temporarily disabled
            cancelAllBuyOrdersButton.addActionListener(e -> {
                try {
                    int canceledCount = orderSender.cancelAllBuyOrders();
                    playBeep(1);
                    appendStatus("Canceled " + canceledCount + " buy order(s)");
                } catch (Exception ex) {
                    logError("canceling buy orders", ex);
                }
            });
            
            // Cancel All Sell Orders button
            JButton cancelAllSellOrdersButton = new JButton("Cancel All Sells");
            cancelAllSellOrdersButton.setBackground(new Color(255, 220, 200)); // Light orange
            cancelAllSellOrdersButton.setForeground(Color.BLACK);
            cancelAllSellOrdersButton.setPreferredSize(new Dimension(120, 30));
            cancelAllSellOrdersButton.setEnabled(false); // Temporarily disabled
            cancelAllSellOrdersButton.addActionListener(e -> {
                try {
                    int canceledCount = orderSender.cancelAllSellOrders();
                    playBeep(1);
                    appendStatus("Canceled " + canceledCount + " sell order(s)");
                } catch (Exception ex) {
                    logError("canceling sell orders", ex);
                }
            });
            
            // Add all cancel buttons to the panel
            cancelAllOrdersPanel.add(cancelAllBuyOrdersButton);
            cancelAllOrdersPanel.add(cancelAllOrdersButton);
            cancelAllOrdersPanel.add(cancelAllSellOrdersButton);
            
            // Add Performance Dashboard button to cancel buttons row
            JButton performanceButton = new JButton("Performance Dashboard");
            performanceButton.setBackground(new Color(230, 230, 255));
            performanceButton.setEnabled(true);
            performanceButton.setToolTipText("Show trading performance dashboard");
            performanceButton.setPreferredSize(new Dimension(160, 30)); // Slightly wider for longer text
            performanceButton.addActionListener(e -> {
                try {
                    if (orderSender != null) {
                        orderSender.showPerformanceDashboard();
                    }
                } catch (Exception ex) {
                    logError("opening performance dashboard", ex);
                }
            });
            cancelAllOrdersPanel.add(performanceButton);
            
            // Add Health Status toggle button after the Performance Dashboard button
            healthStatusToggle = new JToggleButton("Health Status");
            healthStatusToggle.setBackground(new Color(255, 230, 200));
            healthStatusToggle.setForeground(Color.BLACK);
            healthStatusToggle.setSelected(false); // Start as OFF
            healthStatusToggle.setToolTipText("Toggle Health Status UI on/off");
            healthStatusToggle.setPreferredSize(new Dimension(120, 30));
            healthStatusToggle.addActionListener(e -> {
                try {
                    JToggleButton source = (JToggleButton) e.getSource();
                    boolean showHealthUI = source.isSelected();
                    
                    if (showHealthUI) {
                        // Show the health status UI
                        NubiaHealthStatusUI healthUI = NubiaHealthStatusUI.getInstance();
                        if (healthUI != null) {
                            healthUI.showUI();
                            source.setText("Health Status ON");
                            source.setBackground(new Color(200, 255, 200)); // Light green when ON
                            appendStatus("Health Status UI opened");
                        } else {
                            // If getInstance returns null, reset the toggle
                            source.setSelected(false);
                            appendStatus("Failed to create Health Status UI");
                        }
                    } else {
                        // Hide the health status UI
                        NubiaHealthStatusUI.disposeInstance();
                        source.setText("Health Status");
                        source.setBackground(new Color(255, 230, 200)); // Light orange when OFF
                        appendStatus("Health Status UI closed");
                    }
                } catch (Exception ex) {
                    // Reset button state on error
                    JToggleButton source = (JToggleButton) e.getSource();
                    source.setSelected(false);
                    source.setText("Health Status");
                    source.setBackground(new Color(255, 230, 200));
                    logError("toggling health status UI", ex);
                }
            });
            cancelAllOrdersPanel.add(healthStatusToggle);
            
            // Add cancelAllOrdersPanel to the SOUTH of buttonPanel
            buttonPanel.add(cancelAllOrdersPanel, BorderLayout.SOUTH);

            // --- Create Gauge Panel ---
            JPanel gaugePanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 5));
            gaugePanel.setBorder(BorderFactory.createTitledBorder("Gauges"));

            bidAskDiffGauge = HorizontalGauge.createGauge(0, 25, 0, Color.CYAN);
            bidAskDiffGauge.setTitle("BidAsk Spread - Ticks");
            diffGauge = HorizontalGauge.createGauge(0, 25, 0, Color.MAGENTA);
            diffGauge.setTitle("Price jump - Ticks");

            gaugePanel.add(bidAskDiffGauge);
            gaugePanel.add(diffGauge);

            // Add gauge panel between button panel and log panel
            // To do this, we need to adjust how bottomPanel is constructed and added.
            // The old bottomPanel contained cancelAllOrdersPanel and logPanel.
            // Now, mainPanel's SOUTH will have a new panel that contains:
            // 1. buttonPanel (which itself has multiple sub-panels)
            // 2. gaugePanel
            // 3. bottomPanel (which will now only contain logPanel, cancelAllOrdersPanel is moved into buttonPanel structure)

            // Reconstruct the lower part of the UI
            // The buttonPanel already contains market buttons, order type panels, flatten buttons, and cancel all buttons.
            // So, we need a new container for buttonPanel, gaugePanel, and logPanel.

            JPanel southContainer = new JPanel(new BorderLayout(0, 5));
            southContainer.add(buttonPanel, BorderLayout.NORTH); // Existing buttons
            southContainer.add(gaugePanel, BorderLayout.CENTER); // Gauges below buttons

            // Create log panel with consistent appearance (this was part of the old bottomPanel)
            JPanel logPanel = new JPanel(new BorderLayout(0, 5));
            logPanel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createEmptyBorder(8, 0, 0, 0), // Top spacing
                BorderFactory.createTitledBorder("Order Log")
            ));
            
            logTextArea = new JTextArea(5, 50); // Reduced from 60 to 50
            logTextArea.setEditable(false);
            logTextArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11)); // Smaller font
            
            logScrollPane = new JScrollPane(logTextArea); // logScrollPane was already a field
            logPanel.add(logScrollPane, BorderLayout.CENTER);

            southContainer.add(logPanel, BorderLayout.SOUTH); // Log panel at the very bottom

            // Replace the direct addition of buttonPanel to mainPanel.SOUTH
            // mainPanel.add(buttonPanel, BorderLayout.SOUTH); // This line is now replaced by the one below
            mainPanel.add(southContainer, BorderLayout.SOUTH);
            
            // Configure frame size with more accurate minimum dimensions
            localFrame.getContentPane().add(mainPanel);
            localFrame.setMinimumSize(new Dimension(470, 750)); // Reduced by 15% from 550
            
            // Start the UI at minimum width
            localFrame.setSize(470, 750);
            
            // Add action listeners
            marketBuyButton.addActionListener(e -> {
                try {
                    double price = cachedDisplayPrice; // Use cached price instead of direct call
                    
                    // Send the order
                    boolean success = orderSender.sendMarketBuyOrder(1, OrderDuration.GTC, 0, 0);
                    
                    if (success) {
                        // Try to access the registerOrder method via reflection to track the order
                        try {
                            // Generate a unique client ID for tracking
                            final String clientId = "UI_" + System.currentTimeMillis();
                            orderStartTimes.put(clientId, System.currentTimeMillis());
                            
                            // Create a mock order ID for tracking
                            String mockOrderId = "MOCK_" + System.currentTimeMillis();
                            
                            // Store the mapping for tracking
                            orderClientIdToOrderId.put(clientId, mockOrderId);
                            
                            // Find the registerOrder method
                            java.lang.reflect.Method registerMethod = orderSender.getClass().getDeclaredMethod(
                                "registerOrder", String.class, String.class, boolean.class, 
                                OrderSenderControllerV2.OrderType.class, int.class, double.class, 
                                String.class, boolean.class);
                            
                            registerMethod.setAccessible(true);
                            
                            // Invoke the method to register the order
                            registerMethod.invoke(orderSender, mockOrderId, clientId, true, 
                                OrderSenderControllerV2.OrderType.MARKET, 1, price, 
                                "Market Buy Order from UI", false);
                            
                            Log.info("OrdersTestUIV3: Successfully registered UI-generated order for tracking: " + mockOrderId);
                        } catch (Exception ex) {
                            // If reflection fails, just log it
                            Log.info("OrdersTestUIV3: Could not register order via reflection: " + ex.getMessage());
                        }
                    }
                    
                    playBeep(1);
                    logOrder(success, "Market Buy", price);
                } catch (Exception ex) {
                    logError("market buy", ex);
                }
            });
            
            marketSellButton.addActionListener(e -> {
                try {
                    double price = cachedDisplayPrice; // Use cached price instead of direct call
                    boolean success = orderSender.sendMarketSellOrder(1, OrderDuration.GTC, 0, 0);
                    playBeep(1);
                    logOrder(success, "Market Sell", price);
                } catch (Exception ex) {
                    logError("market sell", ex);
                }
            });
            
            instanceFlattenButton.addActionListener(e -> {
                try {
                    boolean success = orderSender.instanceFlattenPositions();
                    playBeep(2); // Double beep for Instance Flatten
                    appendStatus(success ? "Instance flatten executed" : "Failed to flatten instance positions");
                } catch (Exception ex) {
                    logError("instance flatten", ex);
                }
            });
            
            globalFlattenButton.addActionListener(e -> {
                try {
                    boolean success = OrderSenderControllerV2.globalFlattenPositions();
                    playBeep(3); // Triple beep for Global Flatten
                    appendStatus(success ? "Global flatten executed" : "Failed to flatten global positions");
                } catch (Exception ex) {
                    logError("global flatten", ex);
                }
            });
            
            // Restore previous window position
            restoreWindowPosition();
            
        } catch (Exception e) {
            Log.info("OrdersTestUIV3: Error in createAndShowUI: " + e.getMessage());
            throw e; // Rethrow to be caught by the outer handler
        }

        /* One layout pass, then show immediately */
        localFrame.pack();
        localFrame.setMinimumSize(new Dimension(470, 750));
        localFrame.setVisible(true);          // first‑paint ⇢ NOW

        /* Populate heavyweight model after first paint.  adjustTableHeight()
         * is already invoked inside refreshPositionsTable(), so we no longer
         * call it twice.                                                   */
        SwingUtilities.invokeLater(() -> {
            refreshPositionsTable();
            /* no re‑pack needed — viewport size only */
        });
        
        // ═══ PROGRESSIVE STARTUP SYSTEM ═══
        startupTimestamp = System.currentTimeMillis();
        
        // Initialize cached data with current values if available
        initializeStartupData();
        
        // Delay scheduler startup to allow initial data to arrive and stabilize
        Timer startupTimer = new Timer();
        startupTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                try {
                    // Start schedulers with progressive activation
                    startScheduler();
                    Log.info("OrdersTestUIV3: Schedulers started after startup delay");
                } catch (Exception e) {
                    Log.error("OrdersTestUIV3: Error starting schedulers", e);
                } finally {
                    startupTimer.cancel();
                }
            }
        }, 3000); // 3-second startup delay
        
        long initEnd = System.currentTimeMillis();
        double elapsed = (initEnd - initStart) / 1000.0;
        Log.info(String.format("OrdersTestUIV3 initialization took %.2f seconds (schedulers delayed)", elapsed));
    }
    
    /**
     * Initialize cached data with current values if available during startup.
     * This populates the cache before schedulers start to avoid displaying
     * default/stale values initially.
     */
    private void initializeStartupData() {
        try {
            // Initialize cached data with safe defaults
            // Note: Real data will arrive via interface calls and populate these properly
            
            if (DEBUG_UI) Log.info("OrdersTestUIV3: Initializing startup data with defaults");
            
            // Initialize with safe defaults - actual data will come via interface
            cachedDisplayPrice = 0.0; // Will be set by updatePrice() calls
            // Remove this redundant calculation - cachedGlobalPnL should only be updated via updateGlobalPnL()
            // cachedGlobalPnL = globalUnrealizedPnL + globalRealizedPnL;
            
            // Initialize position data with defaults - actual data will come via updatePositions()
            cachedInstancePosition = 0;
            cachedGlobalPosition = 0;
            
            // These will be marked as available when first real data arrives via interface
            
            // Initialize alias if available
            // Note: This will be populated when the first updateAlias() call arrives
            cachedAlias = ""; // Will be updated by listener calls
            
            // Initialize account data with defaults (will be updated by listener calls)
            cachedAccountBalance = 0.0;
            cachedWorkingSells = 0;
            cachedWorkingBuys = 0;
            cachedAccountPosition = 0;
            
            // FIXED: Initialize live trading status from the actual controller
            if (orderSender != null) {
                try {
                    cachedLiveTradingEnabled = orderSender.isEnableLiveTrading();
                    needsLiveTradingUpdate = true; // Ensure UI gets updated with correct status
                    if (DEBUG_UI) Log.info("OrdersTestUIV3: Initialized live trading status from controller: " + cachedLiveTradingEnabled);
                } catch (Exception e) {
                    Log.warn("OrdersTestUIV3: Error getting live trading status from controller, using default false", e);
                    cachedLiveTradingEnabled = false;
                    needsLiveTradingUpdate = true; // Still update UI with default
                }
            } else {
                cachedLiveTradingEnabled = false;
                needsLiveTradingUpdate = true; // Update UI with default
                if (DEBUG_UI) Log.info("OrdersTestUIV3: OrderSender is null, using default live trading status: false");
            }
            
            Log.info("OrdersTestUIV3: Startup data initialization completed");
            
        } catch (Exception e) {
            Log.warn("OrdersTestUIV3: Error during startup data initialization", e);
        }
    }
    
    /* -----------------------------------------------------------
     *  Safer throttling guard — flag is always cleared even if the
     *  body of the guarded section throws.
     * --------------------------------------------------------- */
    private boolean shouldThrottleUpdate() {
        synchronized(updateLock) {
            if (updatePending) {
                return true;                      // another update underway
            }
            boolean skip;
            try {
                long now = System.currentTimeMillis();
                skip = (now - lastUpdateTime) < 200;
                if (!skip) {
                    lastUpdateTime = now;
                }
            } finally {
                updatePending = false;
            }
            return skip;
        }
    }

    private JPanel createOrderPanel(String title, String buyLabel, String sellLabel, OffsetButtonHandler handler) {
        JPanel panel = new JPanel(new BorderLayout(10, 0)); // Increased horizontal spacing
        panel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        
        // Left panel with offset fields - make more compact
        JPanel offsetPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 0)); // Reduced spacing
        
        // First offset field (common for all panels)
        JLabel offsetLabel = new JLabel("Offset:");
        offsetLabel.setPreferredSize(new Dimension(50, offsetLabel.getPreferredSize().height)); // Fixed width for label
        offsetPanel.add(offsetLabel);
        
        JTextField offsetField = new JTextField(5); // Reduced by 33% from 7 to 5
        offsetField.setHorizontalAlignment(JTextField.RIGHT);
        offsetPanel.add(offsetField);
        
        // Add second offset field for OCO panel only
        if (title.equals("OCO")) {
            JLabel limitOffsetLabel = new JLabel("Limit:");
            limitOffsetLabel.setPreferredSize(new Dimension(40, limitOffsetLabel.getPreferredSize().height));
            offsetPanel.add(limitOffsetLabel);
            
            ocoLimitOffsetField = new JTextField(5); // Reduced by 33% from 7 to 5
            ocoLimitOffsetField.setHorizontalAlignment(JTextField.RIGHT);
            offsetPanel.add(ocoLimitOffsetField);
        }
        
        // Button panel with buy/sell buttons - make buttons more consistent and prominent
        JPanel buttonPanel = new JPanel(new GridLayout(1, 2, 10, 0)); // Increased gap between buttons
        JButton buyButton = new JButton(title + " " + buyLabel);
        JButton sellButton = new JButton(title + " " + sellLabel);
        
        // Make buttons equal width with slightly larger dimensions
        Dimension buttonSize = new Dimension(120, 28); // Reduced width from 140 to 120
        buyButton.setPreferredSize(buttonSize);
        sellButton.setPreferredSize(buttonSize);
        
        // Add some visual distinction to buy/sell buttons with better contrast
        buyButton.setBackground(BUY_BUTTON_COLOR);
        buyButton.setForeground(Color.BLACK);
        sellButton.setBackground(SELL_BUTTON_COLOR);
        sellButton.setForeground(Color.BLACK);
        
        buttonPanel.add(buyButton);
        buttonPanel.add(sellButton);
        
        // Use a more balanced layout
        JPanel containerPanel = new JPanel(new BorderLayout());
        containerPanel.add(offsetPanel, BorderLayout.WEST);
        containerPanel.add(buttonPanel, BorderLayout.CENTER);
        
        panel.add(containerPanel, BorderLayout.CENTER);
        
        // Add action listeners based on button type
        if (title.equals("Limit")) {
            buyButton.addActionListener(e -> {
                try {
                    Toolkit.getDefaultToolkit().beep();
                    double price = cachedDisplayPrice; // Use cached price instead of direct call
                    double displayPrice = price;
                    double offset = Double.parseDouble(offsetField.getText());
                    // Buy limit should be BELOW current price
                    double limitPrice = displayPrice - offset;
                    boolean success = orderSender.sendLimitBuyOrder(1, limitPrice, OrderDuration.GTC, 0, 0);
                    logOrder(success, "Limit Buy", limitPrice);
                } catch (Exception ex) {
                    logError("Limit Buy", ex);
                }
            });

            sellButton.addActionListener(e -> {
                try {
                    Toolkit.getDefaultToolkit().beep();
                    double price = cachedDisplayPrice; // Use cached price instead of direct call
                    double displayPrice = price;
                    double offset = Double.parseDouble(offsetField.getText());
                    // Sell limit should be ABOVE current price
                    double limitPrice = displayPrice + offset;
                    boolean success = orderSender.sendLimitSellOrder(1, limitPrice, OrderDuration.GTC, 0, 0);
                    logOrder(success, "Limit Sell", limitPrice);
                } catch (Exception ex) {
                    logError("Limit Sell", ex);
                }
            });
        } 
        else if (title.equals("Trailing Stop")) {
            buyButton.addActionListener(e -> {
                try {
                    Toolkit.getDefaultToolkit().beep();
                    double price = cachedDisplayPrice; // Use cached price instead of direct call
                    double displayPrice = price;
                    double offset = Double.parseDouble(offsetField.getText());
                    // Buy stop should be ABOVE current price
                    double stopPrice = displayPrice + offset;
                    boolean success = orderSender.sendTrailingStopBuyOrder(1, stopPrice, 1, OrderDuration.GTC, 0, 0, 0.0);
                    logOrder(success, "Trailing Stop Buy", stopPrice);
                } catch (Exception ex) {
                    logError("Trailing Stop Buy", ex);
                }
            });

            sellButton.addActionListener(e -> {
                try {
                    Toolkit.getDefaultToolkit().beep();
                    double price = cachedDisplayPrice; // Use cached price instead of direct call
                    double displayPrice = price;
                    double offset = Double.parseDouble(offsetField.getText());
                    // Sell stop should be BELOW current price
                    double stopPrice = displayPrice - offset;
                    boolean success = orderSender.sendTrailingStopSellOrder(1, stopPrice, 1, OrderDuration.GTC, 0, 0, 0.0);
                    logOrder(success, "Trailing Stop Sell", stopPrice);
                } catch (Exception ex) {
                    logError("Trailing Stop Sell", ex);
                }
            });
        }
        else if (title.equals("OCO")) {
            buyButton.addActionListener(e -> {
                try {
                    Toolkit.getDefaultToolkit().beep();
                    double price = cachedDisplayPrice; // Use cached price instead of direct call
                    double displayPrice = price;
                    
                    // Get both offset values
                    double stopOffset = Double.parseDouble(offsetField.getText());
                    double limitOffset = Double.parseDouble(ocoLimitOffsetField.getText());
                    
                    // For OCO Buy: limit below, stop above
                    double limitPrice = displayPrice - limitOffset;
                    double stopPrice = displayPrice + stopOffset;
                    
                    boolean success = orderSender.sendOcoBuyLimitBuyStopOrder(1, limitPrice, stopPrice, OrderDuration.GTC, 0, 0);
                    logOrder(success, "OCO Buy", price);
                } catch (Exception ex) {
                    logError("OCO Buy", ex);
                }
            });

            sellButton.addActionListener(e -> {
                try {
                    Toolkit.getDefaultToolkit().beep();
                    double price = cachedDisplayPrice; // Use cached price instead of direct call
                    double displayPrice = price;
                    
                    // Get both offset values
                    double stopOffset = Double.parseDouble(offsetField.getText());
                    double limitOffset = Double.parseDouble(ocoLimitOffsetField.getText());
                    
                    // For OCO Sell: limit above, stop below
                    double limitPrice = displayPrice + limitOffset;
                    double stopPrice = displayPrice - stopOffset;
                    
                    boolean success = orderSender.sendOcoSellLimitSellStopOrder(1, limitPrice, stopPrice, OrderDuration.GTC, 0, 0);
                    logOrder(success, "OCO Sell", price);
                } catch (Exception ex) {
                    logError("OCO Sell", ex);
                }
            });
        }
        
        // Call the handler if provided (for setup)
        if (handler != null) {
            handler.setupButtons(offsetField, buyButton, sellButton);
        }
        
        return panel;
    }

    private void setupPriceFields(JPanel topRow, JPanel bottomRow, JPanel thirdRow) {
        // Top row: Alias, Display Price (2 columns since Global P&L moved to bottom)
        // Alias field - first position
        JPanel aliasPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 0));
        JLabel aliasLabel = new JLabel("Alias:");
        aliasLabel.setPreferredSize(new Dimension(40, aliasLabel.getPreferredSize().height)); // Fixed width for label
        aliasPanel.add(aliasLabel);
        aliasField = new JTextField(13);  // Reduced from 15 to 13
        aliasField.setEditable(false);
        aliasField.setText(""); // Set to empty or a placeholder initially
        aliasPanel.add(aliasField);
        
        // Display price field - second position
        JPanel displayPricePanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 0));
        displayPricePanel.add(new JLabel("Display Price:"));
        displayPriceField = new JTextField(8);  // Increased from 6 to 8 (25% increase)
        displayPriceField.setEditable(false);
        displayPriceField.setHorizontalAlignment(JTextField.RIGHT);
        displayPricePanel.add(displayPriceField);
        
        // Bottom row: Global P&L, Global Position, Global Position Limit
        // Global P&L field - moved to first position in bottom row
        JPanel globalPnLPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 0));
        globalPnLPanel.add(new JLabel("Global P&L:"));
        globalPnLField = new JTextField(10);  // Displays Global Total P&L (Realized + Unrealized)
        globalPnLField.setEditable(false);
        globalPnLField.setHorizontalAlignment(JTextField.RIGHT);
        globalPnLPanel.add(globalPnLField);
        
        // Global position field - second position in bottom row
        JPanel globalPosPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 0));
        globalPosPanel.add(new JLabel("Global Position:"));
        globalPosField = new JTextField(4);  // Keep the same size
        globalPosField.setEditable(false);
        globalPosField.setHorizontalAlignment(JTextField.RIGHT);
        globalPosPanel.add(globalPosField);
        
        // Global position limit field - third position in bottom row
        JPanel globalPosLimitPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 0));
        globalPosLimitPanel.add(new JLabel("Global Pos Limit:"));
        globalPositionLimitField = new JTextField(4);  // Same size as global position field
        globalPositionLimitField.setEditable(false);
        globalPositionLimitField.setHorizontalAlignment(JTextField.RIGHT);
        globalPosLimitPanel.add(globalPositionLimitField);
        
        // Add panels to their respective rows
        topRow.add(aliasPanel);              // Alias first
        topRow.add(displayPricePanel);       // Display Price second (Available Liquidity stays in infoRow)
        
        bottomRow.add(globalPnLPanel);       // Global P&L first (moved from top row)
        bottomRow.add(globalPosPanel);       // Global Position second
        bottomRow.add(globalPosLimitPanel);  // Global Position Limit third

        // --- Third row: Time‑gate / Position Caps / Order Caps (SPLIT CAPS) ---
        JPanel gatePanel = new JPanel(new FlowLayout(FlowLayout.LEFT,10,0));
        gatePanel.add(new JLabel("Trade Window:"));
        timeGateField = new JTextField(10);
        timeGateField.setEditable(false);
        gatePanel.add(timeGateField);

        // Position Cap field (first part of split caps)
        JPanel posCapPanel = new JPanel(new FlowLayout(FlowLayout.LEFT,10,0));
        posCapPanel.add(new JLabel("Position Cap:"));
        capsField = new JTextField(8); // Reduced size since it only shows position cap now
        capsField.setEditable(false);
        posCapPanel.add(capsField);
        
        // Order Cap field (second part of split caps)
        JPanel orderCapPanel = new JPanel(new FlowLayout(FlowLayout.LEFT,10,0));
        orderCapPanel.add(new JLabel("Order Cap:"));
        orderCapField = new JTextField(8); // New field for order cap
        orderCapField.setEditable(false);
        orderCapField.setHorizontalAlignment(JTextField.RIGHT);
        orderCapPanel.add(orderCapField);

        thirdRow.add(gatePanel);
        thirdRow.add(posCapPanel);     // Position cap only
        thirdRow.add(orderCapPanel);   // Order cap only (new field)
    }

    // --- Update time-gate/caps fields in UI ---
    @Override
    public void updateTimeGate(OrderSenderControllerV2.TimeGateState state,int posCap,int ordCap,long secsToNext)
    {
        // Update cached data immediately (thread-safe)
        cachedTimeGateState = state;
        cachedPosCap = posCap;
        cachedOrdCap = ordCap;
        cachedSecsToNext = secsToNext;
        needsTimeGateUpdate = true;
        
        // Note: Actual UI update will happen in the scheduler
    }

    // Add a named inner class to handle the time gate updates
    private class TimeGateUpdater implements Runnable {
        private final OrderSenderControllerV2.TimeGateState state;
        private final String secs;
        private final int posCap;
        private final int ordCap;
        
        public TimeGateUpdater(OrderSenderControllerV2.TimeGateState state, String secs, int posCap, int ordCap) {
            this.state = state;
            this.secs = secs;
            this.posCap = posCap;
            this.ordCap = ordCap;
        }
        
        @Override
        public void run() {
            if (timeGateField == null || capsField == null || orderCapField == null) {
               Log.info("Error: TimeGateUpdater fields are null");
                return;
            }
            
            try {
                switch(state) {
                    case ACTIVE:
                        timeGateField.setBackground(GATE_ACTIVE);
                        break;
                    case THIN_LIQUIDITY:
                        timeGateField.setBackground(GATE_THIN);
                        break;
                    case CLOSE_ONLY:
                        timeGateField.setBackground(GATE_CLOSE);
                        break;
                    case BLOCKED:
                        timeGateField.setBackground(GATE_BLOCKED);
                        break;
                }
                timeGateField.setText(state.name() + secs);
                
                // Debug the caps values
               Log.info("Setting caps fields: posCap=" + posCap + ", ordCap=" + ordCap);
                
                // Set the text for the split caps fields
                capsField.setText(String.valueOf(posCap));        // Position Cap only
                orderCapField.setText(String.valueOf(ordCap));    // Order Cap only
            } catch (Exception e) {
                Log.error("Error in TimeGateUpdater: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    private void createPositionsTable() {
        // Create column names for the table, adding 'Loss Limit' after 'Realized',
        // and DD columns at the end.
        String[] columnNames = {"Strategy", "Pos Limit", "Enabled", "Position", "Unrealized", "Realized", "Loss Limit", "Profit Limits", "DD Total", "DD Unreal."};
        // Create table model
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
            @Override
            public Object getValueAt(int row, int column) {
                Object value = super.getValueAt(row, column);
                // Hide zero positions
                if (column == 1 && value != null && value.toString().equals("0")) {
                    return "";
                }
                return value;
            }
        };
        // Create table with the model
        positionsTable = new JTable(tableModel);
        positionsTable.setFont(new Font(Font.SANS_SERIF, Font.PLAIN, 11)); // Smaller font
        positionsTable.setRowHeight(16); // Very compact row height
        // Set column widths
        positionsTable.getColumnModel().getColumn(0).setPreferredWidth(95); // Strategy
        positionsTable.getColumnModel().getColumn(1).setPreferredWidth(51); // Pos Limit
        positionsTable.getColumnModel().getColumn(2).setPreferredWidth(51); // Enabled
        positionsTable.getColumnModel().getColumn(3).setPreferredWidth(51); // Position
        positionsTable.getColumnModel().getColumn(4).setPreferredWidth(65); // Unrealized
        positionsTable.getColumnModel().getColumn(5).setPreferredWidth(65); // Realized
        positionsTable.getColumnModel().getColumn(6).setPreferredWidth(70); // Loss Limit
        positionsTable.getColumnModel().getColumn(7).setPreferredWidth(100); // Profit Limits
        positionsTable.getColumnModel().getColumn(8).setPreferredWidth(150); // DD Total (Cash / Percent)
        positionsTable.getColumnModel().getColumn(9).setPreferredWidth(150); // DD Unreal. (Hits / Cash / Percent)
        // Custom renderer for position column to highlight long/short
        positionsTable.getColumnModel().getColumn(3).setCellRenderer(new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                
                // Check for shutdown or initialization in progress
                if (uiState.get() == UIState.SHUTTING_DOWN || uiState.get() == UIState.DISPOSED || tableModel == null) {
                    c.setForeground(NEUTRAL_POSITION_COLOR);
                    c.setFont(c.getFont().deriveFont(Font.PLAIN));
                    return c;
                }
                
                try {
                    c.setFont(c.getFont().deriveFont(Font.BOLD));
                    if (value != null) {
                        String posStr = value.toString();
                        if (!posStr.isEmpty()) {
                            try {
                                int position = Integer.parseInt(posStr);
                                if (position > 0) {
                                    c.setForeground(LONG_POSITION_COLOR);
                                } else if (position < 0) {
                                    c.setForeground(SHORT_POSITION_COLOR);
                                } else {
                                    c.setForeground(NEUTRAL_POSITION_COLOR);
                                }
                            } catch (NumberFormatException e) {
                                c.setForeground(NEUTRAL_POSITION_COLOR);
                            }
                        } else {
                            c.setForeground(NEUTRAL_POSITION_COLOR);
                        }
                    } else {
                        c.setForeground(NEUTRAL_POSITION_COLOR);
                    }
                } catch (Exception e) {
                    c.setForeground(NEUTRAL_POSITION_COLOR);
                    if (DEBUG_UI) Log.info("Position column renderer exception: " + e.getMessage());
                }
                return c;
            }
        });
        // Custom renderer for 'Enabled' column
        positionsTable.getColumnModel().getColumn(2).setCellRenderer(new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                
                // Check for shutdown or initialization
                if (uiState.get() == UIState.SHUTTING_DOWN || uiState.get() == UIState.DISPOSED || tableModel == null) {
                    c.setForeground(Color.BLACK);
                    return c;
                }
                
                try {
                    String val = value != null ? value.toString() : "";
                    if ("No".equalsIgnoreCase(val)) {
                        c.setForeground(new Color(220, 0, 0)); // Bright red
                    } else if ("Yes".equalsIgnoreCase(val)) {
                        c.setForeground(new Color(0, 180, 0)); // Bright green
                    } else {
                        c.setForeground(Color.BLACK);
                    }
                } catch (Exception e) {
                    c.setForeground(Color.BLACK);
                    if (DEBUG_UI) Log.info("Enabled column renderer exception: " + e.getMessage());
                }
                return c;
            }
        });
        // Custom renderer for 'Loss Limit' column
        positionsTable.getColumnModel().getColumn(6).setCellRenderer(new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                c.setFont(c.getFont().deriveFont(Font.PLAIN));
                // First check if tableModel is null (during shutdown)
                if (uiState.get() == UIState.SHUTTING_DOWN || uiState.get() == UIState.DISPOSED || tableModel == null) {
                    c.setForeground(Color.BLACK);
                    c.setFont(c.getFont().deriveFont(Font.PLAIN));
                    return c;
                }
                
                try {
                    // Only color for data rows, not separator/total
                    if (row < tableModel.getRowCount() - 2) {
                        // Get the controller for this row
                        String strategyName = (String) tableModel.getValueAt(row, 0);
                        OrderSenderControllerV2 controller = registeredControllers.get(strategyName);
                        if (controller != null && controller.isLossLimitCloseOnly()) {
                            c.setForeground(Color.RED);
                            c.setFont(c.getFont().deriveFont(Font.BOLD));
                        } else {
                            c.setForeground(Color.BLACK);
                            c.setFont(c.getFont().deriveFont(Font.PLAIN));
                        }
                    } else {
                        c.setForeground(Color.BLACK);
                        c.setFont(c.getFont().deriveFont(Font.PLAIN));
                    }
                } catch (Exception e) {
                    // Handle any exceptions during shutdown
                    c.setForeground(Color.BLACK);
                    c.setFont(c.getFont().deriveFont(Font.PLAIN));
                    if (DEBUG_UI) Log.info("Cell renderer exception: " + e.getMessage());
                }
                return c;
            }
        });
        // Custom renderer for 'Unrealized' and 'Realized' columns to highlight when profit target is met
        DefaultTableCellRenderer pnlRenderer = new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                
                // Ensure value is treated as string for checks
                String valueString = value != null ? value.toString() : "";

                // Check for shutdown or null tableModel
                if (uiState.get() == UIState.SHUTTING_DOWN || uiState.get() == UIState.DISPOSED || tableModel == null) {
                    c.setForeground(Color.BLACK);
                    c.setFont(c.getFont().deriveFont(Font.PLAIN));
                    ((DefaultTableCellRenderer)c).setHorizontalAlignment(SwingConstants.RIGHT);
                    return c;
                }
                
                try {
                    // Don't apply special coloring to separator or total rows
                    if (row >= tableModel.getRowCount() - 2) { // Check if it's one of the last two rows (separator or total)
                        c.setForeground(Color.BLACK); // Default color for special rows
                        c.setFont(c.getFont().deriveFont(Font.PLAIN)); // Reset font for special rows
                        return c;
                    }
                } catch (Exception e) {
                    c.setForeground(Color.BLACK); 
                    c.setFont(c.getFont().deriveFont(Font.PLAIN));
                    if (DEBUG_UI) Log.info("PnL renderer exception: " + e.getMessage());
                    return c;
                }

                // Get the strategy name from the first column of the current row
                Object strategyNameObj = table.getValueAt(row, 0);
                String strategyName = strategyNameObj != null ? strategyNameObj.toString() : null;
                
                // Check if this is a valid strategy row
                if (strategyName != null && !strategyName.isEmpty() && !strategyName.equals("------------------")) {
                    OrderSenderControllerV2 controller = registeredControllers.get(strategyName);
                    // Check if the profit target for this controller instance is met
                    if (controller != null && controller.isProfitTargetCloseOnly()) {
                        // Use the bright purple color defined for the warning label
                        c.setForeground(new Color(199, 21, 133)); 
                        c.setFont(c.getFont().deriveFont(Font.BOLD));
                    } else {
                        // Apply default color based on PnL sign if needed, or just black
                        if (column == 4 || column == 5) { // Unrealized or Realized column
                             if (!valueString.isEmpty()) {
                                 try {
                                     double pnlValue = Double.parseDouble(valueString.replace(" ↑", "").replace(" ↓", "").replace("+", ""));
                                     if (pnlValue > 0) {
                                         c.setForeground(LONG_POSITION_COLOR); // Green for positive
                                     } else if (pnlValue < 0) {
                                         c.setForeground(SHORT_POSITION_COLOR); // Crimson for negative
                                     } else {
                                         c.setForeground(Color.BLACK); // Black for zero
                                     }
                                 } catch (NumberFormatException e) {
                                     c.setForeground(Color.BLACK); // Black for invalid numbers
                                 }
                             } else {
                                  c.setForeground(Color.BLACK); // Black for empty strings
                             }
                             c.setFont(c.getFont().deriveFont(Font.PLAIN));
                        } else {
                            c.setForeground(Color.BLACK); // Default color for other columns
                        }
                    }
                } else {
                     // Handle cases where strategyName is null, empty, or separator row
                     c.setForeground(Color.BLACK); // Default color
                     c.setFont(c.getFont().deriveFont(Font.PLAIN));
                }
                
                // Ensure the text alignment is right
                ((DefaultTableCellRenderer)c).setHorizontalAlignment(SwingConstants.RIGHT);
                
                return c;
            }
        };
        
        // Apply the custom renderer to the 'Unrealized' and 'Realized' columns
        positionsTable.getColumnModel().getColumn(4).setCellRenderer(pnlRenderer);
        positionsTable.getColumnModel().getColumn(5).setCellRenderer(pnlRenderer);

        // Custom renderer for 'DD Total Hit' column (New)
        positionsTable.getColumnModel().getColumn(8).setCellRenderer(new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                
                // Check for shutdown or initialization
                if (uiState.get() == UIState.SHUTTING_DOWN || uiState.get() == UIState.DISPOSED || tableModel == null) {
                    c.setForeground(Color.GRAY);
                    c.setFont(c.getFont().deriveFont(Font.PLAIN));
                    ((DefaultTableCellRenderer)c).setHorizontalAlignment(SwingConstants.CENTER);
                    return c;
                }
                
                try {
                    String status = value != null ? value.toString() : "";
                    if ("HIT".equalsIgnoreCase(status)) {
                        c.setForeground(SHORT_POSITION_COLOR); // Red for HIT
                        c.setFont(c.getFont().deriveFont(Font.BOLD));
                    } else if ("OK".equalsIgnoreCase(status)) {
                        c.setForeground(LONG_POSITION_COLOR); // Green for OK
                        c.setFont(c.getFont().deriveFont(Font.PLAIN));
                    } else {
                        c.setForeground(Color.GRAY); // Gray for other states like "-" or empty
                        c.setFont(c.getFont().deriveFont(Font.PLAIN));
                    }
                } catch (Exception e) {
                    c.setForeground(Color.GRAY);
                    c.setFont(c.getFont().deriveFont(Font.PLAIN));
                    if (DEBUG_UI) Log.info("DD Total column renderer exception: " + e.getMessage());
                }
                
                ((DefaultTableCellRenderer)c).setHorizontalAlignment(SwingConstants.CENTER);
                return c;
            }
        });

        // Custom renderer for 'DD Unreal. Hit' column (New)
        DefaultTableCellRenderer centerRenderer = new DefaultTableCellRenderer();
        centerRenderer.setHorizontalAlignment(SwingConstants.CENTER);
        positionsTable.getColumnModel().getColumn(9).setCellRenderer(centerRenderer);

        // Create scroll pane for the table
        positionsScrollPane = new JScrollPane(positionsTable);
        positionsScrollPane.setPreferredSize(new Dimension(380, 150)); // Reduced by 15% from 450
    }

    // MODIFIED: Renamed from updatePositions and changed to private
    @Override
    public void refreshPositionsTable() {
        // Ensure EDT execution with null safety and throttling
        if (SwingUtilities.isEventDispatchThread()) {
            refreshPositionsTableImpl();
        } else {
            SwingUtilities.invokeLater(this::refreshPositionsTableImpl);
        }
    }
    
    // Implementation method that must be called on EDT
    private void refreshPositionsTableImpl() {
        if (uiState.get() != UIState.RUNNING || shouldThrottleUpdate() || frame == null || !frame.isVisible() || tableModel == null) {
            return;
        }
        
        synchronized (updateLock) {
            if (updatePending) {
                return;
            }
            updatePending = true;
        }
        
        try {
            // (Fix) Always clear and rebuild the table to ensure correct row order and separator/total rows
            tableModel.setRowCount(0);
            double totalLossLimit = 0.0;
            
            // Build data rows for all controllers with null safety
            for (Map.Entry<String, OrderSenderControllerV2> entry : registeredControllers.entrySet()) {
                if (entry == null || entry.getKey() == null) continue; // Skip null entries
                
                OrderSenderControllerV2 controller = entry.getValue();
                if (controller == null) continue; // Skip null controllers
                
                String strategyName = entry.getKey();
                
                try {
                    int position = controller.getInstancePosition();
                    double unrealized = controller.getInstanceUnrealizedPnL();
                    double realized = controller.getInstanceRealizedPnL();
                    int posLimit = controller.getMaxPositionSize();
                    boolean tradingEnabled = controller.isEnableLiveTrading();
                    String tradingEnabledStr = tradingEnabled ? "Yes" : "No";
                    double lossLimit = controller.getMaxDailyLoss();
                    totalLossLimit += lossLimit;
                    
                    // --- Profit Limits column ---
                    double profitLimitRealized = controller.getInstanceMaxDailyProfitRealized();
                    double profitLimitTotal = controller.getInstanceMaxDailyProfitTotal();
                    String profitLimitsStr = String.format("%.2f/%.2f", profitLimitRealized, profitLimitTotal);
                    
                    // --- DD Total Hit column --- (New)
                    String ddTotalHitStatus = "OK";
                    DrawdownGuard drawdownGuard = controller.getDrawdownGuard();
                    String ddTotalDisplay = "-";
                    String ddUnrDisplay = "-";

                    if (drawdownGuard != null) {
                        try {
                            if (drawdownGuard.hasTotalDrawdownHit()) {
                                ddTotalHitStatus = "HIT";
                            }
                            double currentTotalDDValue = drawdownGuard.getCurrentTotalDrawdownValue();
                            double currentTotalDDPercent = drawdownGuard.getCurrentTotalDrawdownPercent();
                            ddTotalDisplay = String.format("%s (%.2f / %.3f%%)", ddTotalHitStatus, currentTotalDDValue, currentTotalDDPercent * 100.0);
                            
                            int ddUnrealizedHits = drawdownGuard.getUnrealizedDrawdownFlattenCount();
                            int ddUnrealizedDetections = drawdownGuard.getUnrealizedDrawdownDetectionCount(); // Use new detection counter
                            double currentUnrealizedDDValue = drawdownGuard.getCurrentUnrealizedDrawdownValue();
                            double currentUnrealizedDDPercent = drawdownGuard.getCurrentUnrealizedDrawdownPercent();
                            ddUnrDisplay = String.format("%dx-%da (%.2f / %.3f%%)", ddUnrealizedDetections, ddUnrealizedHits , currentUnrealizedDDValue, currentUnrealizedDDPercent * 100.0);
                        } catch (Exception ddEx) {
                            // Handle drawdown guard errors gracefully
                            if (DEBUG_UI) Log.info("OrdersTestUIV3: Error getting drawdown data for " + strategyName + ": " + ddEx.getMessage());
                            ddTotalDisplay = "-";
                            ddUnrDisplay = "-";
                        }
                    }

                    // Debug log for each row
                    if (DEBUG_UI) {
                        System.out.println("[OrdersTestUIV3] Row: " + strategyName + " | Controller: " + controller + " | Pos: " + position + " | U: " + unrealized + " | R: " + realized + " | LossLimit: " + lossLimit + " | ProfitLimits: " + profitLimitsStr);
                    }
                    
                    tableModel.addRow(new Object[]{
                        strategyName,
                        posLimit,
                        tradingEnabledStr,
                        position,
                        formatPnLWithPips(unrealized),
                        formatPnLWithPips(realized),
                        String.format("%.2f", lossLimit),
                        profitLimitsStr,
                        ddTotalDisplay, // MODIFIED: Use combined string
                        ddUnrDisplay    // MODIFIED: Use combined string
                    });
                } catch (Exception controllerEx) {
                    // Handle individual controller errors gracefully
                    if (DEBUG_UI) Log.info("OrdersTestUIV3: Error getting data from controller " + strategyName + ": " + controllerEx.getMessage());
                    // Add a placeholder row to indicate error
                    tableModel.addRow(new Object[]{
                        strategyName + " (ERROR)",
                        "-", "-", "-", "-", "-", "-", "-", "-", "-"
                    });
                }
            }
            
            // Add separator row
            tableModel.addRow(new Object[]{"------------------", "", "", "--------------", "--------------", "--------------", "--------------", "", "--------------------", "--------------------"}); // MODIFIED: Adjusted placeholders for DD columns
            
            // Add total row with null safety
            int totalPosition = cachedGlobalPosition; // Use cached data instead of direct call
            double totalUnrealized = globalUnrealizedPnL;
            double totalRealized = globalRealizedPnL;
            int totalPosLimit = 0;
            boolean allEnabled = true;
            
            for (Map.Entry<String, OrderSenderControllerV2> entry : registeredControllers.entrySet()) {
                if (entry == null) continue;
                OrderSenderControllerV2 controller = entry.getValue();
                if (controller != null) {
                    try {
                        totalPosLimit += controller.getMaxPositionSize();
                        allEnabled = allEnabled && controller.isEnableLiveTrading();
                    } catch (Exception ex) {
                        // Handle individual controller errors in total calculation
                        if (DEBUG_UI) Log.info("OrdersTestUIV3: Error getting totals from controller: " + ex.getMessage());
                    }
                }
            }
            
            String totalEnabledStr = allEnabled ? "Yes" : "No";
            
            // For total row, profit limits column can be left blank or a dash
            tableModel.addRow(new Object[]{
                "TOTAL",
                totalPosLimit,
                totalEnabledStr,
                totalPosition,
                formatPnLWithPips(totalUnrealized),
                formatPnLWithPips(totalRealized),
                String.format("%.2f", totalLossLimit),
                "", // Profit Limits for total row
                "-",  // DD Total Hit for total row
                "-"  // DD Unrealized Hits for total row
            });
            
            // (4) Only adjust table height if row count changed
            adjustTableHeight(tableModel.getRowCount());
            
            // Update global position field and color it with null safety
            if (globalPosField != null) {
                globalPosField.setText(String.valueOf(totalPosition));
                if (totalPosition > 0) {
                    globalPosField.setBackground(LONG_POSITION_COLOR);
                    globalPosField.setForeground(Color.WHITE);
                } else if (totalPosition < 0) {
                    globalPosField.setBackground(SHORT_POSITION_COLOR);
                    globalPosField.setForeground(Color.WHITE);
                } else {
                    globalPosField.setBackground(NEUTRAL_POSITION_COLOR);
                    globalPosField.setForeground(Color.BLACK);
                }
            }
        } catch (Exception e) {
            if (DEBUG_UI) Log.info("OrdersTestUIV3: Error updating positions: " + e.getMessage());
        } finally {
            synchronized (updateLock) {
                updatePending = false;
            }
        }
    }

    private String formatPnLWithPips(double pnl) {
        // Don't display PnL when it's zero or very close to zero
        if (Math.abs(pnl) < 0.0001) {
            return "";
        }
        
        // Format with appropriate precision
        String formattedPnL = String.format("%.2f", pnl);
        
        // Add pips indicator
        if (pnl > 0) {
            return "+" + formattedPnL + " ↑";
        } else {
            return formattedPnL + " ↓";
        }
    }

    public void updatePrice(double price) {
        if (uiState.get() != UIState.RUNNING && uiState.get() != UIState.INITIALIZING) {
            return;
        }

        // Update cached data immediately (thread-safe)
        cachedDisplayPrice = price; // Use the parameter instead of direct call
        // Remove this redundant calculation - cachedGlobalPnL should only be updated via updateGlobalPnL()
        // cachedGlobalPnL = globalUnrealizedPnL + globalRealizedPnL;
        needsPriceUpdate = true;
        
        // Track initial price data arrival for startup readiness
        if (!hasInitialPriceData && cachedDisplayPrice > 0.0) {
            hasInitialPriceData = true;
            if (DEBUG_UI) Log.info("OrdersTestUIV3: Initial price data received: " + cachedDisplayPrice);
        }

        // Maintain rolling price history for ATR calculation if needed
        synchronized (priceHistory) {
            if (Double.isFinite(price)) {
                priceHistory.add(price);
                if (priceHistory.size() > ATR_PERIOD + 2) {
                    priceHistory.remove(0);
                }
            }
        }

        // Try to initialize offsets if price is now valid and offsets aren't initialized
        if (cachedDisplayPrice > 0 && !offsetsInitialized) {
            updateOffsets();
            if (!offsetsInitialized) {
                double fallbackOffset = Math.max(1.0, cachedDisplayPrice * 0.005); // 0.5% of price or minimum 1.0
                SwingUtilities.invokeLater(() -> {
                    try {
                        if (limitOffsetField != null && limitOffsetField.getText().isEmpty()) {
                            limitOffsetField.setText(String.format("%.2f", fallbackOffset));
                        }
                        if (stopOffsetField != null && stopOffsetField.getText().isEmpty()) {
                            stopOffsetField.setText(String.format("%.2f", fallbackOffset));
                        }
                        if (ocoLimitOffsetField != null && ocoLimitOffsetField.getText().isEmpty()) {
                            ocoLimitOffsetField.setText(String.format("%.2f", fallbackOffset));
                        }
                        if (ocoStopOffsetField != null && ocoStopOffsetField.getText().isEmpty()) {
                            ocoStopOffsetField.setText(String.format("%.2f", fallbackOffset));
                        }
                        offsetsInitialized = true;
                        Log.info("OrdersTestUIV3: Set fallback offset values from updatePrice: " + fallbackOffset);
                    } catch (Exception ex) {
                        Log.error("Failed to set fallback values in updatePrice", ex);
                    }
                });
            }
        }
    }

    private void updateOffsets() {
        // Only attempt to initialize if not already done
        if (!offsetsInitialized) {
            try {
                double atrValue = -1;
                // Try to get ATR from strategyReference if available
                if (strategyReference != null) {
                    try {
                        // Use reflection to get 'atr' field if present
                        java.lang.reflect.Field atrField = strategyReference.getClass().getDeclaredField("atr");
                        atrField.setAccessible(true);
                        Object atrObj = atrField.get(strategyReference);
                        if (atrObj instanceof Number) {
                            atrValue = ((Number) atrObj).doubleValue();
                        }
                    } catch (Exception ignore) {
                        atrValue = -1;
                    }
                }
                // If no strategy or ATR not available, compute ATR(21) internally
                if (atrValue <= 0) {
                    atrValue = computeInternalATR();
                }
                final double atr = atrValue; // Make final for lambda
                double offsetValue;
                if (atr > 0) {
                    offsetValue = 10 * atr * instrumentInfo.pips;
                } else {
                    // Fallback: use 0.25% of cached price
                    offsetValue = cachedDisplayPrice > 0 ? cachedDisplayPrice * .0025 : 5.0;
                }
                final double finalOffsetValue = offsetValue; // Make final for lambda
                SwingUtilities.invokeLater(() -> {
                    try {
                        if (limitOffsetField != null) {
                            limitOffsetField.setText(String.format("%.2f", finalOffsetValue));
                        }
                        if (stopOffsetField != null) {
                            stopOffsetField.setText(String.format("%.2f", finalOffsetValue));
                        }
                        if (ocoLimitOffsetField != null) {
                            ocoLimitOffsetField.setText(String.format("%.2f", finalOffsetValue));
                        }
                        if (ocoStopOffsetField != null) {
                            ocoStopOffsetField.setText(String.format("%.2f", finalOffsetValue));
                        }
                        offsetsInitialized = true;
                        Log.info("OrdersTestUIV3: Offset fields initialized (ATR=" + atr + ")");
                    } catch (Exception ex) {
                        Log.error("Failed to set offset field values", ex);
                    }
                });
            } catch (Exception e) {
                // Fallback to default value
                SwingUtilities.invokeLater(() -> {
                    try {
                        double defaultOffset = 5.0;
                        if (limitOffsetField != null) {
                            limitOffsetField.setText(String.format("%.2f", defaultOffset));
                        }
                        if (stopOffsetField != null) {
                            stopOffsetField.setText(String.format("%.2f", defaultOffset));
                        }
                        if (ocoLimitOffsetField != null) {
                            ocoLimitOffsetField.setText(String.format("%.2f", defaultOffset));
                        }
                        if (ocoStopOffsetField != null) {
                            ocoStopOffsetField.setText(String.format("%.2f", defaultOffset));
                        }
                        offsetsInitialized = true;
                        Log.info("OrdersTestUIV3: Offset fields initialized with default value after error");
                    } catch (Exception ex) {
                        Log.error("Failed to set default offset field values", ex);
                    }
                });
            }
        }
    }

    // Compute ATR(21) using priceHistory (True Range)
    private double computeInternalATR() {
        synchronized (priceHistory) {
            if (priceHistory.size() < ATR_PERIOD + 1) {
                return -1;
            }
            double sumTR = 0.0;
            for (int i = 1; i <= ATR_PERIOD; i++) {
                double high = priceHistory.get(i);
                double low = priceHistory.get(i);
                double prevClose = priceHistory.get(i - 1);
                // For simplicity, use abs diff as true range (since we only have closes)
                double tr = Math.abs(high - prevClose);
                sumTR += tr;
            }
            return sumTR / ATR_PERIOD;
        }
    }

    private void playBeep(int count) {
        if (count <= 0) return;
        
        // Create a timer to space out multiple beeps
        Timer beepTimer = new Timer();
        synchronized(delayTimers) {
            delayTimers.add(beepTimer);
        }
        
        // Schedule beeps with delay between them
        for (int i = 0; i < count; i++) {
            beepTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    try {
                        Toolkit.getDefaultToolkit().beep();
                    } catch (Exception e) {
                        // Ignore beep errors
                    }
                }
            }, i * 200); // 200ms between beeps
        }
        
        // Schedule a task to clean up the timer
        beepTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                synchronized(delayTimers) {
                    delayTimers.remove(beepTimer);
                }
                beepTimer.cancel();
            }
        }, count * 200 + 100); // Clean up after all beeps complete
    }

    @Override
    public void appendStatus(String message) {
        if (message == null) {
            return; // Don't append null messages
        }
        
        Runnable updateOperation = () -> {
            if (logTextArea != null) {
                try {
                    logTextArea.append(message + "\n");
                    logTextArea.setCaretPosition(logTextArea.getDocument().getLength());
                } catch (Exception e) {
                    // Ignore append errors during shutdown or if UI is disposed
                }
            }
        };
        
        // Use direct EDT invocation during shutdown, safeEDTOperation otherwise
        UIState currentState = uiState.get();
        if (currentState == UIState.SHUTTING_DOWN) {
            if (SwingUtilities.isEventDispatchThread()) {
                updateOperation.run();
            } else {
                SwingUtilities.invokeLater(updateOperation);
            }
        } else {
            safeEDTOperation(updateOperation);
        }
    }

    private void cleanup() {
        // ═══ MODIFIED: Use coordinated shutdown ═══
        initiateShutdown();
    }
    
    // ═══ NEW: Separated UI Component Cleanup ═══
    private void cleanupUIComponents() {
        long shutdownStart = System.currentTimeMillis();
        Log.info("OrdersTestUIV3: Starting UI component cleanup...");
        
        // Dispose health status UI if it's open
        try {
            NubiaHealthStatusUI.disposeInstance();
        } catch (Exception e) {
            if (DEBUG_UI) Log.info("OrdersTestUIV3: Error disposing health status UI during cleanup: " + e.getMessage());
        }
        
        // Stop delay timers
        synchronized(delayTimers) {
            for (Timer timer : new ArrayList<>(delayTimers)) {
                try {
                    timer.cancel();
                    timer.purge();
                } catch (Exception e) {
                    if (DEBUG_UI) Log.info("OrdersTestUIV3: Error canceling timer: " + e.getMessage());
                }
            }
            delayTimers.clear();
        }
        
        final JFrame currentFrame; // ← Make final for lambda capture
        
        synchronized(LOCK) {
            currentFrame = frame;
            frame = null;
        }
        
        // ═══ UI CLEANUP ON EDT (ALWAYS ASYNC TO AVOID DEADLOCKS) ═══
        if (currentFrame != null) {
            try {
                // Save window position while frame is still valid
                if (currentFrame.isDisplayable()) {
                    saveWindowPosition();
                }
                
                // ═══ ALWAYS USE invokeLater FOR UI CLEANUP TO AVOID DEADLOCKS ═══
                SwingUtilities.invokeLater(() -> {
                    try {
                        // Clear UI state safely on EDT
                        if (statusWarningLabel != null) {
                            statusWarningLabel.setText("Status: OK");
                            statusWarningLabel.setForeground(new Color(0, 180, 0));
                            statusWarningLabel.setVisible(true);
                        }
                        
                        if (timeGateField != null) {
                            timeGateField.setText("");
                            timeGateField.setBackground(null);
                        }
                        
                        if (capsField != null) {
                            capsField.setText("");
                            capsField.setBackground(null);
                            capsField.setToolTipText(null);
                        }
                        
                        // Clear table data to prevent renderer exceptions
                        if (tableModel != null) {
                            tableModel.setRowCount(0);
                        }
                        
                        // Disable tooltips to prevent timer events
                        if (positionsTable != null) {
                            ToolTipManager.sharedInstance().unregisterComponent(positionsTable);
                        }
                        
                        // Dispose frame last
                        if (currentFrame.isDisplayable()) {
                            currentFrame.dispose();
                        }
                        
                        Log.info("OrdersTestUIV3: UI cleanup completed on EDT");
                        
                    } catch (Exception e) {
                        Log.warn("OrdersTestUIV3: Error during EDT cleanup", e);
                    }
                });
                
            } catch (Exception e) {
                Log.warn("OrdersTestUIV3: Error in UI cleanup setup", e);
                // Fallback: simple dispose
                SwingUtilities.invokeLater(() -> {
                    try {
                        if (currentFrame.isDisplayable()) {
                            currentFrame.dispose();
                        }
                    } catch (Exception ex) {
                        Log.warn("OrdersTestUIV3: Error in fallback frame disposal", ex);
                    }
                });
            }
        }
        
        long shutdownEnd = System.currentTimeMillis();
        double elapsed = (shutdownEnd - shutdownStart) / 1000.0;
        Log.info(String.format("OrdersTestUIV3: UI component cleanup completed in %.2f seconds", elapsed));
    }
    
    // ═══ NEW: Static State Cleanup ═══
    private void clearStaticState() {
        // Clear tracking maps
        orderClientIdToOrderId.clear();
        orderStartTimes.clear();
        
        // Reset flags under lock
        synchronized(updateLock) {
            updatePending = false;
        }
        
        // Final cleanup under main lock
        synchronized(LOCK) {
            // Null out all UI component references
            globalPnLField = null;
            displayPriceField = null;
            aliasField = null;
            globalPosField = null;
            timeGateField = null;
            capsField = null;
            positionsTable = null;
            tableModel = null;
            logTextArea = null;
            accountBalanceField = null;
            activeOrdersLabel = null;
            statusWarningLabel = null;
            liveTradingStatusLabel = null;
            liveTradingToggle = null;
            positionsScrollPane = null;
            logScrollPane = null;
            bidAskDiffGauge = null;
            diffGauge = null;
            
            // Clear offset fields
            limitOffsetField = null;
            stopOffsetField = null;
            ocoLimitOffsetField = null;
            ocoStopOffsetField = null;
            offsetsInitialized = false;
            
            // Clear additional UI references
            healthStatusToggle = null;
            runModeField = null;
            orderCapField = null;
            globalPositionLimitField = null;
            availableLiquidityField = null;
            
            // Clear collections
            priceHistory.clear();
        }
        
        // Reset static state
        registeredControllers.clear();
        lastUpdateTime = 0;
        
        // Reset status variables
        tradingDisabledReason = "";
        tradingDisabledUntil = 0L;
        currentBookmapMillis = 0L;
    }

    /**
     * Performs batched UI updates based on cached data and flags.
     * Called by the scheduler on the EDT to efficiently update UI components.
     */
    private void performBatchedUIUpdates() {
        if (uiState.get() != UIState.RUNNING || frame == null || !frame.isVisible()) {
            return;
        }

        try {
            // ═══ DATA VALIDATION DURING STARTUP ═══
            long timeSinceStartup = System.currentTimeMillis() - startupTimestamp;
            boolean isEarlyStartup = timeSinceStartup < 2000; // First 2 seconds
            
            // Skip updates if essential data isn't ready during early startup
            if (isEarlyStartup && (!hasInitialPriceData || !hasInitialPositionData)) {
                if (DEBUG_UI && timeSinceStartup > 1000) { // Log only after 1 second to avoid spam
                    Log.info("OrdersTestUIV3: Waiting for initial data (price:" + hasInitialPriceData + ", pos:" + hasInitialPositionData + ")");
                }
                return;
            }
            
            // Update price-related fields
            if (needsPriceUpdate) {
                needsPriceUpdate = false;
                
                if (globalPnLField != null) {
                    globalPnLField.setText(String.format("%.2f", cachedGlobalPnL));
                    // Set color based on P&L
                    if (cachedGlobalPnL > 0) {
                        globalPnLField.setBackground(LONG_POSITION_COLOR);
                        globalPnLField.setForeground(Color.WHITE);
                    } else if (cachedGlobalPnL < 0) {
                        globalPnLField.setBackground(SHORT_POSITION_COLOR);
                        globalPnLField.setForeground(Color.WHITE);
                    } else {
                        globalPnLField.setBackground(NEUTRAL_POSITION_COLOR);
                        globalPnLField.setForeground(Color.BLACK);
                    }
                }
                
                if (displayPriceField != null) {
                    displayPriceField.setText(formatPrice(cachedDisplayPrice));
                }
            }

            // Update position-related fields
            if (needsPositionUpdate) {
                needsPositionUpdate = false;
                
                if (globalPosField != null) {
                    globalPosField.setText(String.valueOf(cachedGlobalPosition));
                    // Set color based on position
                    if (cachedGlobalPosition > 0) {
                        globalPosField.setBackground(LONG_POSITION_COLOR);
                        globalPosField.setForeground(Color.WHITE);
                    } else if (cachedGlobalPosition < 0) {
                        globalPosField.setBackground(SHORT_POSITION_COLOR);
                        globalPosField.setForeground(Color.WHITE);
                    } else {
                        globalPosField.setBackground(NEUTRAL_POSITION_COLOR);
                        globalPosField.setForeground(Color.BLACK);
                    }
                }
                
                // Note: Table refresh is now handled by the slow scheduler
            }

            // Update account and orders
            if (needsAccountUpdate) {
                needsAccountUpdate = false;
                
                if (accountBalanceField != null) {
                    accountBalanceField.setText(String.format("%.2f ||  Position: %d", cachedAccountBalance, cachedAccountPosition));
                }
                if (activeOrdersLabel != null) {
                    String text = String.format("Sells-%d,Buys-%d", cachedWorkingSells, cachedWorkingBuys);
                    boolean hasOrders = (cachedWorkingSells > 0 || cachedWorkingBuys > 0);
                    activeOrdersLabel.setText(text);
                    activeOrdersLabel.setBackground(hasOrders ? Color.ORANGE : new Color(120, 200, 120));
                    activeOrdersLabel.setForeground(Color.BLACK);
                }
            }

            // Update alias
            if (needsAliasUpdate) {
                needsAliasUpdate = false;
                
                if (aliasField != null) {
                    aliasField.setText(cachedAlias);
                }
            }

            // Update live trading status
            if (needsLiveTradingUpdate) {
                needsLiveTradingUpdate = false;
                
                if (liveTradingStatusLabel != null) {
                    liveTradingStatusLabel.setText("Live Trading: " + (cachedLiveTradingEnabled ? "ON" : "OFF"));
                }
                if (liveTradingToggle != null) {
                    liveTradingToggle.setSelected(cachedLiveTradingEnabled);
                    liveTradingToggle.setText(cachedLiveTradingEnabled ? "Trading ON" : "Trading OFF");
                    updateLiveTradingToggleColor(cachedLiveTradingEnabled);
                }
            }

            // Update time gate fields
            if (needsTimeGateUpdate) {
                needsTimeGateUpdate = false;
                
                if (timeGateField != null && capsField != null && orderCapField != null) {
                    final String secs = cachedSecsToNext > 0 ? 
                        String.format(" (%d:%02d)", cachedSecsToNext/60, cachedSecsToNext%60) : "";
                    
                    // Set background color based on state
                    switch(cachedTimeGateState) {
                        case ACTIVE:
                            timeGateField.setBackground(GATE_ACTIVE);
                            break;
                        case THIN_LIQUIDITY:
                            timeGateField.setBackground(GATE_THIN);
                            break;
                        case CLOSE_ONLY:
                            timeGateField.setBackground(GATE_CLOSE);
                            break;
                        case BLOCKED:
                            timeGateField.setBackground(GATE_BLOCKED);
                            break;
                    }
                    
                    timeGateField.setText(cachedTimeGateState.name() + secs);
                    capsField.setText(String.valueOf(cachedPosCap));      // Position Cap only
                    orderCapField.setText(String.valueOf(cachedOrdCap)); // Order Cap only
                }
            }

            // Update new fields
            if (needsGlobalPositionLimitUpdate) {
                needsGlobalPositionLimitUpdate = false;
                
                if (globalPositionLimitField != null) {
                    globalPositionLimitField.setText(String.valueOf(cachedGlobalPositionLimit));
                }
            }
            
            if (needsAvailableLiquidityUpdate) {
                needsAvailableLiquidityUpdate = false;
                
                if (availableLiquidityField != null) {
                    availableLiquidityField.setText(String.format("%.2f", cachedAvailableLiquidity));
                }
            }
            
            // Update RunMode field periodically (every few cycles to avoid excessive API calls)
            if (needsRunModeUpdate || (System.currentTimeMillis() % 5000 < 200)) { // Update every ~5 seconds
                needsRunModeUpdate = false;
                updateRunModeField();
            }

        } catch (Exception e) {
            if (DEBUG_UI) Log.info("OrdersTestUIV3: Error in batched UI update: " + e.getMessage());
        }
    }

    private void startScheduler() {
        // Don't start schedulers if we're not in running state
        if (uiState.get() != UIState.RUNNING && uiState.get() != UIState.INITIALIZING) {
            Log.info("OrdersTestUIV3: Skipping scheduler startup - not in running state");
            return;
        }
        
        // Shutdown existing schedulers
        if (scheduler != null) {
            try {
                scheduler.shutdownNow();
            } catch (Exception e) {
                if (DEBUG_UI) Log.info("OrdersTestUIV3: Error shutting down existing scheduler: " + e.getMessage());
            }
        }
        if (fastScheduler != null) {
            try {
                fastScheduler.shutdownNow();
            } catch (Exception e) {
                if (DEBUG_UI) Log.info("OrdersTestUIV3: Error shutting down existing fast scheduler: " + e.getMessage());
            }
        }

        final ThreadGroup tg = new ThreadGroup("OrdersTestUIV3-Threads");
        
        // ═══ FAST SCHEDULER (100ms) - Responsive UI Updates ═══
        fastScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(tg, r, "Fast-UI-Updater");
            t.setDaemon(true);
            t.setPriority(Thread.MIN_PRIORITY + 2); // Slightly higher priority for responsiveness
            t.setUncaughtExceptionHandler((thread, ex) -> {
                if (DEBUG_UI) Log.info("OrdersTestUIV3: Uncaught exception in fast UI updater: " + ex.getMessage());
            });
            return t;
        });
        
        fastScheduler.scheduleAtFixedRate(() -> {
            try {
                // ═══ STARTUP-AWARE CONDITIONS ═══
                if (frame == null || !frame.isVisible() || uiState.get() == UIState.SHUTTING_DOWN || uiState.get() == UIState.DISPOSED) {
                    return;
                }
                
                // Progressive startup: Allow UI updates even without realtime initially
                long timeSinceStartup = System.currentTimeMillis() - startupTimestamp;
                boolean isStartupPhase = timeSinceStartup < 10000; // First 10 seconds
                boolean canUpdate = isStartupPhase || isRealtime; // Use cached realtime status
                
                if (!canUpdate) {
                    if (DEBUG_SCHEDULER && timeSinceStartup > 12000) { // Log after startup phase has passed
                        Log.info("OrdersTestUIV3: Fast scheduler blocked - isRealtime=" + isRealtime + 
                                ", timeSinceStartup=" + timeSinceStartup + "ms");
                    }
                    return;
                }
                
                SwingUtilities.invokeLater(() -> {
                    try {
                        // ═══ FAST BATCH UI UPDATES ═══
                        performBatchedUIUpdates();
                        
                        // Mark startup complete after first successful update cycle
                        if (!startupComplete && timeSinceStartup > 1000) {
                            startupComplete = true;
                            if (DEBUG_UI) Log.info("OrdersTestUIV3: Startup phase completed");
                        }
                        
                    } catch (Throwable t) {
                        if (DEBUG_UI) Log.info("OrdersTestUIV3: Error in fast scheduled update: " + t.getMessage());
                    }
                });
            } catch (Throwable t) {
                if (DEBUG_UI) Log.info("OrdersTestUIV3: Error in fast scheduler: " + t.getMessage());
            }
        }, 100, 100, TimeUnit.MILLISECONDS); // ← 100ms for responsive updates

        // ═══ SLOW SCHEDULER (1s) - Heavy Operations ═══
        scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(tg, r, "Slow-UI-Updater");
            t.setDaemon(true);
            t.setPriority(Thread.MIN_PRIORITY + 1);
            t.setUncaughtExceptionHandler((thread, ex) -> {
                if (DEBUG_UI) Log.info("OrdersTestUIV3: Uncaught exception in slow UI updater: " + ex.getMessage());
            });
            return t;
        });
        
        scheduler.scheduleAtFixedRate(() -> {
            try {
                // ═══ STARTUP-AWARE CONDITIONS ═══
                if (frame == null || !frame.isVisible() || uiState.get() == UIState.SHUTTING_DOWN || uiState.get() == UIState.DISPOSED) {
                    return;
                }
                
                // Progressive startup: More conservative for heavy operations
                long timeSinceStartup = System.currentTimeMillis() - startupTimestamp;
                boolean isStartupPhase = timeSinceStartup < 15000; // First 15 seconds for slow scheduler
                boolean canUpdate = isStartupPhase || isRealtime; // Use cached realtime status
                
                if (!canUpdate) {
                    return;
                }
                
                SwingUtilities.invokeLater(() -> {
                    try {
                        // ═══ SLOW/HEAVY OPERATIONS ═══
                        if (!shouldThrottleUpdate()) {
                            refreshPositionsTable(); // Heavy table operations
                        }
                        if (!offsetsInitialized && hasInitialPriceData) {
                            updateOffsets(); // Configuration checks (only if price data available)
                        }
                        updateStatusWarningLabel(); // Status warnings
                        
                        // ═══ TIME GATE DATA ═══
                        // Note: TimeGate data now comes through updateTimeGate() interface calls
                        // The OrderSenderControllerV2 should call updateTimeGate() when data changes
                        // No direct fetching needed here - data-driven approach
                        
                    } catch (Throwable t) {
                        if (DEBUG_UI) Log.info("OrdersTestUIV3: Error in slow scheduled update: " + t.getMessage());
                    }
                });
            } catch (Throwable t) {
                if (DEBUG_UI) Log.info("OrdersTestUIV3: Error in slow scheduler: " + t.getMessage());
            }
        }, 1, 1, TimeUnit.SECONDS); // ← 1s for heavy operations

        // ═══ INITIAL SETUP (One-time) ═══
        scheduler.schedule(() -> {
            try {
                SwingUtilities.invokeLater(() -> {
                    if (!offsetsInitialized) {
                        updateOffsets();
                        if (DEBUG_UI) Log.info("OrdersTestUIV3: Performing initial offset check");
                    }
                    // Initial update for loss limit warning label
                    updateStatusWarningLabel();
                });
            } catch (Throwable t) {
                if (DEBUG_UI) Log.info("OrdersTestUIV3: Error in initial setup: " + t.getMessage());
            }
        }, 2, TimeUnit.SECONDS);
    }

    private void logError(String action, Exception e) {
        // (8) Guard debug logs with flag
        if (DEBUG_UI) {
            final String errorMsg = String.format("OrdersTestUIV3: Error in %s: %s\n", action, e.getMessage());
            Log.info(errorMsg);
        }
        if (logTextArea != null) {
            SwingUtilities.invokeLater(() -> {
                try {
                    logTextArea.append("Error in " + action + ": " + e.getMessage() + "\n");
                    logTextArea.setCaretPosition(logTextArea.getDocument().getLength());
                } catch (Exception ex) {}
            });
        }
    }

    // Add an overloaded version of logOrder that generates its own clientId
    private void logOrder(boolean success, String orderType, double price) {
        // Generate a unique client ID for tracking
        final String clientId = "UI_" + System.currentTimeMillis();
        orderStartTimes.put(clientId, System.currentTimeMillis());
        
        // Call the main implementation
        logOrder(success, orderType, price, clientId);
    }
    
    // Main implementation that takes a clientId
    private void logOrder(boolean success, String orderType, double price, String clientId) {
        final String status = success ? "submitted" : "rejected";
        final String formattedPrice = formatPnLWithPips(price);
        final String timestamp = new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new Date());
        
        // Store the client ID for tracking the actual order lifecycle
        
        // Ensure UI updates happen on EDT
        SwingUtilities.invokeLater(() -> {
            try {
                logTextArea.append(String.format("[%s] %s order %s at price %s (tracking ID: %s)\n", 
                    timestamp, orderType, status, formattedPrice, clientId));
                
                // Scroll to bottom of log
                logTextArea.setCaretPosition(logTextArea.getDocument().getLength());
            } catch (Exception e) {
                // Ignore append errors
            }
        });
    }

    public void close() {
        dispose();
    }

    public void dispose() {
        // ═══ MODIFIED: Set state instead of boolean flag ═══
        uiState.set(UIState.SHUTTING_DOWN);
        
        long shutdownStart = System.currentTimeMillis();
        
        synchronized (LOCK) {
            // Disable all tooltips to prevent tooltip timer events during shutdown
            try {
                if (positionsTable != null) {
                    // Disable tooltip manager for the table - this is thread-safe
                    ToolTipManager.sharedInstance().unregisterComponent(positionsTable);
                    
                    // Clear table model content on EDT to avoid threading violations
                    if (tableModel != null) {
                        // Make sure this operation runs on the EDT
                        if (SwingUtilities.isEventDispatchThread()) {
                            // Already on EDT, call directly
                            tableModel.setRowCount(0);
                        } else {
                            try {
                                // We have to use invokeAndWait because we're in dispose
                                SwingUtilities.invokeAndWait(() -> {
                                    tableModel.setRowCount(0);
                                });
                            } catch (Exception ex) {
                                // Just log and continue if there's an error
                                if (DEBUG_UI) Log.info("Error in EDT for table clear: " + ex.getMessage());
                            }
                        }
                    }
                }
            } catch (Exception e) {
                if (DEBUG_UI) Log.info("Error disabling tooltips: " + e.getMessage());
            }
            
            // Cancel all timers first
            if (scheduler != null && !scheduler.isShutdown()) {
                scheduler.shutdownNow();
                scheduler = null;
            }
            
            if (fastScheduler != null && !fastScheduler.isShutdown()) {
                fastScheduler.shutdownNow();
                fastScheduler = null;
            }
            
            delayTimers.forEach(Timer::cancel);
            delayTimers.clear();
            
            // Now handle frame disposal
            if (frame != null) {
                // Ensure disposal on the EDT
                if (SwingUtilities.isEventDispatchThread()) {
                    frame.dispose();
                } else {
                    // Use invokeLater for frame disposal to avoid blocking and potential deadlocks
                    SwingUtilities.invokeLater(() -> {
                        if (frame != null) { // Re-check frame as dispose is now async
                            frame.dispose();
                        }
                    });
                }
                frame = null;
                // ═══ MODIFIED: State is managed by uiState, no separate initialized flag ═══
            }
            
            // All Swing-related cleanup should be done by now
            
            // Final cleanup - clearing references can be done on any thread
            tableModel = null;
            positionsTable = null;
        }
        
        long shutdownEnd = System.currentTimeMillis();
        double elapsed = (shutdownEnd - shutdownStart) / 1000.0;
        Log.info(String.format("OrdersTestUIV3 shutdown took %.2f seconds", elapsed));
    }

    public static void disposeInstance() {
        // ═══ MODIFIED: Set state instead of boolean flag ═══
        uiState.set(UIState.SHUTTING_DOWN);
        
        // Thread-safely work with the instance
        final OrdersTestUIV3 localInstance;
        synchronized (instanceLock) {
            localInstance = instance;
            // Clear the instance reference early to prevent concurrent access
            instance = null;
        }
        
        // Now dispose the instance outside the synchronization block
        if (localInstance != null) {
            try {
                // Since dispose() handles EDT requirements internally,
                // we can call it directly from any thread
                localInstance.dispose();
            } catch (Exception e) {
                Log.info("OrdersTestUIV3: Error in disposeInstance: " + e.getMessage());
            }
        }
        
        // When disposeInstance is called outside of resetInstance, we need to also clean static state
        if (!Thread.holdsLock(instanceLock)) {
            try {
                synchronized (instanceLock) {
                    registeredControllers.clear();
                    frame = null;
                    // ═══ MODIFIED: State is managed by uiState, no separate initialized flag ═══
                    // Reset state so UI can be recreated
                    uiState.set(UIState.DISPOSED);
                }
            } catch (Exception e) {
                Log.info("OrdersTestUIV3: Error clearing state in disposeInstance: " + e.getMessage());
            }
        }
    }

    /**
     * Completely resets the singleton and all static state.
     * This method is used by tests to ensure a clean state.
     */
    public static void resetInstance() {
        // ═══ MODIFIED: Set state instead of boolean flag ═══
        uiState.set(UIState.SHUTTING_DOWN);
        
        OrdersTestUIV3 localInstance;
        
        // Use a smaller synchronized block to avoid deadlocks
        synchronized (instanceLock) {
            localInstance = instance;
            instance = null;
        }
        
        // Dispose instance outside of synchronization
        if (localInstance != null) {
            try {
                if (SwingUtilities.isEventDispatchThread()) {
                    // Already on EDT, execute directly
                    localInstance.dispose();
                } else {
                    try {
                        // Use invokeAndWait to ensure disposal completes before proceeding
                        final OrdersTestUIV3 finalInstance = localInstance;
                        SwingUtilities.invokeAndWait(() -> {
                            try {
                                finalInstance.dispose();
                            } catch (Exception ex) {
                                Log.info("OrdersTestUIV3: Error disposing in EDT: " + ex.getMessage());
                            }
                        });
                    } catch (Exception ex) {
                        Log.info("OrdersTestUIV3: Error in EDT for disposal: " + ex.getMessage());
                    }
                }
            } catch (Exception e) {
                Log.info("OrdersTestUIV3: Error disposing instance: " + e.getMessage());
            }
        }
        
        // Clear all static state
        synchronized (instanceLock) {
            try {
                registeredControllers.clear();
                frame = null;
                // ═══ MODIFIED: State is managed by uiState, no separate initialized flag ═══
            } catch (Exception e) {
                Log.info("OrdersTestUIV3: Error clearing state: " + e.getMessage());
            } finally {
                // Always reset the state at the end
                uiState.set(UIState.DISPOSED);
            }
        }
    }

    /**
     * Helper method to recursively enable/disable all components
     */
    private void setComponentsEnabled(Component[] components, boolean enabled) {
        if (components == null) return;
        
        for (Component component : components) {
            component.setEnabled(enabled);
            
            if (component instanceof Container) {
                setComponentsEnabled(((Container)component).getComponents(), enabled);
            }
        }
    }

    // Format price with fixed decimal places
    private String formatPrice(double price) {
        return String.format("%.2f", price);
    }
    
    // Count decimals in a double
    private int countDecimals(double num) {
        String str = Double.toString(num);
        int decimalIndex = str.indexOf('.');
        if (decimalIndex < 0) return 0;
        return str.length() - decimalIndex - 1;
    }



    /**
     * Save the window position and size to preferences
     */
    private void saveWindowPosition() {
        if (frame != null) {
            try {
                Preferences prefs = Preferences.userNodeForPackage(OrdersTestUIV3.class);
                prefs.putInt("window.x", frame.getX());
                prefs.putInt("window.y", frame.getY());
                prefs.putInt("window.width", frame.getWidth());
                prefs.putInt("window.height", frame.getHeight());
                prefs.flush();
            } catch (Exception e) {
                Log.info("OrdersTestUIV3: Error saving window position: " + e.getMessage());
            }
        }
    }
    
    /**
     * Restore the window position and size from preferences
     */
    private void restoreWindowPosition() {
        try {
            Preferences prefs = Preferences.userNodeForPackage(OrdersTestUIV3.class);
            int x = prefs.getInt("window.x", -1);
            int y = prefs.getInt("window.y", -1);
            int width = prefs.getInt("window.width", -1);
            int height = prefs.getInt("window.height", -1);
            
            if (x != -1 && y != -1) {
                // Check if the saved position is on a visible screen
                boolean onScreen = false;
                for (GraphicsDevice screen : GraphicsEnvironment.getLocalGraphicsEnvironment().getScreenDevices()) {
                    Rectangle bounds = screen.getDefaultConfiguration().getBounds();
                    if (bounds.contains(x, y)) {
                        onScreen = true;
                        break;
                    }
                }
                
                if (onScreen) {
                    frame.setLocation(x, y);
                    if (width != -1 && height != -1) {
                        frame.setSize(width, height);
                    }
                } else {
                    // If not on screen, center the window
                    frame.setLocationRelativeTo(null);
                }
            } else {
                // Default to center on screen if no saved position
                frame.setLocationRelativeTo(null);
            }
        } catch (Exception e) {
            // If there's an error, just center the window
            frame.setLocationRelativeTo(null);
            Log.info("OrdersTestUIV3: Error restoring window position: " + e.getMessage());
        }
    }

    // Add helper method to adjust table height
    private void adjustTableHeight(int totalRows) {
        // (4) Only adjust if row count changed
        if (positionsScrollPane == null || positionsTable == null) {
            return;
        }
        try {
            int rowHeight = positionsTable.getRowHeight();
            int headerHeight = positionsTable.getTableHeader().getPreferredSize().height;
            
            // Ensure minimum rows are visible (at least 8 rows)
            int minRows = 8;
            totalRows = Math.max(totalRows, minRows);
            int tableHeight = (rowHeight * totalRows) + headerHeight + 30;
            Dimension currentSize = positionsScrollPane.getPreferredSize();
            if (currentSize.height != tableHeight) {
                positionsScrollPane.setPreferredSize(new Dimension(currentSize.width, tableHeight));
                if (frame != null) {
                    frame.revalidate();
                }
            }
        } catch (Exception e) {
            if (DEBUG_UI) Log.info("OrdersTestUIV3: Error adjusting table height: " + e.getMessage());
        }
    }

    // Optimize the updatePositions method to be more efficient
    @Override
    public void updatePositions(int instancePosition, int globalPosition) {
        // Update cached data immediately (thread-safe)
        cachedInstancePosition = instancePosition;
        cachedGlobalPosition = globalPosition;
        needsPositionUpdate = true;
        
        // Track initial position data arrival for startup readiness
        if (!hasInitialPositionData) {
            hasInitialPositionData = true;
            if (DEBUG_UI) Log.info("OrdersTestUIV3: Initial position data received: instance=" + instancePosition + ", global=" + globalPosition);
        }
        
        // Note: Actual UI update will happen in the scheduler
    }

    // Method to log order information
    @Override
    public void logOrderUpdate(String orderId, String clientId, String status, boolean isBuy, double price) {
        final String timestamp = new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new Date());
        String elapsed = "";
        
        if (clientId != null) {
            // Store order mapping for future reference
            orderClientIdToOrderId.put(clientId, orderId);
            
            if (orderStartTimes.containsKey(clientId)) {
                long elapsedMs = System.currentTimeMillis() - orderStartTimes.get(clientId);
                elapsed = String.format(" [+%dms]", elapsedMs);
            }
        }
        
        final String orderIdDisplay = orderId.length() > 8 ? 
            orderId.substring(orderId.length() - 8) : orderId;
                
        final String statusMessage = String.format("[%s]%s Order %s: %s → %s (Price: %s)", 
            timestamp, 
            elapsed,
            orderIdDisplay, // Last 8 chars of order ID
            isBuy ? "BUY" : "SELL",
            status,
            formatPnLWithPips(price));
            
        appendStatus(statusMessage);
        
        // If order is complete, clean up tracking
        if (status.equals("FILLED") || status.equals("CANCELED") || status.equals("REJECTED")) {
            if (clientId != null && orderStartTimes.containsKey(clientId)) {
                orderStartTimes.remove(clientId);
            }
        }
    }
    
    // Method to log execution information
    @Override
    public void logExecution(String orderId, int size, double price) {
        final String timestamp = new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new Date());
        
        // Try to find the client ID from our tracking map
        String clientId = null;
        for (Map.Entry<String, String> entry : orderClientIdToOrderId.entrySet()) {
            if (entry.getValue().equals(orderId)) {
                clientId = entry.getKey();
                break;
            }
        }
        
        String elapsed = "";
        if (clientId != null && orderStartTimes.containsKey(clientId)) {
            long elapsedMs = System.currentTimeMillis() - orderStartTimes.get(clientId);
            elapsed = String.format(" [+%dms]", elapsedMs);
            
            // Remove from tracking after execution
            orderStartTimes.remove(clientId);
        }
        
        final String orderIdDisplay = orderId.length() > 8 ? 
            orderId.substring(orderId.length() - 8) : orderId;
        
        final String fillMessage = String.format("[%s]%s Order %s: FILLED %d @ %s", 
            timestamp,
            elapsed,
            orderIdDisplay,
            size,
            formatPnLWithPips(price));
            
        appendStatus(fillMessage);
    }
    
    // Method to log position updates
    @Override
    public void logPosition(int position, int workingBuys, int workingSells) {
        final String timestamp = new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new Date());
        final String posMessage = String.format("[%s] Position update: %d (Buys: %d, Sells: %d)", 
            timestamp,
            position,
            workingBuys,
            workingSells);
            
        appendStatus(posMessage);
    }
    
    // Method to log balance updates
    @Override
    public void logBalance(double balance, String currency) {
        final String timestamp = new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new Date());
        final String balMessage = String.format("[%s] Balance update: %.2f %s", 
            timestamp,
            balance,
            currency);
            
        appendStatus(balMessage);
    }

    // Add setter for live trading status
    public void setEnableLiveTrading(boolean enable) {
        // Update global state immediately
        OrderSenderControllerV2.setGlobalEnableLiveTrading(enable);
        
        // Ensure EDT execution for UI updates with null safety
        if (SwingUtilities.isEventDispatchThread()) {
            if (uiState.get() == UIState.RUNNING) {
                updateLiveTradingUIElements(enable);
            }
        } else {
            SwingUtilities.invokeLater(() -> {
                if (uiState.get() == UIState.RUNNING) {
                    updateLiveTradingUIElements(enable);
                }
            });
        }
    }
    
    // Helper method to update live trading UI elements (must be called on EDT)
    private void updateLiveTradingUIElements(boolean enable) {
        if (liveTradingStatusLabel != null) {
            liveTradingStatusLabel.setText("Live Trading: " + (enable ? "ON" : "OFF"));
        }
        if (liveTradingToggle != null) {
            liveTradingToggle.setSelected(enable);
            liveTradingToggle.setText(enable ? "Trading ON" : "Trading OFF");
            updateLiveTradingToggleColor(enable);
        }
    }

    // Add setter for toggle listener
    public void setLiveTradingToggleListener(java.util.function.Consumer<Boolean> listener) {
        this.liveTradingToggleListener = listener;
    }

    // Helper to build the live trading row panel
    private JPanel buildLiveTradingTopRowPanel() {
        JPanel topRow = new JPanel(new BorderLayout());

        // Left side: live trading label and toggle
        JPanel leftPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 8, 0));
        liveTradingStatusLabel = new JLabel("Live Trading: " + (cachedLiveTradingEnabled ? "ON" : "OFF"));
        liveTradingToggle = new JToggleButton(cachedLiveTradingEnabled ? "Trading ON" : "Trading OFF");
        liveTradingToggle.setSelected(cachedLiveTradingEnabled);
        updateLiveTradingToggleColor(cachedLiveTradingEnabled, liveTradingToggle);

        liveTradingToggle.addActionListener(e -> {
            JToggleButton source = (JToggleButton) e.getSource();
            boolean enabled = source.isSelected();
            OrderSenderControllerV2.setGlobalEnableLiveTrading(enabled);
            liveTradingStatusLabel.setText("Live Trading: " + (enabled ? "ON" : "OFF"));
            source.setText(enabled ? "Trading ON" : "Trading OFF");
            updateLiveTradingToggleColor(enabled, source);
            if (liveTradingToggleListener != null) {
                liveTradingToggleListener.accept(enabled);
            }
        });
        leftPanel.add(liveTradingStatusLabel);
        leftPanel.add(liveTradingToggle);

        // Center: Status warning label
        JPanel centerPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 0, 0));
        statusWarningLabel = new JLabel(""); // Create the status label
        statusWarningLabel.setFont(statusWarningLabel.getFont().deriveFont(Font.BOLD));
        statusWarningLabel.setHorizontalAlignment(SwingConstants.CENTER);
        statusWarningLabel.setVisible(false); // Start hidden
        centerPanel.add(statusWarningLabel);

        // Right side: RunMode field (replaces Performance Dashboard button)
        JPanel rightPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 0, 0));
        runModeField = new JTextField(20); // RunMode display field
        runModeField.setEditable(false);
        runModeField.setBorder(BorderFactory.createTitledBorder("Run Mode"));
        runModeField.setHorizontalAlignment(JTextField.CENTER);
        
        // Initialize RunMode field
        updateRunModeField();
        
        rightPanel.add(runModeField);

        topRow.add(leftPanel, BorderLayout.WEST);
        topRow.add(centerPanel, BorderLayout.CENTER);
        topRow.add(rightPanel, BorderLayout.EAST);

        return topRow;
    }

    // Add this helper method in the class
    private void updateLiveTradingToggleColor(boolean enabled, JToggleButton toggleButton) {
        if (toggleButton != null) {
            toggleButton.setBackground(enabled ? ENABLED_BG : DISABLED_BG);
            toggleButton.setOpaque(true);
        }
    }

    // Keep the original method for backward compatibility
    private void updateLiveTradingToggleColor(boolean enabled) {
        updateLiveTradingToggleColor(enabled, liveTradingToggle);
    }

    // Rename the updateLossLimitLabel method and modify its logic
    // OLD Method Name: updateLossLimitLabel
    // NEW Method Name: updateStatusWarningLabel
    @Override
    public void updateStatusWarningLabel() {
        // Ensure EDT execution with null safety
        if (SwingUtilities.isEventDispatchThread()) {
            updateStatusWarningLabelImpl();
        } else {
            SwingUtilities.invokeLater(this::updateStatusWarningLabelImpl);
        }
    }
    
    // Implementation method that must be called on EDT
    private void updateStatusWarningLabelImpl() {
        if (statusWarningLabel == null || uiState.get() != UIState.RUNNING) {
            return;
        }

        try {
            // --- Priority: explicit trading disable reason ---
            if (!tradingDisabledReason.isEmpty()) {
                String msg = "Trading Disabled: " + tradingDisabledReason;
                // Use Bookmap time for countdowns
                if (tradingDisabledUntil > currentBookmapMillis && currentBookmapMillis > 0) {
                    long secondsLeft = (tradingDisabledUntil - currentBookmapMillis) / 1000;
                    if (secondsLeft < 0) secondsLeft = 0;
                    long min = secondsLeft / 60;
                    long sec = secondsLeft % 60;
                    msg += String.format(" (%02d:%02d remaining)", min, sec);
                } else if (tradingDisabledUntil > 0 && currentBookmapMillis > 0) {
                    // If time has passed, clear the reason
                    tradingDisabledReason = "";
                    tradingDisabledUntil = 0L;
                    msg = "Status: OK";
                }
                statusWarningLabel.setText(msg);
                statusWarningLabel.setForeground(new Color(220, 0, 0)); // Bright red for disable
                statusWarningLabel.setVisible(true);
                return;
            }
            
            // --- Fallback: legacy loss/profit warnings ---
            boolean showLossWarning = false;
            boolean showProfitWarning = false;
            String profitWarningMessage = "";
            
            // Safely iterate over controllers
            for (OrderSenderControllerV2 controller : registeredControllers.values()) {
                if (controller != null) {
                    if (controller.isLossLimitCloseOnly()) {
                        showLossWarning = true;
                        break;
                    }
                    if (!showLossWarning && controller.isProfitTargetCloseOnly()) {
                        showProfitWarning = true;
                        profitWarningMessage = controller.getProfitTargetStatusMessage();
                    }
                }
            }
            
            // Update UI based on warnings
            if (showLossWarning) {
                statusWarningLabel.setText("LOSS LIMIT - CLOSE ONLY");
                statusWarningLabel.setForeground(Color.RED);
                statusWarningLabel.setVisible(true);
            } else if (showProfitWarning) {
                statusWarningLabel.setText(profitWarningMessage);
                statusWarningLabel.setForeground(new Color(199, 21, 133));
                statusWarningLabel.setVisible(true);
            } else {
                statusWarningLabel.setText("Status: OK");
                statusWarningLabel.setForeground(new Color(0, 180, 0)); // Bright green for normal
                statusWarningLabel.setVisible(true);
            }
        } catch (Exception e) {
            // Handle any exceptions gracefully
            if (DEBUG_UI) {
                Log.info("OrdersTestUIV3: Error in updateStatusWarningLabel: " + e.getMessage());
            }
        }
    }

    @Override
    public boolean isReady() {
        return frame != null && frame.isVisible();
    }

    public void deregisterController(String algoName) {
        if (algoName == null) return;

        final OrderSenderControllerV2[] removedController = new OrderSenderControllerV2[1];
        synchronized (LOCK) {
            removedController[0] = registeredControllers.remove(algoName);
        }

        if (removedController[0] != null) {
            if (DEBUG_UI) Log.info("OrdersTestUIV3: Deregistered controller: " + algoName);

            SwingUtilities.invokeLater(() -> {
                synchronized (LOCK) {
                    if (tableModel != null) {
                        for (int i = 0; i < tableModel.getRowCount(); i++) {
                            if (algoName.equals(tableModel.getValueAt(i, 0))) {
                                tableModel.removeRow(i);
                                break;
                            }
                        }
                    }
                    updateStatusWarningLabel(); // Refresh based on remaining controllers
                    if (positionsTable != null && tableModel != null) {
                         adjustTableHeight(tableModel.getRowCount());
                    }

                    // If all controllers are gone, ensure status label is reset
                    // This is also handled by updateStatusWarningLabel if registeredControllers is empty
                    if (registeredControllers.isEmpty()) {
                        if (statusWarningLabel != null) {
                             statusWarningLabel.setText("Status: OK"); 
                             statusWarningLabel.setForeground(UIManager.getColor("Label.foreground"));
                             statusWarningLabel.setVisible(true); 
                        }
                    }
                }
            });
        } else {
            if (DEBUG_UI) Log.info("OrdersTestUIV3: Controller not found for deregistration: " + algoName);
        }
    }

    /**
     * Update the Account Balance and Active Orders row.
     * Call this from OrderSenderControllerV2.onStatus(StatusInfo statusInfo)
     */
    @Override
    public void updateAccountAndOrders(double lastAccountBalance, int workingSells, int workingBuys,int lastAccountPosition) {
        // Update cached data immediately (thread-safe)
        cachedAccountBalance = lastAccountBalance;
        cachedWorkingSells = workingSells;
        cachedWorkingBuys = workingBuys;
        cachedAccountPosition = lastAccountPosition;
        needsAccountUpdate = true;
        
        // Note: Actual UI update will happen in the scheduler
    }

    /* ──────────────────────────────────────────────────────────────
     *  OrderUIDataListener – extra methods required by the contract
     * ────────────────────────────────────────────────────────────── */

    /** Bridge the new interface call to the existing helper. */
    @Override
    public void setEnableLiveTradingStatus(boolean enabled) {
        // Update cached data immediately (thread-safe)
        cachedLiveTradingEnabled = enabled;
        needsLiveTradingUpdate = true;
        
        // Note: Actual UI update will happen in the scheduler
    }

    /** Interface variant carrying explicit position fields. */
  

    String AliasfromInterface;
    @Override
    public void updateAlias(String alias) {
        // Update cached data immediately (thread-safe)
        cachedAlias = alias != null ? alias : "";
        needsAliasUpdate = true;
        
        // Note: Actual UI update will happen in the scheduler
    }

    @Override
    public void updateGlobalPnL(double unrealizedPnL, double realizedPnL, double totalPnL) {
        // Update non-UI data immediately
        this.globalUnrealizedPnL = unrealizedPnL;
        this.globalRealizedPnL = realizedPnL;
        
        // Ensure EDT execution for UI updates with null safety
        if (SwingUtilities.isEventDispatchThread()) {
            if (uiState.get() == UIState.RUNNING) {
                // Update cached P&L and trigger UI refresh  
                cachedGlobalPnL = totalPnL;
                needsPriceUpdate = true; // Trigger price field update which includes P&L
                
                // IMMEDIATE UPDATE: Update Global P&L field immediately to ensure it always shows correct value
                if (globalPnLField != null) {
                    globalPnLField.setText(String.format("%.2f", totalPnL));
                    // Set color based on P&L
                    if (totalPnL > 0) {
                        globalPnLField.setBackground(LONG_POSITION_COLOR);
                        globalPnLField.setForeground(Color.WHITE);
                    } else if (totalPnL < 0) {
                        globalPnLField.setBackground(SHORT_POSITION_COLOR);
                        globalPnLField.setForeground(Color.WHITE);
                    } else {
                        globalPnLField.setBackground(NEUTRAL_POSITION_COLOR);
                        globalPnLField.setForeground(Color.BLACK);
                    }
                }
                
                if (tableModel != null) {
                    refreshPositionsTable(); // Refresh table to update total row
                }
            }
        } else {
            SwingUtilities.invokeLater(() -> {
                if (uiState.get() == UIState.RUNNING) {
                    // Update cached P&L and trigger UI refresh  
                    cachedGlobalPnL = totalPnL;
                    needsPriceUpdate = true; // FAST scheduler
                    
                    // IMMEDIATE UPDATE: Update Global P&L field immediately to ensure it always shows correct value
                    if (globalPnLField != null) {
                        globalPnLField.setText(String.format("%.2f", totalPnL));
                        // Set color based on P&L
                        if (totalPnL > 0) {
                            globalPnLField.setBackground(LONG_POSITION_COLOR);
                            globalPnLField.setForeground(Color.WHITE);
                        } else if (totalPnL < 0) {
                            globalPnLField.setBackground(SHORT_POSITION_COLOR);
                            globalPnLField.setForeground(Color.WHITE);
                        } else {
                            globalPnLField.setBackground(NEUTRAL_POSITION_COLOR);
                            globalPnLField.setForeground(Color.BLACK);
                        }
                    }
                    
                    if (tableModel != null) {
                        refreshPositionsTable(); // Refresh table to update total row
                    }
                }
            });
        }
    }

    @Override
    public void setRealtimeStatus(boolean isRealtime) {
        this.isRealtime = isRealtime;
    }
    
    @Override
    public void updateGlobalPositionLimit(int globalPositionLimit) {
        // Update cached data immediately (thread-safe)
        cachedGlobalPositionLimit = globalPositionLimit;
        needsGlobalPositionLimitUpdate = true;
        
        // Note: Actual UI update will happen in the scheduler
    }

    @Override
    public void updateAvailableLiquidity(double availableLiquidity) {
        // Update cached data immediately (thread-safe)
        cachedAvailableLiquidity = availableLiquidity;
        needsAvailableLiquidityUpdate = true;
        
        // Note: Actual UI update will happen in the scheduler
    }

    // ═══ DEBUG HELPERS ═══
    /**
     * Enable scheduler debugging to help diagnose realtime status issues.
     * Set this to true and check logs if Global P&L field stops updating.
     */
    public static void enableSchedulerDebug() {
        // To enable debugging, temporarily change DEBUG_SCHEDULER to true in the field declaration
        Log.info("OrdersTestUIV3: To enable scheduler debugging, set DEBUG_SCHEDULER = true in OrdersTestUIV3.java");
    }

   static boolean simulationMode = false; 
   static String simulationModeText1="";
   static String simulationModeText2="";
   // Add a private static helper to set simulationMode based on Bookmap run mode
        private static void updateSimulationModeFromRunMode() {
            // Defensive: check for nulls (API returns Boolean, not boolean)
            Boolean isLive = RunModeHelper.isLive();
            Boolean isRealTrading = RunModeHelper.isRealTrading();
            // If either is false or null, treat as simulation
            simulationMode = !(Boolean.TRUE.equals(isLive) && Boolean.TRUE.equals(isRealTrading));
            simulationModeText1 = (isLive?"LiveData":"BackfilledData") ;
            simulationModeText2= (isRealTrading?"RealTrading":"SimTrading");
        }
    
    /**
     * Update the RunMode field with current simulation mode status and appropriate colors.
     * Call this method when simulation mode changes or during UI updates.
     */
    private void updateRunModeField() {
        if (runModeField == null || uiState.get() != UIState.RUNNING) {
            return;
        }
        
        try {
            // Update simulation mode from Bookmap
            updateSimulationModeFromRunMode();
            
            // Combine text and apply formatting
            String displayText = simulationModeText1 + " | " + simulationModeText2;
            
            // Determine colors based on status
            Boolean isLive = RunModeHelper.isLive();
            Boolean isRealTrading = RunModeHelper.isRealTrading();
            
            Color textColor = Color.ORANGE; // Default for simulation
            if (Boolean.TRUE.equals(isLive) && Boolean.TRUE.equals(isRealTrading)) {
                textColor = Color.RED; // Both live and real trading - RED
                displayText = displayText.toUpperCase(); // Make capitals
            } else if (Boolean.TRUE.equals(isLive)) {
                textColor = Color.RED; // Live data but sim trading - RED
                displayText = simulationModeText1.toUpperCase() + " | " + simulationModeText2;
            } else if (Boolean.TRUE.equals(isRealTrading)) {
                textColor = Color.RED; // Real trading but backfilled data - RED  
                displayText = simulationModeText1 + " | " + simulationModeText2.toUpperCase();
            }
            
            runModeField.setText(displayText);
            runModeField.setForeground(textColor);
            runModeField.setFont(runModeField.getFont().deriveFont(Font.BOLD));
            
        } catch (Exception e) {
            // Fallback display in case of API errors
            runModeField.setText("RunMode: Unknown");
            runModeField.setForeground(Color.GRAY);
            if (DEBUG_UI) Log.info("OrdersTestUIV3: Error updating run mode field: " + e.getMessage());
        }
    }

    // ═══ NEW: Safe EDT Operation Helper ═══
    private void safeEDTOperation(Runnable operation) {
        UIState currentState = uiState.get();
        if (currentState == UIState.DISPOSED || currentState == UIState.SHUTTING_DOWN) {
            return; // Skip if shutting down or disposed
        }
        
        if (SwingUtilities.isEventDispatchThread()) {
            // Check state again in case it changed
            if (uiState.get() == UIState.RUNNING || uiState.get() == UIState.INITIALIZING) {
                operation.run();
            }
        } else {
            // Always use invokeLater to avoid deadlocks
            SwingUtilities.invokeLater(() -> {
                UIState state = uiState.get();
                if (state == UIState.RUNNING || state == UIState.INITIALIZING) {
                    operation.run();
                }
            });
        }
    }
    
    // ═══ NEW: Coordinated Shutdown Sequence ═══
    private void initiateShutdown() {
        if (shutdownInitiated) {
            return;
        }
        
        synchronized (LOCK) {
            if (shutdownInitiated) {
                return;
            }
            shutdownInitiated = true;
            uiState.set(UIState.SHUTTING_DOWN);
        }
        
        Log.info("OrdersTestUIV3: Starting coordinated shutdown sequence");
        
        // 1. Stop accepting new operations
        clearAllUpdateFlags();
        
        // 2. Stop schedulers and wait for completion
        shutdownSchedulersCoordinated();
        
        // 3. Cleanup UI components on EDT
        cleanupUIComponents();
        
        // 4. Clear static state
        clearStaticState();
        
        // 5. Mark as disposed
        uiState.set(UIState.DISPOSED);
        shutdownLatch.countDown();
        
        Log.info("OrdersTestUIV3: Coordinated shutdown sequence complete");
    }
    
    private void clearAllUpdateFlags() {
        needsPriceUpdate = false;
        needsPositionUpdate = false;
        needsAccountUpdate = false;
        needsAliasUpdate = false;
        needsLiveTradingUpdate = false;
        needsTimeGateUpdate = false;
        needsGlobalPositionLimitUpdate = false;
        needsAvailableLiquidityUpdate = false;
        needsRunModeUpdate = false;
    }
    
    public static boolean awaitShutdown(long timeout, TimeUnit unit) {
        try {
            return shutdownLatch.await(timeout, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }
    
    // ═══ NEW: Improved Scheduler Shutdown ═══
    private void shutdownSchedulersCoordinated() {
        ScheduledExecutorService currentScheduler = null;
        ScheduledExecutorService currentFastScheduler = null;
        
        synchronized(LOCK) {
            currentScheduler = scheduler;
            currentFastScheduler = fastScheduler;
            scheduler = null;
            fastScheduler = null;
        }
        
        List<ScheduledExecutorService> schedulers = Arrays.asList(currentScheduler, currentFastScheduler);
        
        for (ScheduledExecutorService sched : schedulers) {
            if (sched != null && !sched.isShutdown()) {
                sched.shutdown(); // Stop accepting new tasks
                
                try {
                    if (!sched.awaitTermination(2, TimeUnit.SECONDS)) {
                        sched.shutdownNow(); // Cancel running tasks
                        if (!sched.awaitTermination(1, TimeUnit.SECONDS)) {
                            Log.warn("OrdersTestUIV3: Scheduler did not terminate gracefully");
                        }
                    }
                } catch (InterruptedException e) {
                    sched.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }
    }


// Properly reset the UI state to allow fresh UI creation

public static void prepareForNewInstance() {
    synchronized (instanceLock) {
        if (uiState.get() == UIState.DISPOSED) {
            uiState.set(UIState.INITIALIZING);
            Log.info("OrdersTestUIV3: State reset from DISPOSED to INITIALIZING for new instance");
        }
    }
}
    
}