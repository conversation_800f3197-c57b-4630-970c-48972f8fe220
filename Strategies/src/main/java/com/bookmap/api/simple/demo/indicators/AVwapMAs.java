package com.bookmap.api.simple.demo.indicators;

import velox.api.layer1.simplified.Indicator;
import com.bookmap.api.simple.demo.indicators.InputDataHealthTrackerV2;

/*
====================================================================================
CRITICAL MAINTENANCE NOTE:
------------------------------------------------------------------------------------
Whenever making ANY changes to this file (adding, removing, or modifying variables or state),
you MUST reevaluate what needs to be added to the reset() method.

This method is responsible for robustly resetting all state and ensuring no carry-over
between runs. Failing to update it when adding new state can cause subtle, hard-to-debug bugs.

ALWAYS review and update reset() when making changes to this file!
====================================================================================
*/

public class AVwapMAs {
    public final int level;
    public double topHighValue = Double.NaN;
    public double botLowValue = Double.NaN;
    public double prevTopHighValue = Double.NaN;
    public double prevBotLowValue = Double.NaN;
    public double prevHeight = Double.NaN;
    public double topSlope = 0.0;
    public double botSlope = 0.0;
    public boolean available = false;
    public int trend = 0;
    public int trendSmoothingCounter = 0;
    public int lastTrend = 0;
    
    // Visibility tracking
    private boolean topVisible = false;
    private boolean botVisible = false;
    
    // Enhanced visibility history tracking
    private boolean topVisibleFullLength = false;
    private boolean botVisibleFullLength = false;
    private int topVisibleBars = 0;
    private int botVisibleBars = 0;
    private int topInvisibleBars = 0;
    private int botInvisibleBars = 0;
    private boolean[] topVisibilityHistory;
    private boolean[] botVisibilityHistory;
    private int visibilityHead = 0;
    private int visibilitySize = 0;
    private int visibilityCapacity;

    // Indicators
    public Indicator topHighIndicator;
    public Indicator topHighDimmedIndicator;
    public Indicator botLowIndicator;
    public Indicator botLowDimmedIndicator;
    public Indicator heightIndicator;
    public Indicator trendIndicator;

    // Slope metrics
    public MASlopeMetrics slopeMetrics;

    // Distance tracking to price
    private double[] topDistances;
    private double[] botDistances;
    private int distanceCapacity;
    private int distanceHead = 0;
    private int distanceSize = 0;
    private double topSum = 0.0;  // Running sum for top distances
    private double botSum = 0.0;  // Running sum for bot distances
    private double topSumOfSquares = 0.0;  // Running sum of squares for top distances
    private double botSumOfSquares = 0.0;  // Running sum of squares for bot distances
    private double topDistanceMax = Double.MIN_VALUE;
    private double botDistanceMax = Double.MIN_VALUE;
    private double topDistanceAvg = 0.0;
    private double botDistanceAvg = 0.0;
    private double topDistanceStd = 0.0;
    private double botDistanceStd = 0.0;

    // Indicators for MA + std*multiplier
    public Indicator topHighStdIndicator;
    public Indicator botLowStdIndicator;

    // Configurable stddev multiplier for this MA
    private double stdMultiplier = 2.0;

    // --- Store last value added to std indicators ---
    private double lastTopHighStdValue = Double.NaN;
    private double lastBotLowStdValue = Double.NaN;

    // --- Data health tracker for price data ---
    InputDataHealthTrackerV2 dataHealthTracker;
    private boolean ready = false;

    // --- Health monitoring enable flag ---
    private boolean healthMonitoringEnabled = true;

    // --- Require sufficient data for visibility ---
    private boolean requireSufficientForVisibility = false;

    // --- New: Store current width (distance) between top and bot ---
    private double currentTopToBotDistance = Double.NaN;

    // --- Timestamp of the last update ---
    private long lastUpdateTimeNanos = 0L;

    // Updated constructor to accept stdMultiplier and requireSufficientForVisibility
    public AVwapMAs(int level, MASlopeMetrics slopeMetrics, int maLength, double stdMultiplier, boolean requireSufficientForVisibility) {
        this.level = level;
        this.slopeMetrics = slopeMetrics;
        this.distanceCapacity = maLength;
        this.topDistances = new double[maLength];
        this.botDistances = new double[maLength];
        this.stdMultiplier = stdMultiplier;
        this.visibilityCapacity = maLength;
        this.topVisibilityHistory = new boolean[maLength];
        this.botVisibilityHistory = new boolean[maLength];
        this.topSum = 0.0;
        this.botSum = 0.0;
        this.topSumOfSquares = 0.0;
        this.botSumOfSquares = 0.0;
        this.topDistanceMax = Double.MIN_VALUE;
        this.botDistanceMax = Double.MIN_VALUE;
        // Use same health check config as VWAPCalculatorOptimized
        this.dataHealthTracker = new InputDataHealthTrackerV2("MA" + level + "-Tracker");
        this.ready = false;
        this.requireSufficientForVisibility = requireSufficientForVisibility;
    }

    // Backward-compatible constructor (default multiplier 2.0, requireSufficientForVisibility false)
    public AVwapMAs(int level, MASlopeMetrics slopeMetrics, int maLength) {
        this(level, slopeMetrics, maLength, 2.0, false);
    }

    // Backward-compatible constructor (default requireSufficientForVisibility false)
    public AVwapMAs(int level, MASlopeMetrics slopeMetrics, int maLength, double stdMultiplier) {
        this(level, slopeMetrics, maLength, stdMultiplier, false);
    }

    public void reset() {
        topHighValue = Double.NaN;
        botLowValue = Double.NaN;
        prevTopHighValue = Double.NaN;
        prevBotLowValue = Double.NaN;
        prevHeight = Double.NaN;
        topSlope = 0.0;
        botSlope = 0.0;
        available = false;
        trend = 0;
        trendSmoothingCounter = 0;
        lastTrend = 0;
        topVisible = false;
        botVisible = false;
        if (slopeMetrics != null) slopeMetrics.reset();
        distanceHead = 0;
        distanceSize = 0;
        topSum = 0.0;
        botSum = 0.0;
        topSumOfSquares = 0.0;
        botSumOfSquares = 0.0;
        topDistanceMax = Double.MIN_VALUE;
        botDistanceMax = Double.MIN_VALUE;
        topDistanceAvg = 0.0;
        botDistanceAvg = 0.0;
        topDistanceStd = 0.0;
        botDistanceStd = 0.0;
        topDistances = new double[distanceCapacity];
        botDistances = new double[distanceCapacity];
        // Reset visibility history
        topVisibleFullLength = false;
        botVisibleFullLength = false;
        topVisibleBars = 0;
        botVisibleBars = 0;
        topInvisibleBars = 0;
        botInvisibleBars = 0;
        visibilityHead = 0;
        visibilitySize = 0;
        topVisibilityHistory = new boolean[visibilityCapacity];
        botVisibilityHistory = new boolean[visibilityCapacity];
        // Reset indicators to NaN if present
        if (topHighIndicator != null) topHighIndicator.addPoint(Double.NaN);
        if (topHighDimmedIndicator != null) topHighDimmedIndicator.addPoint(Double.NaN);
        if (botLowIndicator != null) botLowIndicator.addPoint(Double.NaN);
        if (botLowDimmedIndicator != null) botLowDimmedIndicator.addPoint(Double.NaN);
        if (heightIndicator != null) heightIndicator.addPoint(Double.NaN);
        if (trendIndicator != null) trendIndicator.addPoint(Double.NaN);
        if (topHighStdIndicator != null) topHighStdIndicator.addPoint(Double.NaN);
        if (botLowStdIndicator != null) botLowStdIndicator.addPoint(Double.NaN);
        lastTopHighStdValue = Double.NaN;
        lastBotLowStdValue = Double.NaN;
        if (dataHealthTracker != null) dataHealthTracker.reset();
        ready = false;
        healthMonitoringEnabled = true;
        currentTopToBotDistance = Double.NaN;
        lastUpdateTimeNanos = 0L; // Reset the timestamp
    }
    
    /**
     * Check if the top indicator is currently visible (not dimmed)
     * @return true if top indicator is visible
     */
    public boolean isTopVisible() {
        return topVisible;
    }
    
    /**
     * Check if the bottom indicator is currently visible (not dimmed)
     * @return true if bottom indicator is visible
     */
    public boolean isBotVisible() {
        return botVisible;
    }

    public void update(double topHigh, double botLow, double currentPrice, long nanoTime) {
        // Optimize NaN check - combine all checks into one condition
        if (Double.isNaN(topHigh) || Double.isNaN(botLow) || Double.isNaN(currentPrice)) {
            currentTopToBotDistance = Double.NaN;
            return; // Skip slope/distance updates etc.
        }
        this.lastUpdateTimeNanos = nanoTime; // Store the timestamp of this update
     // Cache distanceCapacityQuarter to avoid repeated division
        final int distanceCapacityQuarter = distanceCapacity >> 2; // Bit shift is faster than division by 4
        
        boolean valid = !Double.isNaN(currentPrice) && !Double.isInfinite(currentPrice);
            dataHealthTracker.update(currentPrice, nanoTime, valid);
        // Update data health tracker with price data only if health monitoring is enabled
        if (healthMonitoringEnabled && dataHealthTracker != null) {
            dataHealthTracker.update(currentPrice, nanoTime, valid);
        }
        ready = valid && (distanceSize >= distanceCapacityQuarter);

        prevTopHighValue = topHighValue;
        prevBotLowValue = botLowValue;
        topHighValue = topHigh;
        botLowValue = botLow;
        // --- New: Update currentTopToBotDistance ---
        if (!Double.isNaN(topHighValue) && !Double.isNaN(botLowValue)) {
            currentTopToBotDistance = topHighValue - botLowValue;
        } else {
            currentTopToBotDistance = Double.NaN;
        }
        topSlope = MASlopeMetrics.calculateSlope(topHighValue, prevTopHighValue);
        botSlope = MASlopeMetrics.calculateSlope(botLowValue, prevBotLowValue);
        available = !Double.isNaN(topHighValue) && !Double.isNaN(botLowValue);
                //&& !Double.isNaN(prevTopHighValue) && !Double.isNaN(prevBotLowValue);
        if (slopeMetrics != null && available) {
            slopeMetrics.addSlopes(topSlope, botSlope);
        }

        // Calculate and store signed distances
        if (available) {
            final double topDistance = currentPrice - topHighValue;
            final double botDistance = currentPrice - botLowValue;
            
            // Add to circular buffer and update metrics incrementally
            final int prevIndex = distanceHead;  // Index of the value being overwritten (if full)
            if (distanceSize > 0) {
                // Remove the effect of the old value if buffer is full
                if (distanceSize == distanceCapacity) {
                    final double oldTopValue = topDistances[prevIndex];
                    final double oldBotValue = botDistances[prevIndex];
                    topSum -= oldTopValue;
                    botSum -= oldBotValue;
                    topSumOfSquares -= (oldTopValue * oldTopValue);
                    botSumOfSquares -= (oldBotValue * oldBotValue);
                    
                    // Update max: only if the old value was the max
                    final double absOldTop = Math.abs(oldTopValue);
                    final double absOldBot = Math.abs(oldBotValue);
                    if (absOldTop == topDistanceMax) {
                        topDistanceMax = Double.MIN_VALUE;  // Recompute max if necessary
                        for (int i = 0; i < distanceSize; i++) {
                            final int idx = (prevIndex + i) % distanceCapacity;
                            topDistanceMax = Math.max(topDistanceMax, Math.abs(topDistances[idx]));
                        }
                    }
                    if (absOldBot == botDistanceMax) {
                        botDistanceMax = Double.MIN_VALUE;
                        for (int i = 0; i < distanceSize; i++) {
                            final int idx = (prevIndex + i) % distanceCapacity;
                            botDistanceMax = Math.max(botDistanceMax, Math.abs(botDistances[idx]));
                        }
                    }
                }
                // Add the new value
                topSum += topDistance;
                botSum += botDistance;
                topSumOfSquares += (topDistance * topDistance);
                botSumOfSquares += (botDistance * botDistance);
                
                final double absTopDistance = Math.abs(topDistance);
                final double absBotDistance = Math.abs(botDistance);
                topDistanceMax = Math.max(topDistanceMax, absTopDistance);
                botDistanceMax = Math.max(botDistanceMax, absBotDistance);
            }
            
            topDistances[distanceHead] = topDistance;
            botDistances[distanceHead] = botDistance;
            distanceHead = (distanceHead + 1) % distanceCapacity;
            if (distanceSize < distanceCapacity) {
                distanceSize++;
            }
            
            // Now update averages and std dev incrementally
            if (distanceSize > 0) {
                topDistanceAvg = topSum / distanceSize;
                botDistanceAvg = botSum / distanceSize;
                if (distanceSize > 1) {
                    // Use optimized standard deviation calculation to avoid division twice
                    final double topVariance = (topSumOfSquares - (topSum * topSum / distanceSize)) / (distanceSize - 1);
                    final double botVariance = (botSumOfSquares - (botSum * botSum / distanceSize)) / (distanceSize - 1);
                    topDistanceStd = Math.sqrt(topVariance);
                    botDistanceStd = Math.sqrt(botVariance);
                } else {
                    topDistanceStd = 0.0;
                    botDistanceStd = 0.0;
                }
            }
        }
    }

    public int smoothTrend(int newTrend) {
        if (newTrend == lastTrend) {
            trendSmoothingCounter++;
        } else {
            trendSmoothingCounter = 0;
        }
        if (trendSmoothingCounter >= 2) {
            lastTrend = newTrend;
        } else if (trendSmoothingCounter == 0) {
            lastTrend = newTrend;
        }
        trend = lastTrend;
        return lastTrend;
    }

    public void updateIndicators(boolean alwaysShow, double currentHigh, double currentLow, double prevTopHigh, double prevBotLow, int length, int ma1Length, int ma2Length, int ma3Length, int ma4Length, int ma5Length) {
        final double oldPrevTopHighValue;
        final double oldPrevBotLowValue;
        if (length == ma1Length) {
            oldPrevTopHighValue = Double.NaN;
            oldPrevBotLowValue = Double.NaN;
        } else if (length == ma2Length) {
            oldPrevTopHighValue = prevTopHigh;
            oldPrevBotLowValue = prevBotLow;
        } else if (length == ma3Length) {
            oldPrevTopHighValue = prevTopHigh;
            oldPrevBotLowValue = prevBotLow;
        } else if (length == ma4Length) {
            oldPrevTopHighValue = prevTopHigh;
            oldPrevBotLowValue = prevBotLow;
        } else if (length == ma5Length) {
            oldPrevTopHighValue = prevTopHigh;
            oldPrevBotLowValue = prevBotLow;
        } else {
            oldPrevTopHighValue = prevTopHigh;
            oldPrevBotLowValue = prevBotLow;
        }

        // Store visibility state in member variables - now with truly symmetrical conditions
        if (!isReady()) {
            // Currently do nothing
        } else {
            final boolean sufficient = !requireSufficientForVisibility || isSufficient();
            this.topVisible = sufficient && (length == ma1Length || Double.isNaN(oldPrevTopHighValue) || topHighValue > oldPrevTopHighValue) && topHighValue > currentLow;
            this.botVisible = sufficient && (length == ma1Length || Double.isNaN(oldPrevBotLowValue) || botLowValue < oldPrevBotLowValue) && botLowValue < currentHigh;
        }
        
        // Update visibility history
        updateVisibilityHistory();

        if (topVisible) {
            if (topHighIndicator != null) topHighIndicator.addPoint(topHighValue);
            if (topHighDimmedIndicator != null) topHighDimmedIndicator.addPoint(Double.NaN);
        } else if (alwaysShow) {
            if (topHighDimmedIndicator != null) topHighDimmedIndicator.addPoint(topHighValue);
            if (topHighIndicator != null) topHighIndicator.addPoint(Double.NaN);
        } else {
            // Not visible and not set to always show - hide both indicators
            if (topHighIndicator != null) topHighIndicator.addPoint(Double.NaN);
            if (topHighDimmedIndicator != null) topHighDimmedIndicator.addPoint(Double.NaN);
        }

        if (botVisible) {
            if (botLowIndicator != null) botLowIndicator.addPoint(botLowValue);
            if (botLowDimmedIndicator != null) botLowDimmedIndicator.addPoint(Double.NaN);
        } else if (alwaysShow) {
            if (botLowDimmedIndicator != null) botLowDimmedIndicator.addPoint(botLowValue);
            if (botLowIndicator != null) botLowIndicator.addPoint(Double.NaN);
        } else {
            // Not visible and not set to always show - hide both indicators
            if (botLowIndicator != null) botLowIndicator.addPoint(Double.NaN);
            if (botLowDimmedIndicator != null) botLowDimmedIndicator.addPoint(Double.NaN);
        }

        if (heightIndicator != null && !Double.isNaN(topHighValue) && !Double.isNaN(botLowValue)) {
            heightIndicator.addPoint(topHighValue - botLowValue);
        } else if (heightIndicator != null) {
            heightIndicator.addPoint(Double.NaN);
        }
        if (trendIndicator != null) {
            trendIndicator.addPoint(trend);
        }

        // Compute std values before adding to indicators
        final double topHighStdValue = topHighValue - stdMultiplier * topDistanceStd;
        final double botLowStdValue = botLowValue + stdMultiplier * botDistanceStd;

        // --- Store the value that will be added to the indicators (even if NaN) ---
        final boolean sufficient = !requireSufficientForVisibility || isSufficient();
        if (topHighStdIndicator != null) {
            if ((topVisible || alwaysShow) && sufficient) {
                lastTopHighStdValue = topHighStdValue;
                topHighStdIndicator.addPoint(topHighStdValue);
            } else {
                lastTopHighStdValue = Double.NaN;
                topHighStdIndicator.addPoint(Double.NaN);
            }
        }

        if (botLowStdIndicator != null) {
            if ((botVisible || alwaysShow) && sufficient) {
                lastBotLowStdValue = botLowStdValue;
                botLowStdIndicator.addPoint(botLowStdValue);
            } else {
                lastBotLowStdValue = Double.NaN;
                botLowStdIndicator.addPoint(Double.NaN);
            }
        }
    }

    private void updateVisibilityHistory() {
        // Remove old visibility state from full-length flags if we're at capacity
        if (visibilitySize == visibilityCapacity) {
            final int oldIndex = visibilityHead;
            if (topVisibilityHistory[oldIndex]) {
                // Check if there are any false values in the current history
                // If there are, we might still be full length visible after removing this true
                topVisibleFullLength = false;
                for (int i = 0; i < visibilityCapacity; i++) {
                    if (i != oldIndex && !topVisibilityHistory[i]) {
                        topVisibleFullLength = false;
                        break;
                    }
                    topVisibleFullLength = true;
                }
            }
            if (botVisibilityHistory[oldIndex]) {
                botVisibleFullLength = false;
                for (int i = 0; i < visibilityCapacity; i++) {
                    if (i != oldIndex && !botVisibilityHistory[i]) {
                        botVisibleFullLength = false;
                        break;
                    }
                    botVisibleFullLength = true;
                }
            }
        }
        
        // Add current visibility state to history, but only if allowed by requireSufficientForVisibility
        boolean sufficient = !requireSufficientForVisibility || isSufficient();
        topVisibilityHistory[visibilityHead] = topVisible && sufficient;
        botVisibilityHistory[visibilityHead] = botVisible && sufficient;
        visibilityHead = (visibilityHead + 1) % visibilityCapacity;
        if (visibilitySize < visibilityCapacity) {
            visibilitySize++;
        } else {
            // If we're at capacity, update full-length flags based on new value
            if (topVisible && sufficient) {
                topVisibleFullLength = true;
            } else {
                topVisibleFullLength = false;
            }
            if (botVisible && sufficient) {
                botVisibleFullLength = true;
            } else {
                botVisibleFullLength = false;
            }
        }
        
        // Update consecutive visibility counters
        if (topVisible && sufficient) {
            topVisibleBars++;
            topInvisibleBars = 0;
        } else {
            topVisibleBars = 0;
            topInvisibleBars++;
        }
        
        if (botVisible && sufficient) {
            botVisibleBars++;
            botInvisibleBars = 0;
        } else {
            botVisibleBars = 0;
            botInvisibleBars++;
        }
    }
    
    // Getters for visibility metrics
    public boolean isTopVisibleFullLength() {
        return topVisibleFullLength;
    }
    
    public boolean isBotVisibleFullLength() {
        return botVisibleFullLength;
    }
    
    public int getTopVisibleBars() {
        return topVisibleBars;
    }
    
    public int getBotVisibleBars() {
        return botVisibleBars;
    }
    
    public int getTopInvisibleBars() {
        return topInvisibleBars;
    }
    
    public int getBotInvisibleBars() {
        return botInvisibleBars;
    }

    // --- Getters for last value added to std indicators ---
    public double getLastTopHighStdValue() {
        return lastTopHighStdValue;
    }
    public double getLastBotLowStdValue() {
        return lastBotLowStdValue;
    }

    // --- Public Getters for Distance Metrics ---
    public double getTopDistanceAvg() {
        return topDistanceAvg;
    }

    public double getBotDistanceAvg() {
        return botDistanceAvg;
    }

    public double getTopDistanceStd() {
        return topDistanceStd;
    }

    public double getBotDistanceStd() {
        return botDistanceStd;
    }

    // --- New: Getter for current width (distance) between top and bot ---
    public double getTopToBotDistance() {
        return currentTopToBotDistance;
    }
    // --- End of Public Getters ---

    public int detectTrend(double trendThreshold) {
        // Check internal state first
        if (Double.isNaN(topHighValue) || Double.isNaN(botLowValue)) {
            prevHeight = Double.NaN; // Ensure prevHeight is also NaN if current values are
            return 0; // Cannot determine trend if current values are NaN
        }
        // Use the prev values set by the update() method from the previous bar
        final double previousTopHighValue = prevTopHighValue;
        final double previousBotLowValue = prevBotLowValue;
        double previousHeight = prevHeight; // Use the height from the previous bar

        // Handle initial NaN state for previousHeight
        if (Double.isNaN(previousHeight) && !Double.isNaN(previousTopHighValue) && !Double.isNaN(previousBotLowValue)) {
            previousHeight = previousTopHighValue - previousBotLowValue;
        }

        int trendVal = 0;
        // Ensure previous values are valid before calculating changes
        if (!Double.isNaN(previousTopHighValue) && !Double.isNaN(previousBotLowValue) && !Double.isNaN(previousHeight)) {
            final double botChange = botLowValue - previousBotLowValue;
            final double topChange = topHighValue - previousTopHighValue;
            final double currentHeight = topHighValue - botLowValue;
            final double heightChange = currentHeight - previousHeight;

            // Check for invalid height calculation
            if (Double.isNaN(heightChange)) {
                 // If heightChange is NaN, default to neutral trend
                 trendVal = 0;
            } else {
                final double minChangeThreshold = trendThreshold;
                final double heightChangeThreshold = trendThreshold;

                final boolean botLowRising = botChange > minChangeThreshold;
                final boolean topHighRising = topChange > minChangeThreshold;
                final boolean botLowFalling = botChange < -minChangeThreshold;
                final boolean topHighFalling = topChange < -minChangeThreshold;
                final boolean heightExpanding = heightChange > heightChangeThreshold;
                final boolean heightContracting = heightChange < -heightChangeThreshold;

                if (botLowRising && topHighRising && heightExpanding) {
                    trendVal = 2;
                } else if (botLowFalling && topHighFalling && heightExpanding) {
                    trendVal = -2;
                } else if (botLowRising || (topHighRising && !heightContracting)) {
                    trendVal = 1;
                } else if (topHighFalling || (botLowFalling && !heightContracting)) {
                    trendVal = -1;
                } else {
                    trendVal = 0;
                }
            }
        }
        // Update prevHeight for the next bar's calculation
        // Do NOT update prevTopHighValue or prevBotLowValue here, update() handles that.
        prevHeight = topHighValue - botLowValue;
        return trendVal;
    }

    /**
     * Returns the clipped and amplified botSlope for indicator plotting.
     */
    public double getClippedBotSlope(double amplification, double lower, double upper, double pips) {
        if (!available) return 0.0;
        double val = (botSlope * amplification) / pips;
        return Math.max(lower, Math.min(upper, val));
    }

    /**
     * Returns the clipped and amplified topSlope for indicator plotting.
     */
    public double getClippedTopSlope(double amplification, double lower, double upper, double pips) {
        if (!available) return 0.0;
        double val = (topSlope * amplification) / pips;
        return Math.max(lower, Math.min(upper, val));
    }

    /**
     * Returns true if the MA is available and has enough data for a full-length calculation.
     */
    public boolean isSufficient() {
        return available && (distanceSize >= distanceCapacity);
    }

    /**
     * Returns true if the recent price data is healthy (not too many NaNs or outliers)
     */
    public boolean isDataHealthy() {
        return dataHealthTracker == null || dataHealthTracker.isHealthy();
    }

    /**
     * Returns true if the MA is ready and the data is healthy (same logic as VWAPCalculatorOptimized)
     */
    public boolean isReady() {
        return ready && isDataHealthy();
    }

    /**
     * Returns the health status of the input data for this MA.
     */
    public InputDataHealthTrackerV2.Status getDataHealthStatus() {
        return dataHealthTracker != null ? dataHealthTracker.getStatus() : InputDataHealthTrackerV2.Status.UNHEALTHY;
    }

    public void setHealthMonitoringEnabled(boolean enabled) {
        this.healthMonitoringEnabled = enabled;
    }

    // Setter for requireSufficientForVisibility
    public void setRequireSufficientForVisibility(boolean require) {
        this.requireSufficientForVisibility = require;
    }

    // Add cleanup method to match AVwapMAsV2
    public void cleanup() {
        if (dataHealthTracker != null) {
            dataHealthTracker.reset();
            dataHealthTracker.dispose(); // Ensure tracker is disposed
            dataHealthTracker = null;
        }
    }

    // Snapshot class definition
    public static class AVwapMASnapshot {
        public final int level;
        public final double topHighValue;
        public final double botLowValue;
        public final double prevTopHighValue;
        public final double prevBotLowValue;
        public final double topSlope;
        public final double botSlope;
        public final boolean available;
        public final int trend;
        public final boolean topVisible;
        public final boolean botVisible;
        public final boolean topVisibleFullLength;
        public final boolean botVisibleFullLength;
        public final int topVisibleBars;
        public final int botVisibleBars;
        public final int topInvisibleBars;
        public final int botInvisibleBars;
        public final double topDistanceAvg;
        public final double botDistanceAvg;
        public final double topDistanceStd;
        public final double botDistanceStd;
        public final double lastTopHighStdValue;
        public final double lastBotLowStdValue;
        public final boolean isReady;
        public final InputDataHealthTrackerV2.Status dataHealthStatus;
        public final double currentTopToBotDistance;
        public final double stdMultiplier;
        public final long timestampNanos;

        public AVwapMASnapshot(
                int level, double topHighValue, double botLowValue,
                double prevTopHighValue, double prevBotLowValue,
                double topSlope, double botSlope, boolean available, int trend,
                boolean topVisible, boolean botVisible,
                boolean topVisibleFullLength, boolean botVisibleFullLength,
                int topVisibleBars, int botVisibleBars,
                int topInvisibleBars, int botInvisibleBars,
                double topDistanceAvg, double botDistanceAvg,
                double topDistanceStd, double botDistanceStd,
                double lastTopHighStdValue, double lastBotLowStdValue,
                boolean isReady, InputDataHealthTrackerV2.Status dataHealthStatus,
                double currentTopToBotDistance, double stdMultiplier, long timestampNanos) {
            this.level = level;
            this.topHighValue = topHighValue;
            this.botLowValue = botLowValue;
            this.prevTopHighValue = prevTopHighValue;
            this.prevBotLowValue = prevBotLowValue;
            this.topSlope = topSlope;
            this.botSlope = botSlope;
            this.available = available;
            this.trend = trend;
            this.topVisible = topVisible;
            this.botVisible = botVisible;
            this.topVisibleFullLength = topVisibleFullLength;
            this.botVisibleFullLength = botVisibleFullLength;
            this.topVisibleBars = topVisibleBars;
            this.botVisibleBars = botVisibleBars;
            this.topInvisibleBars = topInvisibleBars;
            this.botInvisibleBars = botInvisibleBars;
            this.topDistanceAvg = topDistanceAvg;
            this.botDistanceAvg = botDistanceAvg;
            this.topDistanceStd = topDistanceStd;
            this.botDistanceStd = botDistanceStd;
            this.lastTopHighStdValue = lastTopHighStdValue;
            this.lastBotLowStdValue = lastBotLowStdValue;
            this.isReady = isReady;
            this.dataHealthStatus = dataHealthStatus;
            this.currentTopToBotDistance = currentTopToBotDistance;
            this.stdMultiplier = stdMultiplier;
            this.timestampNanos = timestampNanos;
        }

        // Add getters for all fields if needed for external use
        public int getLevel() { return level; }
        public double getTopHighValue() { return topHighValue; }
        public double getBotLowValue() { return botLowValue; }
        public double getPrevTopHighValue() { return prevTopHighValue; }
        public double getPrevBotLowValue() { return prevBotLowValue; }
        public double getTopSlope() { return topSlope; }
        public double getBotSlope() { return botSlope; }
        public boolean isAvailable() { return available; }
        public int getTrend() { return trend; }
        public boolean isTopVisible() { return topVisible; }
        public boolean isBotVisible() { return botVisible; }
        public boolean isTopVisibleFullLength() { return topVisibleFullLength; }
        public boolean isBotVisibleFullLength() { return botVisibleFullLength; }
        public int getTopVisibleBars() { return topVisibleBars; }
        public int getBotVisibleBars() { return botVisibleBars; }
        public int getTopInvisibleBars() { return topInvisibleBars; }
        public int getBotInvisibleBars() { return botInvisibleBars; }
        public double getTopDistanceAvg() { return topDistanceAvg; }
        public double getBotDistanceAvg() { return botDistanceAvg; }
        public double getTopDistanceStd() { return topDistanceStd; }
        public double getBotDistanceStd() { return botDistanceStd; }
        public double getLastTopHighStdValue() { return lastTopHighStdValue; }
        public double getLastBotLowStdValue() { return lastBotLowStdValue; }
        public boolean isReady() { return isReady; }
        public InputDataHealthTrackerV2.Status getDataHealthStatus() { return dataHealthStatus; }
        public double getCurrentTopToBotDistance() { return currentTopToBotDistance; }
        public double getStdMultiplier() { return stdMultiplier; }
        public long getTimestampNanos() { return timestampNanos; }
    }

    /**
     * Returns a snapshot of the current state of this AVwapMAs instance.
     * @return An AVwapMASnapshot object.
     */
    public AVwapMASnapshot getSnapshot() {
        return new AVwapMASnapshot(
                this.level,
                this.topHighValue,
                this.botLowValue,
                this.prevTopHighValue,
                this.prevBotLowValue,
                this.topSlope,
                this.botSlope,
                this.available,
                this.trend,
                this.topVisible,
                this.botVisible,
                this.topVisibleFullLength,
                this.botVisibleFullLength,
                this.topVisibleBars,
                this.botVisibleBars,
                this.topInvisibleBars,
                this.botInvisibleBars,
                this.topDistanceAvg,
                this.botDistanceAvg,
                this.topDistanceStd,
                this.botDistanceStd,
                this.lastTopHighStdValue,
                this.lastBotLowStdValue,
                this.isReady(), // Call method to get current readiness
                this.getDataHealthStatus(), // Call method
                this.currentTopToBotDistance,
                this.stdMultiplier,
                this.lastUpdateTimeNanos // Use the stored timestamp
        );
    }
}
