package com.bookmap.api.simple.demo.indicators;

import java.awt.Color;
// Removed: import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.List;
import java.util.LinkedHashMap;
import java.util.Map;
import java.io.IOException;
import java.util.Arrays;

import com.bookmap.api.simple.demo.indicators.utils.MiniAtrBandsCalculator;
import com.bookmap.api.simple.demo.utils.gui.IconFactory;
// Removed: import com.bookmap.api.simple.demo.indicators.VWAPCalculatorOptimized;
// Removed: import com.bookmap.api.simple.demo.indicators.MultipleAccountSupportDemoBase;

import javax.swing.SwingUtilities;

import velox.api.layer1.Layer1ApiProvider;
import velox.api.layer1.annotations.Layer1ApiVersion;
import velox.api.layer1.annotations.Layer1ApiVersionValue;
import velox.api.layer1.annotations.Layer1Attachable;
import velox.api.layer1.annotations.Layer1SimpleAttachable;
import velox.api.layer1.annotations.Layer1StrategyName;
import velox.api.layer1.annotations.Layer1TradingStrategy;
import velox.api.layer1.common.ListenableHelper;
import velox.api.layer1.common.Log;
import velox.api.layer1.data.InstrumentInfo;
import velox.api.layer1.data.OrderDuration;
import velox.api.layer1.layers.utils.OrderBook;
import velox.api.layer1.messages.indicators.Layer1ApiUserMessageModifyIndicator.GraphType;
import velox.api.layer1.simplified.Api;
import velox.api.layer1.simplified.AxisGroup;
import velox.api.layer1.simplified.BackfilledDataListener;
import velox.api.layer1.simplified.Bar;
import velox.api.layer1.simplified.BarDataListener;
import velox.api.layer1.simplified.BalanceListener;
import velox.api.layer1.simplified.CustomModule;
import velox.api.layer1.simplified.Indicator;
import velox.api.layer1.simplified.InitialState;
import velox.api.layer1.simplified.Intervals;
import velox.api.layer1.simplified.OrdersListener;
import velox.api.layer1.simplified.Parameter;
import velox.api.layer1.simplified.PositionListener;
import velox.api.layer1.simplified.TimeListener;
import velox.api.layer1.simplified.TradeDataListener;
import velox.api.layer1.simplified.BboListener;
import velox.api.layer1.data.BalanceInfo;
// Removed: import velox.api.layer1.data.ExecutionInfo;
// Removed: import velox.api.layer1.data.OrderInfoUpdate;
import velox.api.layer1.data.StatusInfo;
import velox.api.layer1.simplified.HistoricalModeListener;
import com.bookmap.api.simple.demo.indicators.InputDataHealthTracker;
import com.bookmap.api.simple.demo.indicators.AVwapMAs;
import com.bookmap.api.simple.demo.indicators.TrailingStopControllerV3;

// --- VPA/Session fields ---
import com.bookmap.api.simple.demo.indicators.VolumeProfileAnalyzer;
import com.bookmap.api.simple.demo.indicators.SessionSchedule;
import com.bookmap.api.simple.demo.indicators.HistoryStore;
import com.bookmap.api.simple.demo.indicators.SessionManager;
import com.bookmap.api.simple.demo.indicators.CloudNotesBroadcaster;
import com.bookmap.api.simple.demo.indicators.VolumeProfileAnalyzer.VolumeNode;
import velox.api.layer1.data.TradeInfo; // Correct import based on search results

// Import for StopSpec
import com.bookmap.api.simple.demo.indicators.TrailingStopControllerV3.StopSpec;
import com.bookmap.api.simple.demo.indicators.TrailingStopControllerV3.Position;
import com.bookmap.api.simple.demo.indicators.TrailingStopControllerV3.Type;
import com.bookmap.api.simple.demo.indicators.TrailingStopControllerV3.CompositeMode;
import com.bookmap.api.simple.demo.indicators.TrailingStopControllerV3.CandleBar;
import com.bookmap.api.simple.demo.indicators.utils.DynamicProfitTargetGenerator;
import com.bookmap.api.simple.demo.indicators.utils.PotentialTarget;
import com.bookmap.api.simple.demo.utils.ProximityConditionChecker;

import velox.api.layer1.annotations.Layer1MultiAccountTradingSupported;

// Import OrdersTestUIV3 to update gauges
import com.bookmap.api.simple.demo.indicators.OrdersTestUIV3;
/*
====================================================================================
CRITICAL MAINTENANCE NOTE:
------------------------------------------------------------------------------------
Whenever making ANY changes to this file (adding, removing, or modifying variables or state),
you MUST reevaluate what needs to be added to the stop() and resetTradeState() methods.

These methods are responsible for robustly resetting all state and ensuring no carry-over
between runs. Failing to update them when adding new state can cause subtle, hard-to-debug bugs.

ALWAYS review and update stop() and resetTradeState() when making changes to this file!
====================================================================================
*/

@Layer1Attachable
@Layer1TradingStrategy
@Layer1MultiAccountTradingSupported
@Layer1SimpleAttachable
@Layer1StrategyName("Nubia V5_8_0_1 - Auto Midas Anchored VWAP with Midas Adaptive Zone Trader and StdDev Bands")
@Layer1ApiVersion(Layer1ApiVersionValue.VERSION2)
public class NubiaAutoMidasAnchoredVWAPV5_8_0 extends MultipleAccountSupportDemoBase
        implements CustomModule, BboListener, BarDataListener, TradeDataListener, TimeListener,
        BackfilledDataListener, HistoricalModeListener {

    // Add a field for the BBOAnalysedDataInterface listener
    private BBOAnalysedDataInterface bboAnalysedDataListener;

    // Method to register the listener (OrdersTestUIV3 instance)
    public void setBboAnalysedDataListener(BBOAnalysedDataInterface listener) {
        this.bboAnalysedDataListener = listener;
    }

    public NubiaAutoMidasAnchoredVWAPV5_8_0() {
        super(null);
    }

    private Indicator accountPositionIndicator;
    private Indicator accountUnrealizedPnlIndicator;
    private Indicator accountRealizedPnlIndicator;
    private Indicator accountVolumeIndicator;
    private Indicator accountWorkingBuysIndicator;
    private Indicator accountWorkingSellsIndicator;
    private Indicator accountBalanceIndicator;

    private Api api;
    private InstrumentInfo instrumentInfo;
    private double pips;
    private final Object lock = new Object(); // For thread safety
    private static final double EPSILON = 1e-10; // For numerical stability
    private static final double MAX_SIZE_MULTIPLIER = 2.0; // Maximum position size limit
    private boolean NowRealtime = false;
    // Version identifier
    private final String VERSION = "******* - First attemp add Balance and Position Listeners";

    private SessionSchedule vpaSessionSchedule;
    private HistoryStore vpaHistoryStore;
    private VolumeProfileAnalyzer vpa;
    private SessionManager vpaSessionManager;
    private CloudNotesBroadcaster vpaNotesBroadcaster;

    // Configurable Parameters - General Settings
    @Parameter(name = "<html><b><font color='#3366CC'>===== General Settings ******* =====</font></b><br>Always show<html>", reloadOnChange = true)
    private Boolean alwaysShow = false;

    @Parameter(name = "<html>&nbsp;&nbsp;MA1 Length<html>", minimum = 2, maximum = 10000)
    private Integer ma1Length = 17;

    @Parameter(name = "<html>&nbsp;&nbsp;MA2 Length <i>(medium term)</i><html>", minimum = 2, maximum = 10000)
    private Integer ma2Length = 113; // 72

    @Parameter(name = "<html>MA3 Length<html>", minimum = 2, maximum = 10000)
    private Integer ma3Length = 759; // 305

    @Parameter(name = "<html>MA4 Length<html>", minimum = 2, maximum = 10000)
    private Integer ma4Length = 5066; // 1292

    @Parameter(name = "<html>MA5 Length<html>", minimum = 2, maximum = 99999)
    private Integer ma5Length = 33839; // 99999

    @Parameter(name = "Min Volume Filter", minimum = 0, maximum = 99999)
    public Integer minVolumeFilter = 1;

    @Parameter(name = "<html>Use 500ms interval <font color='#996633'><i>(requires restart)</i></font><html>", reloadOnChange = true)
    private Boolean use500ms = false;

    @Parameter(name = "<html>Debug Logging<html>", reloadOnChange = true)
    private Boolean debugLogging = false;

    @Parameter(name = "<html>Disable ALL Logging<html>", reloadOnChange = true)
    private Boolean disableAllLogging = true;

    // Configurable Parameters - Mini ATR Bands
    @Parameter(name = "<html><b>===== Mini ATR Bands =====</b><br>Mini ATR Length<html>", minimum = 3, maximum = 50)
    private Integer miniAtrLength = 5;

    @Parameter(name = "<html>Mini ATR Factor<html>", minimum = 0.25, maximum = 2.0, step = 0.05)
    private Double miniAtrFactor = 0.3;

    @Parameter(name = "<html>Mini ATR Min Band Width (pips)<html>", minimum = 1, maximum = 20, step = 0.5)
    private Double miniAtrMinWidthPips = 4.0;

    @Parameter(name = "<html>Mini ATR Threshold Buffer (pips)<html>", minimum = 0.0, maximum = 5.0, step = 0.1)
    private Double miniAtrThresholdBufferPips = 1.0; // Default 1 pip buffer zone

    // Removed showMiniAtrBands and showMiniAtrTrend parameters as they are always
    // true
    @Parameter(name = "<html>Mini ATR Trend Strength Scale<html>", minimum = 5, maximum = 30)
    private Integer miniAtrTrendStrengthScale = 10;

    // Configurable Parameters - Mini ATR Trading
    @Parameter(name = "<html><b>===== Mini ATR Trading =====</b><br>Use Mini ATR Trend For Trading<html>", minimum = 0, maximum = 2)
    private Integer useMiniAtrTrendForTradingInt = 0; // 0=Off, 1=Alongside regular, 2=Exclusive

    @Parameter(name = "<html>Mini ATR Entry Stop Pips<html>", minimum = 0, maximum = 10, step = 0.5)
    private Double miniAtrEntryStopPips = 2.0;

    @Parameter(name = "<html>Mini ATR Exit ATR Factor<html>", minimum = 0.5, maximum = 99.0, step = 0.1)
    private Double miniAtrExitAtrFactor = 6.0; // Multiplier for ATR-based trailing stop

    @Parameter(name = "<html>Mini ATR Confirmation Bars<html>", minimum = 0, maximum = 5)
    private Integer miniAtrConfirmationBars = 0; // Default 2 bars for confirmation, 0 for immediate signal

    @Parameter(name = "<html>Mini ATR Profit Distance (pips)<html>", minimum = 1.0, maximum = 20.0, step = 0.5)
    private Double miniAtrProfitDistancePips = 8.0; // Default to 8 pips for profitable trade detection

    @Parameter(name = "<html>Mini ATR Profitable Exit Buffer (pips)<html>", minimum = 0.0, maximum = 10.0, step = 0.5)
    private Double miniAtrProfitableExitBufferPips = 4.0; // Default to 4 pips relaxed threshold for profitable
                                                          // positions

    @Parameter(name = "<html>Mini ATR Exit Trailing Stop Pips<html>", minimum = 0.0, maximum = 10.0, step = 0.5)
    private Double miniAtrExitTrailingStopPips = 0.0; // Default 0 = immediate exit (current behavior)

    // Configurable Parameters - Standard Trading Settings
    @Parameter(name = "<html><b>===== Standard Trading Settings =====</b><br>Breakout Trailing Stop Distance<html>", minimum = 1.0, maximum = 50.0, step = 0.5)
    private Double breakoutTrailingStopDistance = 10.0;

    @Parameter(name = "<html>Trend Following Trailing Stop Distance<html>", minimum = 1.0, maximum = 20.0, step = 0.5)
    private Double trendFollowingTrailingStopDistance = 5.0;

    @Parameter(name = "<html>Use Trailing Stop<html>", reloadOnChange = true)
    private Boolean useTrailingStop = false;

    @Parameter(name = "<html><font color='#CC0000'>Catastrophic Stop Multiplier</font><html>", minimum = 1.0, maximum = 10.0)
    private Double catastrophicStopMultiplier = 6.0;

    @Parameter(name = "<html><font color='#CC0000'>Disable Catastrophic Stops</font><html>", reloadOnChange = true)
    private Boolean disableCatastrophicStops = true;

    @Parameter(name = "<html>Trend Threshold<html>", minimum = 0.0001, maximum = 0.01, step = 0.0001)
    private Double trendThreshold = 0.001; // Configurable trend threshold

    @Parameter(name = "<html>Cooldown Period (bars)<html>", minimum = 0, maximum = 100)
    private Integer cooldownPeriod = 2; // Configurable cooldown period - was 10

    // Configurable Parameters - Standard Deviation Bands
    @Parameter(name = "<html><b>===== Standard Deviation Bands =====</b><br>StdDev Bands Multiplier<html>", minimum = 0.1, maximum = 5.0)
    private Double stdDevMultiplier = 1.95; // was 2.0

    @Parameter(name = "<html>StdDev Bands Window (sec)<html>", minimum = 10, maximum = 3600)
    private Integer stdBandsWindowSecs = 300; // was 300

    // Add per-MA stddev multipliers
    @Parameter(name = "<html>MA2 StdDev Multiplier<html>", minimum = 0.1, maximum = 5.0, step = 0.1)
    private Double ma2StdDevMultiplier = 1.35; // was 1.0
    @Parameter(name = "<html>MA3 StdDev Multiplier<html>", minimum = 0.1, maximum = 5.0, step = 0.1)
    private Double ma3StdDevMultiplier = 1.65; // was 1.5
    @Parameter(name = "<html>MA4 StdDev Multiplier<html>", minimum = 0.1, maximum = 5.0, step = 0.1)
    private Double ma4StdDevMultiplier = 1.85; // was 1.8
    @Parameter(name = "<html>MA5 StdDev Multiplier<html>", minimum = 0.1, maximum = 5.0, step = 0.1)
    private Double ma5StdDevMultiplier = 2.05; // was 2.1

    // Configurable Parameters - Width Reversal Detector
    @Parameter(name = "<html><b>===== Width Reversal Detector =====</b><br>Enable Width Reversal Detector<html>", reloadOnChange = true)
    private Boolean enableReversalDetector = false;

    @Parameter(name = "<html>Width Detector Lookback<html>", minimum = 3, maximum = 20)
    private Integer widthDetectorLookback = 5;

    @Parameter(name = "<html>Width Detector Threshold %<html>", minimum = 5, maximum = 30, step = 1)
    private Integer widthDetectorThresholdPct = 15;

    @Parameter(name = "<html>Width Detector Min Bars Between Signals<html>", minimum = 3, maximum = 50)
    private Integer widthDetectorMinBarsBetween = 10;

    @Parameter(name = "<html>Width Detector Price Movement %<html>", minimum = 0, maximum = 10, step = 0.5)
    private Double widthDetectorPriceMovementPct = 2.0; // Set to 0 to disable price confirmation

    private WidthATRReversalDetector reversalDetector;

    // Configurable Parameters - Live Trading
    @Parameter(name = "<html><b>===== Live Trading Parameters =====</b><br>Position Size</html>", minimum = 1, maximum = 100)
    private Integer positionSize = 1;

    @Parameter(name = "<html>Max Daily Loss ($)</html>", minimum = 100, maximum = 10000)
    private Double maxDailyLoss = 1000.0;

    @Parameter(name = "<html>Enable Live Trading</html>", reloadOnChange = false)
    private Boolean enableLiveTrading = false;

    @Parameter(name = "<html><b>===== Sound Alert Settings =====</b><br>Use Simple Beeps for Exits<html>", reloadOnChange = true)
    private Boolean useSimpleBeepsForExits = false;

    // Mini ATR Trend Trading variables
    private int useMiniAtrTrendForTrading = 2; // Default to exclusive mode
    private int previousMiniAtrTrend = 0; // Track previous trend for change detection
    private double miniAtrEntryPrice = 0.0;
    private double miniAtrTrailingStopPrice = 0.0;
    private int miniAtrConfirmationCounter = 0;
    private boolean miniAtrDataValidated = false; // Track data validation state
    private Indicator miniAtrTradingModeIndicator;
    private Indicator miniAtrTrailingStopIndicator;

    // Trailing entry variables
    private boolean hasPendingEntryOrder = false;
    private int pendingEntryDirection = 0; // 1 for long, -1 for short
    private double pendingEntryStopLevel = 0.0;
    private double bestPriceForTrailingEntry = 0.0; // Tracks best price for trailing
    private Indicator pendingEntryStopIndicator;
    private Indicator bestPriceIndicator;

    // Trailing exit variables
    private boolean hasPendingExit = false;
    private int pendingExitDirection = 0; // 1 for long exit, -1 for short exit
    private double pendingExitStopLevel = 0.0;
    private double bestPriceForTrailingExit = 0.0;
    private int savedExitTrend = 0; // Store trend that triggered exit for potential opposite entry
    private int pendingExitConfirmationBars = 0; // For trend re-reversal confirmation
    private Indicator pendingExitStopIndicator;
    private Indicator bestExitPriceIndicator;

    // Indicators
    private Indicator ma1TopHigh, ma1BotLow, ma2TopHigh, ma2BotLow, ma3TopHigh, ma3BotLow, ma4TopHigh, ma4BotLow,
            ma1TopHighDimmed, ma1BotLowDimmed, ma2TopHighDimmed, ma2BotLowDimmed,
            ma3TopHighDimmed, ma3BotLowDimmed, ma4TopHighDimmed, ma4BotLowDimmed,
            ma1TrendIndicator, ma2TrendIndicator, ma3TrendIndicator, ma4TrendIndicator,
            positionIndicator, tradePLIndicator, NubiaTotalPLIndicator, ma3ConfirmationIndicator,
            uptrendIndicator, downtrendIndicator, strongBullishIndicator, strongBearishIndicator,
            ma1HeightIndicator, ma2HeightIndicator, ma3HeightIndicator, ma4HeightIndicator, totalTradesIndicator;

    // --- Add missing MA5 indicators here ---
    private Indicator ma5TopHigh, ma5BotLow, ma5TopHighDimmed, ma5BotLowDimmed, ma5HeightIndicator, ma5TrendIndicator;

    // Mini ATR Bands indicators
    private Indicator upperMiniAtrBandIndicator, lowerMiniAtrBandIndicator, middleMiniAtrBandIndicator,
            widthMiniAtrBandIndicator;
    private Indicator miniAtrTrendIndicator, miniAtrTrendStrengthIndicator;
    private final Color miniAtrUpperColor = new Color(128, 128, 128);
    private final Color miniAtrLowerColor = new Color(128, 128, 128);
    private final Color miniAtrMidColor = new Color(255, 255, 255);
    private final Color miniAtrWidthColor = new Color(30, 144, 255); // Dodger blue color for width indicator

    // Trade icons indicator
    private Indicator tradeIconsIndicator;

    // Market price indicator
    private Indicator marketPriceIndicator;

    // Trade Reason Indicator
    private Indicator TradeEntryReasonIndicator; // New indicator for entry reasons
    private Indicator TradeExitReasonIndicator; // Renamed indicator for exit reasons
    private Indicator maProfitProtectorActiveIndicator; // Indicator for MA Profit Protector state

    // Colors

    // Entry Triple Stop Colors
    private final Color entryL1LongColor = new Color(173, 216, 230); // Light Blue
    private final Color entryL2LongColor = new Color(30, 144, 255); // Dodger Blue
    private final Color entryL3LongColor = new Color(0, 0, 139); // Dark Blue
    private final Color entryL1ShortColor = new Color(255, 182, 193); // Light Pink
    private final Color entryL2ShortColor = new Color(255, 20, 147); // Deep Pink
    private final Color entryL3ShortColor = new Color(139, 0, 139); // Dark Magenta

    private final Color redColor = new Color(255, 0, 0),
            greenColor = new Color(0, 255, 0),
            dimRedColor = new Color(146, 105, 102),
            dimGreenColor = new Color(82, 117, 82),
            trendColor = new Color(255, 165, 0),
            positionColor = new Color(255, 255, 0),
            tradePLColor = new Color(255, 215, 0),
            totalPLColor = new Color(128, 0, 128),
            uptrendColor = new Color(0, 128, 0),
            downtrendColor = new Color(178, 34, 34),
            strongBullishColor = new Color(0, 100, 0),
            strongBearishColor = new Color(139, 0, 0),
            ma1HeightColor = new Color(70, 130, 180),
            ma2HeightColor = new Color(0, 191, 255),
            ma3HeightColor = new Color(30, 144, 255),
            ma4HeightColor = new Color(65, 105, 225);

    private final Color maProfitProtectorActiveColor = new Color(0, 200, 200); // Teal-ish color for active state

    // Add these colors near the other color declarations
    private final Color stdTopColor = Color.WHITE;
    private final Color stdBotColor = Color.YELLOW;

    // Data storage
    private double[] highPrices, lowPrices, closePrices, volumeValues, cumulativeVolume, cumulativeWgtHigh,
            cumulativeWgtLow;
    private int head = 0, size = 0, capacity;

    // VWAP values
    private double latestMa1TopHighValue = Double.NaN, latestMa1BotLowValue = Double.NaN,
            latestMa2TopHighValue = Double.NaN, latestMa2BotLowValue = Double.NaN,
            latestMa3TopHighValue = Double.NaN, latestMa3BotLowValue = Double.NaN,
            latestMa4TopHighValue = Double.NaN, latestMa4BotLowValue = Double.NaN,
            latestMa5TopHighValue = Double.NaN, latestMa5BotLowValue = Double.NaN;

    // Previous VWAP values
    private double previousMa1TopHighValue = Double.NaN, previousMa1BotLowValue = Double.NaN,
            previousMa2TopHighValue = Double.NaN, previousMa2BotLowValue = Double.NaN,
            previousMa3TopHighValue = Double.NaN, previousMa3BotLowValue = Double.NaN,
            previousMa4TopHighValue = Double.NaN, previousMa4BotLowValue = Double.NaN,
            previousMa5TopHighValue = Double.NaN, previousMa5BotLowValue = Double.NaN;

    // Previous height values
    private double previousMa1Height = Double.NaN, previousMa2Height = Double.NaN,
            previousMa3Height = Double.NaN, previousMa4Height = Double.NaN, previousMa5Height = Double.NaN;

    // Trend values
    private int ma1Trend = 0, ma2Trend = 0, ma3Trend = 0, ma4Trend = 0, ma5Trend = 0;
    private int ma1TrendSmoothingCounter = 0, ma1LastTrend = 0;
    private int ma2TrendSmoothingCounter = 0, ma2LastTrend = 0;
    private int ma3TrendSmoothingCounter = 0, ma3LastTrend = 0;
    private int ma4TrendSmoothingCounter = 0, ma4LastTrend = 0;
    private int ma5TrendSmoothingCounter = 0, ma5LastTrend = 0;

    // Trading state
    private int currentPosition = 0;
    private double entryPrice = 0.0, stopPrice, targetPrice, currentSizeMultiplier = 1.0, totalPL = 0.0;
    private List<Double> tradePLs = new ArrayList<>(1000);
    private int barsSinceLastExit = 2; // Add this missing variable declaration

    // Trailing stop variables
    private double highestPrice = Double.MIN_VALUE, lowestPrice = Double.MAX_VALUE, currentTrailingStopDistance = 5.0,
            lastExitPrice = 0.0;

    // Trend duration tracking
    private int uptrendDuration = 0, downtrendDuration = 0;
    private boolean isStrongUptrendNow = false, isStrongDowntrendNow = false;
    private int strongUptrendDuration = 0, strongDowntrendDuration = 0;

    private boolean ma3ConfirmationActive = false;

    private BucketedAverageWithSTDBands stdBands;
    private Indicator upperBandIndicator, lowerBandIndicator;
    private final Color upperBandColor = new Color(180, 100, 220);
    private final Color lowerBandColor = new Color(150, 80, 200);
    private Indicator stdBandsNormPriceDistIndicator;

    private volatile long currentTimestampNanos = 0;
    private volatile long lastProcessedTimestampNanos = 0;
    private volatile boolean receivedFirstTimestamp = false;

    private MiniAtrBandsCalculator miniAtrBandsCalculator;

    // OrderSenderControllerV2 instance
    private OrderSenderControllerV2 OSCInstanceMain;
    private OrderSenderControllerV2 OSCInstance;

    // Reference to the UI instance for gauge updates
    private OrdersTestUIV3 ordersTestUIV3Instance;

    // Chop Zone Detection
    @Parameter(name = "<html><b>===== Chop Zone Detection =====</b><br>Enable Chop Detection<html>", reloadOnChange = true)
    private Boolean enableChopDetection = false;

    @Parameter(name = "<html>MA2 Slope Threshold<html>", minimum = 0.0001, maximum = 0.005, step = 0.0001)
    private Double ma2SlopeThreshold = 0.0015;

    @Parameter(name = "<html>MA3 Slope Threshold<html>", minimum = 0.00005, maximum = 0.001, step = 0.00005)
    private Double ma3SlopeThreshold = 0.0006;

    @Parameter(name = "<html>Crossover Lookback Period (bars)<html>", minimum = 5, maximum = 50)
    private Integer crossoverLookbackPeriod = 20;

    @Parameter(name = "<html>Min Crossovers For Chop<html>", minimum = 1, maximum = 10)
    private Integer minCrossoversForChop = 3;

    @Parameter(name = "<html>Chop Confirmation Bars<html>", minimum = 2, maximum = 10)
    private Integer chopConfirmationBars = 3;

    // --- Add New Chop Zone Parameters Here ---
    @Parameter(name = "<html>VWAP Proximity Threshold (pips)<html>", minimum = 0.1, maximum = 50.0, step = 0.1)
    private Double vwapProximityThresholdPips = 5.0;

    @Parameter(name = "<html>POC Proximity Threshold (pips)<html>", minimum = 0.1, maximum = 50.0, step = 0.1)
    private Double pocProximityThresholdPips = 5.0;

    @Parameter(name = "<html>VWAP/POC Proximity Percent (%)<html>", minimum = 0.01, maximum = 1.0, step = 0.01)
    private Double vwapPocProximityPercent = 0.2; // Representing 0.2%

    @Parameter(name = "<html>Price in VWAP-POC Zone Duration (bars)<html>", minimum = 1, maximum = 100)
    private Integer priceInVwapPocZoneDurationBars = 10;

    @Parameter(name = "<html>VWAP Crossover Lookback (bars)<html>", minimum = 5, maximum = 100)
    private Integer vwapCrossoverLookbackPeriod = 20;

    @Parameter(name = "<html>Min VWAP Crossovers For Chop<html>", minimum = 1, maximum = 20)
    private Integer minVwapCrossoversForChop = 3;
    // ----------------------------------------

    private ChopZoneCalculatorV2 chopZoneCalculator;
    private Indicator chopZoneIndicator;
    private Indicator chopIntensityIndicator;
    private Indicator ma3BandsInsideMiniAtrIndicator;

    // --- Add New Chop Zone Boundary Indicators Here ---
    private Indicator chopZoneUpperLimitIndicator;
    private Indicator chopZoneLowerLimitIndicator;
    // -----------------------------------------------

    // ===== Abnormal Market Conditions =====
    @Parameter(name = "<html><b>===== Abnormal Market Conditions =====</b><br>Enable Abnormal Price Jump Detection<html>", reloadOnChange = true)
    private Boolean enableAbnormalPriceJumpDetection = true;

    @Parameter(name = "<html>Abnormal Price Jump Threshold (ticks)<html>", minimum = 1.0, maximum = 1000.0, step = 0.1, reloadOnChange = true)
    private Double abnormalPriceJumpThresholdAmount = 5.0;

    private double lastTradePrice = Double.NaN;
    private Indicator abnormalPriceJumpIndicator;
    private Indicator bidAskDiffIndicator;
    // New variables for MA slope-based exit suppression for Mini ATR trading
    private boolean maSlopeConfirmationActive = false; // New flag for slope-based suppression
    private final int slopeLookback = 3; // Lookback period for slope calculation
    private double prevMa2BotLowValue = Double.NaN;
    private double prevMa3BotLowValue = Double.NaN;
    private double prevMa4BotLowValue = Double.NaN;
    private boolean prevValuesInitialized = false;
    private Indicator maSlopeConfirmationIndicator; // To visualize slope confirmation

    // Add these with other member variables near the top of the class
    private double ma2BotSlope = 0.0;
    private double ma3BotSlope = 0.0;
    private double ma4BotSlope = 0.0;
    private double ma5BotSlope = 0.0;
    private double ma2TopSlope = 0.0;
    private double ma3TopSlope = 0.0;
    private double ma4TopSlope = 0.0;
    private double ma5TopSlope = 0.0;
    private boolean ma2Available = false;
    private boolean ma3Available = false;
    private boolean ma4Available = false;
    private boolean ma5Available = false;

    // Indicators for MA slopes
    private Indicator ma2BotSlopeIndicator, ma3BotSlopeIndicator, ma4BotSlopeIndicator, ma5BotSlopeIndicator;
    private Indicator ma2TopSlopeIndicator, ma3TopSlopeIndicator, ma4TopSlopeIndicator, ma5TopSlopeIndicator;
    private final Color ma2BotSlopeColor = new Color(0, 128, 255); // Blue
    private final Color ma3BotSlopeColor = new Color(0, 192, 255); // Light Blue
    private final Color ma4BotSlopeColor = new Color(0, 255, 255); // Cyan
    private final Color ma5BotSlopeColor = new Color(0, 255, 128); // Light Green
    private final Color ma2TopSlopeColor = new Color(255, 0, 128); // Red
    private final Color ma3TopSlopeColor = new Color(255, 0, 192); // Light Red
    private final Color ma4TopSlopeColor = new Color(255, 0, 255); // Magenta
    private final Color ma5TopSlopeColor = new Color(255, 128, 0); // Orange

    // Add this new indicator declaration with the other indicator declarations
    private Indicator maSlopeDataAvailableIndicator;

    // New variables for MA slope trend detection
    private Indicator ma2SlopeTrendIndicator, ma3SlopeTrendIndicator, ma4SlopeTrendIndicator, ma5SlopeTrendIndicator;
    private final Color ma2SlopeTrendColor = new Color(255, 128, 0); // Orange
    private final Color ma3SlopeTrendColor = new Color(255, 165, 0); // Dark orange
    private final Color ma4SlopeTrendColor = new Color(255, 215, 0); // Gold
    private final Color ma5SlopeTrendColor = new Color(255, 255, 0); // Yellow

    // Current slope trends (-1, 0, 1)
    private int ma2SlopeTrend = 0;
    private int ma3SlopeTrend = 0;
    private int ma4SlopeTrend = 0;
    private int ma5SlopeTrend = 0;

    // Add these class variables with the other confirmation/indicator variables
    private boolean ma2SlopeTrendConfirmationActive = false;
    private Indicator ma2SlopeTrendConfirmationIndicator;

    // Visibility Trend Bias variables
    private Indicator visibilityTrendBiasIndicator;
    private double visibilityTrendBiasValue = 0.0;
    // Change from yellow/gold to blue for bias, and purple for suppression
    private final Color visibilityTrendBiasColor = new Color(30, 144, 255); // Dodger blue
    // Renamed for clarity: exit suppression
    private boolean visibilityTrendBiasExitSuppressionActive = false;
    private Indicator visibilityTrendBiasExitSuppressionIndicator;

    // Add these indicators with other indicator declarations near line 300
    private Indicator ma3TopVisibleBarsIndicator, ma3BotVisibleBarsIndicator;
    private Indicator ma4TopVisibleBarsIndicator, ma4BotVisibleBarsIndicator;
    private Indicator ma5TopVisibleBarsIndicator, ma5BotVisibleBarsIndicator;
    private Indicator ma3TopInvisibleBarsIndicator, ma3BotInvisibleBarsIndicator;
    private Indicator ma4TopInvisibleBarsIndicator, ma4BotInvisibleBarsIndicator;
    private Indicator ma5TopInvisibleBarsIndicator, ma5BotInvisibleBarsIndicator;

    // Add colors for visibility indicators near other color declarations
    private final Color ma3VisibleColor = new Color(0, 255, 255); // Cyan
    private final Color ma4VisibleColor = new Color(0, 255, 128); // Light Green
    private final Color ma5VisibleColor = new Color(128, 255, 0); // Lime
    private final Color ma3InvisibleColor = new Color(255, 0, 255); // Magenta
    private final Color ma4InvisibleColor = new Color(255, 128, 0); // Orange
    private final Color ma5InvisibleColor = new Color(192, 192, 0); // Dark Yellow

    // Add parameter to control MA2 slope trend confirmation
    @Parameter(name = "<html>Use MA2 Slope Trend Confirmation<html>", reloadOnChange = true)
    private Boolean useMa2SlopeTrendConfirmation = false;

    // Add parameter to control visibility trend bias exit suppression
    @Parameter(name = "<html>Use Visibility Trend Bias Exit Suppression<html>", reloadOnChange = true)
    private Boolean useVisibilityTrendBiasExitSuppression = true;

    // Add these constants (if not already present)
    private static final double STRONG_NEGATIVE_SLOPE = -1000.0;
    private static final double STRONG_POSITIVE_SLOPE = 1000.0;
    private static final double NEUTRAL_SLOPE = 0.0;

    // Add new MASlopeMetrics instances for MA2, MA3, MA4:
    private MASlopeMetrics ma2SlopeMetrics = new MASlopeMetrics(STRONG_NEGATIVE_SLOPE, STRONG_POSITIVE_SLOPE,
            NEUTRAL_SLOPE);
    private MASlopeMetrics ma3SlopeMetrics = new MASlopeMetrics(STRONG_NEGATIVE_SLOPE, STRONG_POSITIVE_SLOPE,
            NEUTRAL_SLOPE);
    private MASlopeMetrics ma4SlopeMetrics = new MASlopeMetrics(STRONG_NEGATIVE_SLOPE, STRONG_POSITIVE_SLOPE,
            NEUTRAL_SLOPE);

    // Add at the top with other fields:
    private AVwapMAs[] mas;

    // Add these constants near the top of the class
    private static final int MAX_ANCHOR_SEARCH_WINDOW = 100000; // Cap search window for anchors
    private static final int SPARSE_ANCHOR_STEP = 60; // Step for sparse search in very long MAs
    private static final int SPARSE_ANCHOR_THRESHOLD = 50000; // Use sparse search if MA length > this

    // Add parameter to control MA3 confirmation exit suppression
    @Parameter(name = "<html>Use MA3 Confirmation Exit Suppression<html>", reloadOnChange = true)
    private Boolean useMa3ConfirmationExitSuppression = false;

    // Add new indicators for MA+2std for each MA
    private Indicator ma2TopStdIndicator, ma2BotStdIndicator;
    private Indicator ma3TopStdIndicator, ma3BotStdIndicator;
    private Indicator ma4TopStdIndicator, ma4BotStdIndicator;
    private Indicator ma5TopStdIndicator, ma5BotStdIndicator;

    // --- Add indicators for min/max std values across MA3, MA4, MA5 ---
    private Indicator topHighStdMax345Indicator, topHighStdMin345Indicator;
    private Indicator botLowStdMax345Indicator, botLowStdMin345Indicator;

    // --- Add fields to track running min/max ---
    private double topHighStdMax345 = Double.NaN, topHighStdMin345 = Double.NaN;
    private double botLowStdMax345 = Double.NaN, botLowStdMin345 = Double.NaN;

    // Add parameter to enable/disable MA2 Std entry suppression
    @Parameter(name = "<html>Enable MA2 Std Entry Suppression (MiniATR)<html>", reloadOnChange = true)
    private Boolean enableMa2StdEntrySuppression = true;

    // State variable for suppression
    private boolean ma2StdEntrySuppressionActive = false;

    // --- Add state variable to track suppression *direction* ---
    private int ma2StdEntrySuppressionDirection = 0; // 0 = none, 1 = long suppressed, -1 = short suppressed

    // Indicator for suppression
    private Indicator ma2StdEntrySuppressionIndicator;

    // === New: MA-based Trailing/Pending Entry Parameters ===
    @Parameter(name = "<html><b>===== MA Trailing Entry =====</b><br>Enable MA Trailing Entry Stops<html>", reloadOnChange = true)
    private Boolean usePendingEntryStops = true;

    @Parameter(name = "<html>MA Pending Entry Stop Pips<html>", minimum = 0.0, maximum = 10.0, step = 0.5)
    private Double pendingEntryStopPips = 2.0;

    @Parameter(name = "<html>MA Pending Entry Confirmation Bars<html>", minimum = 0, maximum = 5)
    private Integer pendingEntryConfirmationBars = 0;

    @Parameter(name = "<html>MA Pending Entry Profit Distance (pips)<html>", minimum = 1.0, maximum = 20.0, step = 0.5)
    private Double pendingEntryProfitDistancePips = 8.0;

    @Parameter(name = "<html>MA Pending Entry Profitable Buffer (pips)<html>", minimum = 0.0, maximum = 10.0, step = 0.5)
    private Double pendingEntryProfitableBufferPips = 4.0;

    // === New: MA-based pending entry state ===
    private boolean hasMAPendingEntryOrder = false;
    private int maPendingEntryDirection = 0; // 1 for long, -1 for short
    private double maPendingEntryStopLevel = 0.0;
    private double maBestPriceForTrailingEntry = 0.0;
    private int maPendingEntryConfirmationCounter = 0;

    // === New: Store the reason code for the pending setup ===
    private int maPendingEntrySetupReasonCode = 0;

    // === MA Opportunity Entry Parameters and State ===
    @Parameter(name = "<html><b>===== MA Opportunity Entry =====</b><br>Enable MA Opportunity Entry<html>", reloadOnChange = true)
    private Boolean enableMAOpportunityEntry = true;

    @Parameter(name = "<html>Enable MA3 Opportunity Entry<html>", reloadOnChange = true)
    private Boolean enableMA3OpportunityEntry = false;

    @Parameter(name = "<html>Enable MA4 Opportunity Entry<html>", reloadOnChange = true)
    private Boolean enableMA4OpportunityEntry = true;

    @Parameter(name = "<html>MA Opportunity Entry Lookback (bars)<html>", minimum = 1, maximum = 500, step = 1)
    private Integer maOpportunityLookbackBars = 120;

    @Parameter(name = "<html>MA Opportunity Entry Hysteresis (pips)<html>", minimum = 0.5, maximum = 5.0, step = 0.5)
    private Double maOpportunityHysteresisPips = 2.0;

    // For each MA (MA3, MA4): [0]=last cross direction (+1 above, -1 below, 0
    // none), [1]=bar index of last cross
    private int[] ma3LastCross = new int[] { 0, -1 };
    private int[] ma4LastCross = new int[] { 0, -1 };
    private int barCounter = 0;

    // --- New parameters and indicators for MA3 and MA4 MA Rejection Opportunity
    // and trailing stop ---
    @Parameter(name = "<html>MA Rejection Trailing Stop Distance (pips)<html>", minimum = 0.0, maximum = 50.0, step = 0.5)
    private Double maRejectionTrailingStopPips = 5.0;

    private Indicator ma3RejectionOpportunityIndicator, ma4RejectionOpportunityIndicator;

    private int ma3RejectionOpportunityCountdown = 0;
    private int ma4RejectionOpportunityCountdown = 0;
    private int ma3RejectionOpportunityInsufficientCountdown = 0;
    private int ma4RejectionOpportunityInsufficientCountdown = 0;

    @Parameter(name = "<html>Invisible Bars Threshold<html>", minimum = 0, maximum = 100, step = 1)
    private Integer invisibleBarsThreshold = 5;

    // --- Add after other indicator declarations ---
    private Indicator globalPositionIndicator;
    private Indicator globalPLIndicator;

    // Add VWAPCalculator and indicators
    private VWAPCalculatorOptimized vwapCalculator;
    private Indicator vwapIndicator;
    private Indicator vwapUpperBandIndicator;
    private Indicator vwapLowerBandIndicator;

    // As of 2024-06, this is now a Double and passed directly to
    // VWAPCalculatorOptimized (no division by 10)
    @Parameter(name = "<html><b>===== VWAP Settings =====</b><br>VWAP StdDev Multiplier<html>", minimum = 0.1, maximum = 10.0, step = 0.1)
    private Double vwapStdDevMultiplier = 1.95; // was 2.0

    private String alias;

    // Add VWAP extended parameters
    @Parameter(name = "<html>VWAP ATR Period<html>", minimum = 2, maximum = 100)
    private Integer vwapAtrPeriod = 14;
    @Parameter(name = "<html>VWAP Band Multiplier<html>", minimum = 0.1, maximum = 10.0, step = 0.1)
    private Double vwapBandMultiplier = 2.0;
    @Parameter(name = "<html>VWAP EMA Alpha<html>", minimum = 0.01, maximum = 0.99, step = 0.01)
    private Double vwapEmaAlpha = 0.2;
    @Parameter(name = "<html>VWAP Slope Threshold<html>", minimum = 0.0, maximum = 0.01, step = 0.0001)
    private Double vwapSlopeThreshold = 0.0;

    // Add new VWAP indicators
    private Indicator vwapSlopeIndicator;
    private Indicator vwapPriceDistIndicator;
    private Indicator vwapNormPriceDistIndicator;

    // Add at class level:
    // private Map<String, Object[]> lastHealthStatusMap = new
    // java.util.LinkedHashMap<>(); // Removed

    // Add parameter to require sufficient data for MA visibility
    @Parameter(name = "<html>Require Sufficient Data for MA Visibility<html>", reloadOnChange = true)
    private Boolean requireSufficientForVisibility = true;

    @Parameter(name = "Enable Health Monitoring", reloadOnChange = false)
    private Boolean enableHealthMonitoring = false;

    // Add after enableHealthMonitoring declaration (line ~620)
    private Boolean lastEnableHealthMonitoring = null;
    private int intervalCallCounter = 0;

    @Parameter(name = "<html>Enable Development Indicators<html>", reloadOnChange = true)
    private Boolean enableDevelopmentIndicators = false;

    @Parameter(name = "<html>Enable Experimental Indicators<html>", reloadOnChange = true)
    private Boolean enableExperimentalIndicators = false;

    @Parameter(name = "Min Distance to VWAP (pips)", minimum = 0.0, maximum = 50.0, step = 0.5)
    private Double minDistanceToVWAPPips = 10.0;

    @Parameter(name = "Max Band-to-Price Distance (pips)", minimum = 0.0, maximum = 50.0, step = 0.5)
    private Double maxBandToPricePips = 10.0;

    @Parameter(name = "<html>Use ATR-based Trailing Stop for MA<html>", reloadOnChange = true)
    private Boolean useMaAtrTrailingStop = false; // Re-added for MA Pending Entry calculation

    @Parameter(name = "<html>MA ATR Trailing Stop Factor<html>", minimum = 0.1, maximum = 10.0, step = 0.1)
    private Double maAtrTrailingStopFactor = 1.0; // Re-added for MA Pending Entry calculation

    @Parameter(name = "<html>MA ATR Trailing Stop Lookback<html>", minimum = 2, maximum = 100)
    private Integer maAtrTrailingStopLookback = 14; // Re-added for MA Pending Entry calculation

    // === Profit Target System Parameters ===
    @Parameter(name = "<html><b><font color='#4CAF50'>===== Dynamic Profit Targets =====</font></b><br>&nbsp;&nbsp;Enable Profit Target System</html>", reloadOnChange = false)
    private Boolean enableProfitTargetSystem = false;

    @Parameter(name = "<html>&nbsp;&nbsp;Min Target Distance from Market (Pips)</html>", minimum = 0.5, maximum = 50.0, step = 0.5)
    private Double profitTargetMinDistanceFromMarketPips = 5.0;

    @Parameter(name = "<html>&nbsp;&nbsp;Max Active Targets (per side, if split)</html>", minimum = 1, maximum = 10)
    private Integer profitTargetMaxActiveCount = 3;

    @Parameter(name = "<html>&nbsp;&nbsp;Target Max Age (seconds)</html>", minimum = 1.0, maximum = 3600.0, step = 1.0)
    private Double profitTargetMaxAgeSeconds = 300.0;
    // ========================================

    // Add state for trailing stop controller - Removed for MA exits
    // private TrailingStopControllerV3.Position maTrailingPosition = null;
    // private TrailingStopControllerV3.StopSpec maTrailingStopSpec = null;

    // Add new indicator fields for VPA metrics
    private Indicator pocIndicator, hvn1Indicator, lvn1Indicator;
    // Add new indicators for 2nd HVN/LVN and value area
    private Indicator hvn2Indicator, lvn2Indicator, valIndicator, vahIndicator;

    // Add indicator fields for HVN/LVN 1-5
    private Indicator hvn3Indicator, hvn4Indicator, hvn5Indicator;
    private Indicator lvn3Indicator, lvn4Indicator, lvn5Indicator;

    // --- Independent Triple Stop Parameters ---

    // --- Entry Triple Stop Parameters ---
    @Parameter(name = "<html><b><font color='#1E90FF'>===== Entry Triple Stop =====</font></b><br>&nbsp;&nbsp;Enable Entry Triple Stop<html>", reloadOnChange = true)
    private Boolean entryTriple_Enabled = true;

    @Parameter(name = "<html>&nbsp;&nbsp;Stop Color Alpha (%)</html>", minimum = 0.0, maximum = 100.0, step = 1.0, reloadOnChange = true)
    private Double entryTriple_StopColorAlphaPercent = 60.0;

    // Layer 1: Fixed Offset
    @Parameter(name = "<html>&nbsp;&nbsp;L1 Fixed Offset (pips)</html>", minimum = 0.1, maximum = 1000.0, step = 0.1, reloadOnChange = true)
    private Double entryTriple_L1_OffsetPips = 10.0;

    // Layer 2: Fixed Offset + VolStop (Tightest)
    @Parameter(name = "<html>&nbsp;&nbsp;L2 Fixed Offset (pips)</html>", minimum = 0.1, maximum = 1000.0, step = 0.1, reloadOnChange = true)
    private Double entryTriple_L2_FixedOffsetPips = 15.0;
    @Parameter(name = "<html>&nbsp;&nbsp;L2 VolStop Period</html>", minimum = 2, maximum = 500, reloadOnChange = true)
    private Integer entryTriple_L2_VolStopPeriod = 250;
    @Parameter(name = "<html>&nbsp;&nbsp;L2 VolStop Multiplier</html>", minimum = 0.1, maximum = 20.0, step = 0.1, reloadOnChange = true)
    private Double entryTriple_L2_VolStopMultiplier = 4.0;

    // Layer 3: Trailing Fixed Offset
    @Parameter(name = "<html>&nbsp;&nbsp;L3 Fixed Offset (pips)</html>", minimum = 0.1, maximum = 1000.0, step = 0.1, reloadOnChange = true)
    private Double entryTriple_L3_OffsetPips = 20.0;
    @Parameter(name = "<html>&nbsp;&nbsp;L3 Trailing Increment (pips)</html>", minimum = 0.1, maximum = 1000.0, step = 0.1, reloadOnChange = true)
    private Double entryTriple_L3_TrailingIncrementPips = 1.0;

    @Parameter(name = "<html><b><font color='#FF8C00'>===== MA Exit Settings =====</font></b><br>&nbsp;&nbsp;Enable Normal MA Exits (Target/Trigger)</html>", reloadOnChange = false)
    private Boolean enableMaNormalExits = true;

    @Parameter(name = "<html>&nbsp;&nbsp;Enable MA Profit Protector</html>", reloadOnChange = false)
    private Boolean useMAtoProtectProfits = true;
    private boolean ActivateMAProfitProtector = false;

    @Parameter(name = "<html>MA to Use for Profit Protection (1=MA1, 2=MA2, 3=MA3, 4=MA4, 5=MA5)</html>", minimum = 1, maximum = 5, reloadOnChange = false)
    private Integer maToUseForProfitProtection = 2; // Default to MA3

    @Parameter(name = "<html>&nbsp;&nbsp;Enable Catastrophic MA Exit</html>", reloadOnChange = false)
    private Boolean enableMaCatastrophicExit = true;

    // New Parameters for OSC Profit Targets
    @Parameter(name = "<html><b><font color='#FFD700'>===== OSC Profit Targets =====</font></b><br>&nbsp;&nbsp;OSC Main - Realized Profit Target ($)</html>", minimum = 0.0, maximum = 100000.0, step = 100.0)
    private Double oscMainRealizedProfitTarget = 2000.0;

    @Parameter(name = "<html>&nbsp;&nbsp;OSC Main - Total Profit Target ($)</html>", minimum = 0.0, maximum = 100000.0, step = 100.0)
    private Double oscMainTotalProfitTarget = 3000.0;

    @Parameter(name = "<html>&nbsp;&nbsp;OSC MiniATR - Realized Profit Target ($)</html>", minimum = 0.0, maximum = 100000.0, step = 100.0)
    private Double oscMiniAtrRealizedProfitTarget = 3000.0;

    @Parameter(name = "<html>&nbsp;&nbsp;OSC MiniATR - Total Profit Target ($)</html>", minimum = 0.0, maximum = 100000.0, step = 100.0)
    private Double oscMiniAtrTotalProfitTarget = 3500.0;

    @Parameter(name = "<html><b><font color='#E67E22'>===== OSC Drawdown Guard Settings =====</font></b><br>&nbsp;&nbsp;DD Guard - Total Cash Limit ($)</html>", minimum = 0.0, maximum = 100000.0, step = 100.0)
    private Double oscDdCash = 500.0;

    @Parameter(name = "<html>&nbsp;&nbsp;DD Guard - Total Percent Limit (%)</html>", minimum = 0.0, maximum = 1.0, step = 0.001)
    private Double oscDdPct = 0.05; // e.g., 5%

    @Parameter(name = "<html>&nbsp;&nbsp;DD Guard - Unrealized Cash Limit ($)</html>", minimum = 0.0, maximum = 100000.0, step = 50.0)
    private Double oscDdUnrCash = 300.0;

    @Parameter(name = "<html>&nbsp;&nbsp;DD Guard - Unrealized Percent Limit (%)</html>", minimum = 0.0, maximum = 1.0, step = 0.001)
    private Double oscDdUnrPct = 0.025; // e.g., 2.5%

    @Parameter(name = "<html>&nbsp;&nbsp;DD Guard - Profit Give-Back Percent (%)</html>", minimum = 0.0, maximum = 100.0, step = 1.0)
    private Double oscDdGivePct = 50.0; // e.g., 50%

    @Parameter(name = "<html>&nbsp;&nbsp;DD Guard - Profit Epsilon (ticks)</html>", minimum = 0.0, maximum = 100.0, step = 0.1)
    private Double oscProfitEpsilonTicks = 4.0; // e.g., 4 ticks

    @Parameter(name = "<html>&nbsp;&nbsp;DD Guard - Enable Total DD Actions</html>", reloadOnChange = false)
    public Boolean oscEnableTotalDDAction = false; // Note: public or ensure getter if private and accessed

    @Parameter(name = "<html>&nbsp;&nbsp;DD Guard - Enable Unrealized DD Actions</html>", reloadOnChange = false)
    public Boolean oscEnableUnrealizedDDAction = true; // Note: public or ensure getter
    // End New Parameters

    @Parameter(name = "<html><b><font color='#FF8C00'>===== Independent Triple Stop Viz =====</font></b><br>&nbsp;&nbsp;Trailing Increment (pips)</html>", minimum = 0.0, maximum = 100.0, step = 0.1, reloadOnChange = true)
    private Double indTriple_TrailingIncrementPips = 5.0;
    @Parameter(name = "<html>&nbsp;&nbsp;Hysteresis (pips)</html>", minimum = 0.0, maximum = 100.0, step = 0.1, reloadOnChange = true)
    private Double indTriple_HysteresisPips = 4.0;
    @Parameter(name = "<html>&nbsp;&nbsp;Stop Color Alpha (%)</html>", minimum = 0.0, maximum = 100.0, step = 1.0, reloadOnChange = true)
    private Double indTriple_StopColorAlphaPercent = 75.0;

    // Warn Layer (ATR + VolStop)
    @Parameter(name = "<html><b>&nbsp;&nbsp;Warn Layer</b><br>&nbsp;&nbsp;&nbsp;&nbsp;ATR Period</html>", minimum = 2, maximum = 500, reloadOnChange = true)
    private Integer indTriple_Warn_AtrPeriod = 500;
    @Parameter(name = "<html>&nbsp;&nbsp;&nbsp;&nbsp;ATR Multiplier</html>", minimum = 0.1, maximum = 20.0, step = 0.1, reloadOnChange = true)
    private Double indTriple_Warn_AtrMultiplier = 6.5;
    @Parameter(name = "<html>&nbsp;&nbsp;&nbsp;&nbsp;VolStop Period</html>", minimum = 2, maximum = 500, reloadOnChange = true)
    private Integer indTriple_Warn_VolStopPeriod = 500;
    @Parameter(name = "<html>&nbsp;&nbsp;&nbsp;&nbsp;VolStop Multiplier</html>", minimum = 0.1, maximum = 20.0, step = 0.1, reloadOnChange = true)
    private Double indTriple_Warn_VolStopMultiplier = 5.0;

    // Trigger Layer (FixedOffset + ATR_HL)
    @Parameter(name = "<html><b>&nbsp;&nbsp;Trigger Layer</b><br>&nbsp;&nbsp;&nbsp;&nbsp;Fixed Offset (pips)</html>", minimum = 0.1, maximum = 1000.0, step = 0.1, reloadOnChange = true)
    private Double indTriple_Trig_FixedOffsetPips = 117.0;
    @Parameter(name = "<html>&nbsp;&nbsp;&nbsp;&nbsp;ATR_HL ATR Period</html>", minimum = 2, maximum = 500, reloadOnChange = true)
    private Integer indTriple_Trig_AtrHl_AtrPeriod = 35;
    @Parameter(name = "<html>&nbsp;&nbsp;&nbsp;&nbsp;ATR_HL Smooth Period</html>", minimum = 2, maximum = 500, reloadOnChange = true)
    private Integer indTriple_Trig_AtrHl_SmoothPeriod = 21;
    @Parameter(name = "<html>&nbsp;&nbsp;&nbsp;&nbsp;ATR_HL Multiplier</html>", minimum = 0.1, maximum = 20.0, step = 0.1, reloadOnChange = true)
    private Double indTriple_Trig_AtrHl_Multiplier = 6.0;
    @Parameter(name = "<html>&nbsp;&nbsp;&nbsp;&nbsp;ATR_HL Min Offset (pips)</html>", minimum = 0.1, maximum = 1000.0, step = 0.1, reloadOnChange = true)
    private Double indTriple_Trig_AtrHl_MinOffsetPips = 10.0;
    @Parameter(name = "<html>&nbsp;&nbsp;&nbsp;&nbsp;Mode (0=TIGHTEST, 1=WIDEST, 2=AVERAGE)</html>", minimum = 0, maximum = 2, reloadOnChange = true)
    private Integer indTriple_Trig_CompositeModeInt = 0;

    // Catastrophic Layer (ATR_HL + FixedPercent)
    @Parameter(name = "<html><b>&nbsp;&nbsp;Cat Layer</b><br>&nbsp;&nbsp;&nbsp;&nbsp;ATR_HL ATR Period</html>", minimum = 2, maximum = 500, reloadOnChange = true)
    private Integer indTriple_Cat_AtrHl_AtrPeriod = 51;
    @Parameter(name = "<html>&nbsp;&nbsp;&nbsp;&nbsp;ATR_HL Smooth Period</html>", minimum = 2, maximum = 500, reloadOnChange = true)
    private Integer indTriple_Cat_AtrHl_SmoothPeriod = 35;
    @Parameter(name = "<html>&nbsp;&nbsp;&nbsp;&nbsp;ATR_HL Multiplier</html>", minimum = 0.1, maximum = 20.0, step = 0.1, reloadOnChange = true)
    private Double indTriple_Cat_AtrHl_Multiplier = 3.8;
    @Parameter(name = "<html>&nbsp;&nbsp;&nbsp;&nbsp;ATR_HL Min Offset (pips)</html>", minimum = 0.1, maximum = 1000.0, step = 0.1, reloadOnChange = true)
    private Double indTriple_Cat_AtrHl_MinOffsetPips = 10.0;
    @Parameter(name = "<html>&nbsp;&nbsp;&nbsp;&nbsp;Fixed Percent Value (e.g., 0.005)</html>", minimum = 0.0001, maximum = 0.1, step = 0.0001, reloadOnChange = true)
    private Double indTriple_Cat_FixedPercentValue = 0.002;

    // --- Independent Triple Stop State & Indicators ---

    // --- Entry Triple Stop State ---
    private StopSpec entryTripleStopSpec;
    private StopSpec entryTripleL1Spec, entryTripleL2Spec, entryTripleL3Spec;

    private double entryTripleL2Long_CurrentStopLevel = Double.NaN;
    private double entryTripleL2Short_CurrentStopLevel = Double.NaN;
    private double entryTripleL3Long_CurrentStopLevel = Double.NaN;
    private double entryTripleL3Short_CurrentStopLevel = Double.NaN;
    private double entryTripleL3Long_BestPriceForTrailing = Double.NaN;
    private double entryTripleL3Short_BestPriceForTrailing = Double.NaN;

    private StopSpec independentTripleStopSpec;
    private Indicator independentTripleWarnIndicator;
    private Indicator independentTripleTriggerIndicator;
    private Indicator independentTripleCatIndicator;

    // Latest MA Triple Stop Levels
    private double latestMaAdjustedCatStop = Double.NaN;
    private double latestMaAdjustedTrigStop = Double.NaN;
    private double latestMaAdjustedWarnStop = Double.NaN;

    private boolean isMaTradeActive = false; // Flag to identify active MA trade
    
    // --- Entry Triple Stop Indicators ---
    private Indicator entryTripleL1LongIndicator, entryTripleL1ShortIndicator;
    private Indicator entryTripleL2LongIndicator, entryTripleL2ShortIndicator;
    private Indicator entryTripleL3LongIndicator, entryTripleL3ShortIndicator;

    // --- State for 2-second aggregation (using Bar object) for Independent Triple
    // Stop Viz ---
    private final Bar agg2s_bar = new Bar(); // The aggregating Bar object for 2s interval
    private long agg2s_startTimeNanos = 0; // Anchor for the start of the 2s window
    private Bar agg2s_lastCompletedBar = null; // Holds the finalized 2s bar COPY
    private boolean agg2s_barNeedsReset = true; // Flag to manage initial reset
    // --- End 2-second aggregation state ---

    // Profit Target System Fields
    private DynamicProfitTargetGenerator profitTargetGenerator;
    private List<PotentialTarget> activePotentialTargets = new ArrayList<>();
    private List<Indicator> profitTargetSupportIndicators = new ArrayList<>();
    private List<Indicator> profitTargetResistanceIndicators = new ArrayList<>();
    private final Color profitTargetSupportColor = new Color(0, 255, 0, 150); // Semi-transparent green
    private final Color profitTargetResistanceColor = new Color(255, 0, 0, 150); // Semi-transparent red

    // For VWAP Proximity
    private ProximityConditionChecker vwapProximityChecker;
    private static final int VWAP_PROXIMITY_LOOKBACK_PERIOD = 30; // Define this constant if not already
    private static final double VWAP_PROXIMITY_THRESHOLD_PIPS = 5.0; // Define this too

    // For VPA (POC, VAH, VAL) Proximity
    private ProximityConditionChecker vpaProximityChecker;
    private boolean ActivateVPAProximityCondition = false; // Will be controlled by vpaProximityChecker

    // Define these constants, or make them @Parameters if you want them
    // configurable
    private static final int VPA_PROXIMITY_LOOKBACK_PERIOD = 15; // Example: 30 bars
    private static final double VPA_PROXIMITY_THRESHOLD_PIPS = 5.0; // Example: 10 pips

    BalanceInfo last_balanceInfo;
    StatusInfo last_statusInfo;
    String key = "";
    String oldkey = "";
    double LastAccountBalance = 0.0;

    int LastAccountPosition = 0;
    double LastAccountunrealizedPnl = 0.0;
    double LastAccountrealizedPnl = 0.0;

    double LastAccountvolume = 0.0;
    int LastAccountworkingBuys = 0;
    int LastAccountworkingSells = 0;

    @Override
    public void initialize(String alias, InstrumentInfo info, Api api, InitialState initialState) {
        synchronized (lock) {
            this.api = api;
            this.instrumentInfo = info;
            this.pips = info.pips;

            Layer1ApiProvider provider = api.getProvider();
            this.provider = provider; // set the base class provider field
            // Register listeners on the actual subclass instance
            if (provider != null) {
                ListenableHelper.addListeners(provider, this);
            }

            setApi(api);

            this.alias = OrderSenderControllerV2.getTrimmedAlias(alias);
            // Log version information
            if (!disableAllLogging)
                Log.info("Initializing Nubia Auto Midas V" + VERSION);
            Log.info("[HEALTH_UI_DEBUG] In initialize - enableHealthMonitoring initial value: "
                    + enableHealthMonitoring); // Added Log

            // Reset all trading state to ensure we start clean
            resetTradeState();
            miniAtrDataValidated = false;
            hasPendingEntryOrder = false;
            pendingEntryDirection = 0;
            pendingEntryStopLevel = 0.0;
            bestPriceForTrailingEntry = 0.0;
            previousMiniAtrTrend = 0;
            miniAtrEntryPrice = 0.0;
            miniAtrTrailingStopPrice = 0.0;
            miniAtrConfirmationCounter = 0;
            barsSinceLastExit = 2;
            uptrendDuration = 0;
            downtrendDuration = 0;
            strongUptrendDuration = 0;
            strongDowntrendDuration = 0;
            totalPL = 0.0;
            tradePLs.clear();

            // Initialize previous MA values to NaN
            previousMa1TopHighValue = Double.NaN;
            previousMa1BotLowValue = Double.NaN;
            previousMa2TopHighValue = Double.NaN;
            previousMa2BotLowValue = Double.NaN;
            previousMa3TopHighValue = Double.NaN;
            previousMa3BotLowValue = Double.NaN;
            previousMa4TopHighValue = Double.NaN;
            previousMa4BotLowValue = Double.NaN;
            previousMa5TopHighValue = Double.NaN;
            previousMa5BotLowValue = Double.NaN;

            int maMultiplier = use500ms ? 10 : 1;
            int effectiveMa1Length = Math.min(10000, Math.max(2, ma1Length * maMultiplier));
            int effectiveMa2Length = Math.min(10000, Math.max(2, ma2Length * maMultiplier));
            int effectiveMa3Length = Math.min(10000, Math.max(2, ma3Length * maMultiplier));
            int effectiveMa4Length = Math.min(10000, Math.max(2, ma4Length * maMultiplier));
            int effectiveMa5Length = Math.min(99999, Math.max(2, ma5Length * maMultiplier));

            capacity = effectiveMa5Length;
            highPrices = new double[capacity];
            lowPrices = new double[capacity];
            closePrices = new double[capacity];
            volumeValues = new double[capacity];
            cumulativeVolume = new double[capacity];
            cumulativeWgtHigh = new double[capacity];
            cumulativeWgtLow = new double[capacity];

            // Initialize MA objects
            mas = new AVwapMAs[5];
            mas[0] = new AVwapMAs(1, null, effectiveMa1Length, 2.0); // MA1: fixed multiplier
            mas[1] = new AVwapMAs(2, ma2SlopeMetrics, effectiveMa2Length, ma2StdDevMultiplier);
            mas[2] = new AVwapMAs(3, ma3SlopeMetrics, effectiveMa3Length, ma3StdDevMultiplier);
            mas[3] = new AVwapMAs(4, ma4SlopeMetrics, effectiveMa4Length, ma4StdDevMultiplier);
            mas[4] = new AVwapMAs(5, null, effectiveMa5Length, ma5StdDevMultiplier);

            // Set requireSufficientForVisibility for all MAs
            for (AVwapMAs ma : mas) {
                if (ma != null) {
                    ma.setRequireSufficientForVisibility(requireSufficientForVisibility);
                }
            }

            // After creating AVwapMAs[] mas
            if (mas != null) {
                for (AVwapMAs ma : mas) {
                    if (ma != null) {
                        ma.setHealthMonitoringEnabled(enableHealthMonitoring);
                    }
                }
            }

            initializeIndicators();
            stdBands = new BucketedAverageWithSTDBands(stdDevMultiplier, stdBandsWindowSecs);
            // stdBands.setBucketSizeNanos(5_000_000_000L); // Set bucket size to 30 seconds
            upperBandIndicator = registerIndicator("Upper StdDev Band", GraphType.PRIMARY, upperBandColor, 2);
            lowerBandIndicator = registerIndicator("Lower StdDev Band", GraphType.PRIMARY, lowerBandColor, 2);
            stdBandsNormPriceDistIndicator = registerIndicator("Norm Price Dist from Mean (StdBands)", GraphType.BOTTOM,
                    Color.MAGENTA, 2);

            widthMiniAtrBandIndicator = registerIndicator("Width Mini ATR Band", GraphType.BOTTOM, miniAtrWidthColor,
                    2);
            miniAtrTrendIndicator = registerIndicator("Mini ATR Trend Signal", GraphType.BOTTOM, new Color(255, 165, 0),
                    2);
            miniAtrTrendStrengthIndicator = registerIndicator("Mini ATR Trend Strength", GraphType.BOTTOM,
                    new Color(100, 149, 237), 2);
            miniAtrBandsCalculator = new MiniAtrBandsCalculator(miniAtrLength, miniAtrFactor, pips,
                    miniAtrMinWidthPips, miniAtrThresholdBufferPips,
                    miniAtrConfirmationBars, miniAtrTrendStrengthScale,
                    debugLogging);

            // Add indicators for Mini ATR Trading
            miniAtrTradingModeIndicator = registerIndicator("Mini ATR Trading Mode", GraphType.BOTTOM,
                    new Color(128, 0, 128), 2);
            miniAtrTrailingStopIndicator = registerIndicator("Mini ATR Trailing Stop", GraphType.PRIMARY,
                    new Color(255, 20, 147), 2);

            // Register indicators for trailing entry system
            pendingEntryStopIndicator = registerIndicator("Pending Entry Stop", GraphType.PRIMARY,
                    new Color(255, 140, 0), 2); // Orange color
            bestPriceIndicator = registerIndicator("Best Trailing Price", GraphType.PRIMARY, new Color(0, 191, 255), 1); // Deep
                                                                                                                         // sky
                                                                                                                         // blue,
                                                                                                                         // thinner
                                                                                                                         // line

            // Register indicator for trade icons
            tradeIconsIndicator = registerIndicator("Trade Icons", GraphType.PRIMARY, Color.WHITE, 1);

            // Register indicators for pending exit visualization
            pendingExitStopIndicator = registerIndicator("Pending Exit Stop", GraphType.PRIMARY, new Color(255, 69, 0),
                    2); // Orange-red
            bestExitPriceIndicator = registerIndicator("Best Exit Price", GraphType.PRIMARY, new Color(70, 130, 180),
                    1); // Steel blue

            // Initialize Width Reversal Detector if enabled
            if (enableReversalDetector) {
                reversalDetector = new WidthATRReversalDetector(
                        api,
                        widthDetectorLookback,
                        widthDetectorThresholdPct,
                        widthDetectorMinBarsBetween,
                        widthDetectorPriceMovementPct);
                if (!disableAllLogging)
                    Log.info("Initialized Width ATR Reversal Detector with lookback=" + widthDetectorLookback);
            }
            OSCInstanceMain = new OrderSenderControllerV2(api, alias, "Main", maxDailyLoss, positionSize,
                    oscMainRealizedProfitTarget, oscMainTotalProfitTarget, // Use new parameters
                    null, info,
                    oscDdCash, oscDdPct, oscDdUnrCash, oscDdUnrPct, oscDdGivePct, oscProfitEpsilonTicks,
                    oscEnableTotalDDAction, oscEnableUnrealizedDDAction);
            // Ensure OSCInstance is initialized
            OSCInstance = new OrderSenderControllerV2(api, alias, "MiniMinMaxATR", maxDailyLoss, positionSize,
                    oscMiniAtrRealizedProfitTarget, oscMiniAtrTotalProfitTarget, // Use new parameters
                    tradeIconsIndicator, instrumentInfo,
                    oscDdCash, oscDdPct, oscDdUnrCash, oscDdUnrPct, oscDdGivePct, oscProfitEpsilonTicks,
                    oscEnableTotalDDAction, oscEnableUnrealizedDDAction);
            if (!disableAllLogging)
                Log.info("Initialized OrderSenderControllerV2 for live trading with MiniMinMaxATR algorithm");

            // Set initial live trading state
            OSCInstance.setEnableLiveTrading(enableLiveTrading != null && enableLiveTrading);
            OSCInstanceMain.setEnableLiveTrading(enableLiveTrading != null && enableLiveTrading);

            // After creating OSCInstance/OSCInstanceMain
            if (OSCInstance != null) {
                OSCInstance.setHealthMonitoringEnabled(enableHealthMonitoring);
            }
            if (OSCInstanceMain != null) {
                OSCInstanceMain.setHealthMonitoringEnabled(enableHealthMonitoring);
            }

            if (stdBands != null) {
                stdBands.setHealthMonitoringEnabled(enableHealthMonitoring);
            }

            // Initialize OrderSenderControllerV2 for real orders if live trading is enabled
            if (enableLiveTrading) {

                OrderSenderControllerV2.setRealtimeStatus(NowRealtime);

            }

            // Initialize chop zone calculator
            chopZoneCalculator = new ChopZoneCalculatorV2(
                    ma2SlopeThreshold,
                    ma3SlopeThreshold,
                    crossoverLookbackPeriod,
                    minCrossoversForChop,
                    chopConfirmationBars,
                    debugLogging,
                    vwapProximityThresholdPips,
                    pocProximityThresholdPips,
                    vwapPocProximityPercent,
                    priceInVwapPocZoneDurationBars,
                    vwapCrossoverLookbackPeriod,
                    minVwapCrossoversForChop);

            // Explicitly initialize visibility trend bias exit suppression
            useVisibilityTrendBiasExitSuppression = true; // Ensure parameter is set
            visibilityTrendBiasExitSuppressionActive = false; // Start with suppression inactive
            if (!disableAllLogging)
                Log.info("Initialized visibility trend bias exit suppression: parameter="
                        + useVisibilityTrendBiasExitSuppression);

            // Register chop zone indicators
            chopZoneIndicator = registerIndicator("Chop Zone", GraphType.BOTTOM,
                    new Color(255, 165, 0), 3); // Orange, thick line
            chopIntensityIndicator = registerIndicator("Chop Intensity", GraphType.BOTTOM,
                    new Color(255, 69, 0), 1); // Orange-red, thin line

            Color chopBoundColor = new Color(30, 144, 255); // ‑‑ dodger‑blue
            chopZoneUpperLimitIndicator = registerIndicator("Chop Zone Upper", GraphType.PRIMARY, chopBoundColor, 1);
            chopZoneLowerLimitIndicator = registerIndicator("Chop Zone Lower", GraphType.PRIMARY, chopBoundColor, 1);

            // Register MA3 inside MiniATR indicator
            ma3BandsInsideMiniAtrIndicator = registerIndicator("MA3 Bands Inside MiniATR",
                    GraphType.BOTTOM, new Color(255, 20, 147), 2); // Pink color

            // Add this new indicator registration
            maSlopeDataAvailableIndicator = registerIndicator("MA Slope Data Available", GraphType.BOTTOM,
                    new Color(50, 150, 200), 2);

            // Register visibility trend bias indicator
            visibilityTrendBiasIndicator = registerIndicator("Visibility Trend Bias", GraphType.BOTTOM,
                    visibilityTrendBiasColor, 3);

            // Change suppression indicator color from yellow/gold to purple
            visibilityTrendBiasExitSuppressionIndicator = registerIndicator("Visibility Trend Bias Exit Suppression",
                    GraphType.BOTTOM, new Color(128, 0, 128), 3); // Purple

            // Register indicator for MA2 Std Entry Suppression
            ma2StdEntrySuppressionIndicator = registerIndicator("MA2 Std Entry Suppression", GraphType.BOTTOM,
                    new Color(255, 192, 203), 3); // Pink

            // --- Register new indicators for global position and P&L ---
            globalPositionIndicator = registerIndicator("Global Position", GraphType.BOTTOM, new Color(0, 128, 255), 2);
            globalPLIndicator = registerIndicator("Global P&L", GraphType.BOTTOM, new Color(255, 140, 0), 2);

            if (!disableAllLogging)
                Log.info("Initialized with Midas Adaptive Zone Trader, StdDev Bands and Mini ATR Bands");

            // Initialize VWAPCalculatorOptimized with direct std multiplier (no division
            // by
            // 10)
            vwapCalculator = new VWAPCalculatorOptimized(
                    vwapStdDevMultiplier != null ? vwapStdDevMultiplier : 2.0,
                    vwapEmaAlpha != null ? vwapEmaAlpha : 0.2,
                    vwapAtrPeriod != null ? vwapAtrPeriod : 14,
                    vwapBandMultiplier != null ? vwapBandMultiplier : 2.0,
                    vwapSlopeThreshold != null ? vwapSlopeThreshold : 0.0);

            // Register VWAP indicators
            vwapIndicator = registerIndicator("VWAP", GraphType.PRIMARY, Color.WHITE, 4);
            vwapUpperBandIndicator = registerIndicator("VWAP + 2Std", GraphType.PRIMARY, new Color(255, 0, 255), 4); // Bright
                                                                                                                     // pink
            vwapLowerBandIndicator = registerIndicator("VWAP - 2Std", GraphType.PRIMARY, new Color(255, 0, 255), 4); // Bright
                                                                                                                     // pink

            // Add new VWAP indicators
            vwapSlopeIndicator = registerIndicator("VWAP Slope", GraphType.BOTTOM, new Color(0, 200, 255), 2);
            vwapPriceDistIndicator = registerIndicator("VWAP Price Distance", GraphType.BOTTOM, new Color(255, 140, 0),
                    2);
            vwapNormPriceDistIndicator = registerIndicator("VWAP Norm Price Dist", GraphType.BOTTOM,
                    new Color(128, 0, 255), 2);

            // --- Nubia Health Status UI ---
            if (Boolean.TRUE.equals(enableHealthMonitoring)) {
                try {
                    InputDataHealthTrackerV2.refreshUI(); // Added
                } catch (Exception e) {
                    Log.info("Failed to launch/refresh NubiaHealthStatusUI: " + e.getMessage());
                }
            }

            // --- VPA/Session construction ---
            String symbol = this.alias;
            double pipSize = info.pips;
            long tsNanos = currentTimestampNanos; // Will be 0 at init, but SessionManager handles this

            // vpaSessionSchedule = SessionSchedule.defaultForAlias(symbol);
            // vpaHistoryStore = new HistoryStore(symbol, 10);
            vpa = new VolumeProfileAnalyzer();
            vpa.startNewSession(java.time.Instant.EPOCH); // Start at epoch (or use first bar timestamp if available)
            vpaSessionSchedule = null;
            vpaHistoryStore = null;
            vpaSessionManager = null;
            try {
                vpaNotesBroadcaster = new CloudNotesBroadcaster(8088, pipSize, symbol);
            } catch (IOException e) {
                System.err.println("Failed to initialize CloudNotesBroadcaster: " + e.getMessage());
                vpaNotesBroadcaster = null;
            }

            // --- Build Independent Triple Stop Spec ---
            try {
                // Warn Layer: Composite of ATR and VOL_STOP (Average Mode)
                StopSpec indWarnAtrChild = new StopSpec.Builder(Type.ATR)
                        .atr(indTriple_Warn_AtrPeriod, indTriple_Warn_AtrMultiplier).trailing(true)
                        .trailingIncrement(indTriple_TrailingIncrementPips * pips).build();
                StopSpec indWarnVolStopChild = new StopSpec.Builder(Type.VOL_STOP)
                        .atr(indTriple_Warn_VolStopPeriod, indTriple_Warn_VolStopMultiplier).trailing(true)
                        .trailingIncrement(indTriple_TrailingIncrementPips * pips).build();
                StopSpec indWarnSpecComposite = new StopSpec.Builder(Type.COMPOSITE)
                        .composite(Arrays.asList(indWarnAtrChild, indWarnVolStopChild), CompositeMode.AVERAGE)
                        .trailing(true).trailingIncrement(indTriple_TrailingIncrementPips * pips).build();

                // Trigger Layer: Composite (FixedOffset + ATR_HL), mode from parameter
                StopSpec indTrigFixedOffsetChild = new StopSpec.Builder(Type.FIXED_OFFSET)
                        .priceOffset(indTriple_Trig_FixedOffsetPips * pips).trailing(true)
                        .trailingIncrement(indTriple_TrailingIncrementPips * pips).build();
                StopSpec indTrigAtrHlChild = new StopSpec.Builder(Type.ATR_HIGH_LOW)
                        .atrHighLow(indTriple_Trig_AtrHl_AtrPeriod, indTriple_Trig_AtrHl_SmoothPeriod,
                                indTriple_Trig_AtrHl_Multiplier, indTriple_Trig_AtrHl_MinOffsetPips * pips)
                        .trailing(true).trailingIncrement(indTriple_TrailingIncrementPips * pips).build();
                CompositeMode indTrigCompositeMode;
                if (indTriple_Trig_CompositeModeInt == 0)
                    indTrigCompositeMode = CompositeMode.TIGHTEST;
                else if (indTriple_Trig_CompositeModeInt == 1)
                    indTrigCompositeMode = CompositeMode.LOOSEST;
                else
                    indTrigCompositeMode = CompositeMode.AVERAGE;
                StopSpec indTrigSpecComposite = new StopSpec.Builder(Type.COMPOSITE)
                        .composite(Arrays.asList(indTrigFixedOffsetChild, indTrigAtrHlChild), indTrigCompositeMode)
                        .trailing(true).trailingIncrement(indTriple_TrailingIncrementPips * pips).build();

                // Catastrophic Layer: Composite (ATR_HL + FixedPercent), Average Mode
                StopSpec indCatAtrHlChild = new StopSpec.Builder(Type.ATR_HIGH_LOW)
                        .atrHighLow(indTriple_Cat_AtrHl_AtrPeriod, indTriple_Cat_AtrHl_SmoothPeriod,
                                indTriple_Cat_AtrHl_Multiplier, indTriple_Cat_AtrHl_MinOffsetPips * pips)
                        .trailing(true).trailingIncrement(indTriple_TrailingIncrementPips * pips).build();
                StopSpec indCatFixedPercentChild = new StopSpec.Builder(Type.FIXED_PERCENT)
                        .percent(indTriple_Cat_FixedPercentValue).trailing(true)
                        .trailingIncrement(indTriple_TrailingIncrementPips * pips).build();
                StopSpec indCatSpecComposite = new StopSpec.Builder(Type.COMPOSITE)
                        .composite(Arrays.asList(indCatAtrHlChild, indCatFixedPercentChild), CompositeMode.AVERAGE)
                        .trailing(true).trailingIncrement(indTriple_TrailingIncrementPips * pips).build();

                // Final Triple Stop Spec
                this.independentTripleStopSpec = new StopSpec.Builder(Type.TRIPLE_STOP)
                        .triple(indWarnSpecComposite, indTrigSpecComposite, indCatSpecComposite)
                        .trailing(true) // Overall triple stop should also respect trailing
                        .trailingIncrement(indTriple_TrailingIncrementPips * pips)
                        .build();

                Log.info("Built Independent Triple Stop Spec successfully.");

                // --- Build Entry Triple Stop Spec ---
                if (Boolean.TRUE.equals(entryTriple_Enabled)) {
                    try {
                        entryTripleL1Spec = new StopSpec.Builder(Type.FIXED_OFFSET)
                                .priceOffset(entryTriple_L1_OffsetPips * pips).trailing(false).build();

                        StopSpec entryL2Fixed = new StopSpec.Builder(Type.FIXED_OFFSET)
                                .priceOffset(entryTriple_L2_FixedOffsetPips * pips).trailing(false).build();
                        StopSpec entryL2Vol = new StopSpec.Builder(Type.VOL_STOP)
                                .atr(entryTriple_L2_VolStopPeriod, entryTriple_L2_VolStopMultiplier).trailing(false)
                                .build();
                        entryTripleL2Spec = new StopSpec.Builder(Type.COMPOSITE)
                                .composite(Arrays.asList(entryL2Fixed, entryL2Vol), CompositeMode.TIGHTEST)
                                .trailing(false).build();

                        entryTripleL3Spec = new StopSpec.Builder(Type.FIXED_OFFSET)
                                .priceOffset(entryTriple_L3_OffsetPips * pips).trailing(true)
                                .trailingIncrement(entryTriple_L3_TrailingIncrementPips * pips).build();

                        entryTripleStopSpec = new StopSpec.Builder(Type.TRIPLE_STOP)
                                .triple(entryTripleL1Spec, entryTripleL2Spec, entryTripleL3Spec).trailing(false)
                                .build();

                        Log.info("Built Entry Triple Stop Spec successfully.");
                    } catch (Exception e) {
                        Log.error("Failed to build Entry Triple Stop Spec: " + e.getMessage(), e);
                        entryTripleStopSpec = null;
                    }
                }

            } catch (Exception e) {
                Log.error("Failed to build Independent Triple Stop Spec: " + e.getMessage(), e);
                this.independentTripleStopSpec = null; // Ensure it's null if build fails
            }

            try {

                // Layer 1: fixed, non-trailing
                entryTripleL1Spec = new StopSpec.Builder(Type.FIXED_OFFSET)
                        .priceOffset(entryTriple_L1_OffsetPips * pips)
                        .trailing(false)
                        .build();
                // Layer 2: composite of fixed + volstop, non-trailing
                StopSpec entryL2Fixed = new StopSpec.Builder(Type.FIXED_OFFSET)
                        .priceOffset(entryTriple_L2_FixedOffsetPips * pips)
                        .trailing(false)
                        .build();
                StopSpec entryL2Vol = new StopSpec.Builder(Type.VOL_STOP)
                        .atr(entryTriple_L2_VolStopPeriod, entryTriple_L2_VolStopMultiplier)
                        .trailing(false)
                        .build();
                entryTripleL2Spec = new StopSpec.Builder(Type.COMPOSITE)
                        .composite(Arrays.asList(entryL2Fixed, entryL2Vol), CompositeMode.TIGHTEST)
                        .trailing(false)
                        .build();
                // Layer 3: fixed, trailing
                entryTripleL3Spec = new StopSpec.Builder(Type.FIXED_OFFSET)
                        .priceOffset(entryTriple_L3_OffsetPips * pips)
                        .trailing(true)
                        .trailingIncrement(entryTriple_L3_TrailingIncrementPips * pips)
                        .build();
                entryTripleStopSpec = new StopSpec.Builder(Type.TRIPLE_STOP)
                        .triple(entryTripleL1Spec, entryTripleL2Spec, entryTripleL3Spec)
                        .build();

            } catch (Exception e) {
                Log.error("Failed to build Entry  Triple Stop Spec: " + e.getMessage(), e);
                this.independentTripleStopSpec = null; // Ensure it's null if build fails
            }
            // --- Initialize 2s aggregation state ---
            agg2s_startTimeNanos = 0;
            agg2s_lastCompletedBar = null;
            agg2s_barNeedsReset = true;
            // Note: agg2s_bar is final, no need to re-instantiate
            // --- End 2s init ---
        }
        // --- Initialize Profit Target System ---
        if (Boolean.TRUE.equals(enableProfitTargetSystem)) {
            long maxAgeNanos = (long) (profitTargetMaxAgeSeconds * 1_000_000_000L);
            profitTargetGenerator = new DynamicProfitTargetGenerator(
                    mas,
                    pips, // pips is instrumentInfo.pips, used as tickSizeApproximation
                    profitTargetMinDistanceFromMarketPips,
                    profitTargetMaxActiveCount * 2, // Total targets, will be split later if needed
                    maxAgeNanos // Pass max age in nanoseconds
            );
            activePotentialTargets = new ArrayList<>();
            profitTargetSupportIndicators.clear();
            profitTargetResistanceIndicators.clear();
            for (int i = 0; i < profitTargetMaxActiveCount; i++) {
                profitTargetSupportIndicators.add(registerIndicator("Profit Target Support " + (i + 1),
                        GraphType.PRIMARY, profitTargetSupportColor, 2));
                profitTargetResistanceIndicators.add(registerIndicator("Profit Target Resistance " + (i + 1),
                        GraphType.PRIMARY, profitTargetResistanceColor, 2));
            }
        }

        // Initialize the VWAP proximity checker (assuming this part exists)
        int numberOfVwapReferenceValues = 3; // VWAP, Upper, Lower

        this.vwapProximityChecker = new ProximityConditionChecker(
                VWAP_PROXIMITY_LOOKBACK_PERIOD,
                VWAP_PROXIMITY_THRESHOLD_PIPS, // Pass the pips-based threshold directly
                numberOfVwapReferenceValues);

        // Initialize the VPA (POC, VAH, VAL) proximity checker
        int numberOfVpaReferenceValues = 3; // POC, VAH, VAL

        this.vpaProximityChecker = new ProximityConditionChecker(
                VPA_PROXIMITY_LOOKBACK_PERIOD,
                VPA_PROXIMITY_THRESHOLD_PIPS, // Pass the pips-based threshold directly
                numberOfVpaReferenceValues);

    }

    // --- Add the missing initializeIndicators method ---
    private void initializeIndicators() {
        // Register core MA indicators
        int alpha = 192; // 0.75 * 255
        // Register core MA indicators with alpha
        ma1TopHigh = registerIndicator("MA1 Top High", GraphType.PRIMARY,
                new Color(redColor.getRed(), redColor.getGreen(), redColor.getBlue(), alpha), 1);
        ma1BotLow = registerIndicator("MA1 Bot Low", GraphType.PRIMARY,
                new Color(greenColor.getRed(), greenColor.getGreen(), greenColor.getBlue(), alpha), 1);
        ma2TopHigh = registerIndicator("MA2 Top High", GraphType.PRIMARY,
                new Color(redColor.getRed(), redColor.getGreen(), redColor.getBlue(), alpha), 2);
        ma2BotLow = registerIndicator("MA2 Bot Low", GraphType.PRIMARY,
                new Color(greenColor.getRed(), greenColor.getGreen(), greenColor.getBlue(), alpha), 2);
        ma3TopHigh = registerIndicator("MA3 Top High", GraphType.PRIMARY,
                new Color(redColor.getRed(), redColor.getGreen(), redColor.getBlue(), alpha), 4);
        ma3BotLow = registerIndicator("MA3 Bot Low", GraphType.PRIMARY,
                new Color(greenColor.getRed(), greenColor.getGreen(), greenColor.getBlue(), alpha), 4);
        ma4TopHigh = registerIndicator("MA4 Top High", GraphType.PRIMARY,
                new Color(redColor.getRed(), redColor.getGreen(), redColor.getBlue(), alpha), 6);
        ma4BotLow = registerIndicator("MA4 Bot Low", GraphType.PRIMARY,
                new Color(greenColor.getRed(), greenColor.getGreen(), greenColor.getBlue(), alpha), 6);
        ma5TopHigh = registerIndicator("MA5 Top High", GraphType.PRIMARY,
                new Color(redColor.getRed(), redColor.getGreen(), redColor.getBlue(), alpha), 8);
        ma5BotLow = registerIndicator("MA5 Bot Low", GraphType.PRIMARY,
                new Color(greenColor.getRed(), greenColor.getGreen(), greenColor.getBlue(), alpha), 8);

        // Register trading state indicators
        positionIndicator = registerIndicator("Position", GraphType.BOTTOM, positionColor, 2);
        tradePLIndicator = registerIndicator("Trade P/L", GraphType.BOTTOM, tradePLColor, 2);
        NubiaTotalPLIndicator = registerIndicator("Nubia Total P/L", GraphType.BOTTOM, totalPLColor, 2);
        ma3ConfirmationIndicator = registerIndicator("MA3 Confirmation", GraphType.BOTTOM, new Color(255, 105, 180), 3);
        uptrendIndicator = registerIndicator("Uptrend", GraphType.BOTTOM, uptrendColor, 3);
        downtrendIndicator = registerIndicator("Downtrend", GraphType.BOTTOM, downtrendColor, 3);
        strongBullishIndicator = registerIndicator("Strong Bullish", GraphType.BOTTOM, strongBullishColor, 4);
        strongBearishIndicator = registerIndicator("Strong Bearish", GraphType.BOTTOM, strongBearishColor, 4);
        totalTradesIndicator = registerIndicator("Total Trades", GraphType.BOTTOM, new Color(255, 0, 255), 3); // Magenta
                                                                                                               // color

        // Register StdDev/Statistical indicators
        ma2TopStdIndicator = registerIndicator("MA2 Top + 2Std", GraphType.PRIMARY, stdTopColor, 2);
        ma2BotStdIndicator = registerIndicator("MA2 Bot + 2Std", GraphType.PRIMARY, stdBotColor, 2);
        ma3TopStdIndicator = registerIndicator("MA3 Top + 2Std", GraphType.PRIMARY, stdTopColor, 3);
        ma3BotStdIndicator = registerIndicator("MA3 Bot + 2Std", GraphType.PRIMARY, stdBotColor, 3);
        ma4TopStdIndicator = registerIndicator("MA4 Top + 2Std", GraphType.PRIMARY, stdTopColor, 4);
        ma4BotStdIndicator = registerIndicator("MA4 Bot + 2Std", GraphType.PRIMARY, stdBotColor, 4);
        ma5TopStdIndicator = registerIndicator("MA5 Top + 2Std", GraphType.PRIMARY, stdTopColor, 5);
        ma5BotStdIndicator = registerIndicator("MA5 Bot + 2Std", GraphType.PRIMARY, stdBotColor, 5);
        topHighStdMax345Indicator = registerIndicator("TopHighStd Max MA3-5", GraphType.PRIMARY, Color.CYAN, 2);
        topHighStdMin345Indicator = registerIndicator("TopHighStd Min MA3-5", GraphType.PRIMARY, Color.BLUE, 2);
        botLowStdMax345Indicator = registerIndicator("BotLowStd Max MA3-5", GraphType.PRIMARY, Color.MAGENTA, 2);
        botLowStdMin345Indicator = registerIndicator("BotLowStd Min MA3-5", GraphType.PRIMARY, Color.PINK, 2);

        // Register MA3 and MA4 Rejection Opportunity indicators
        ma3RejectionOpportunityIndicator = registerIndicator("MA3 Rejection Opportunity", GraphType.BOTTOM,
                new Color(255, 140, 0), 3); // Orange
        ma4RejectionOpportunityIndicator = registerIndicator("MA4 Rejection Opportunity", GraphType.BOTTOM,
                new Color(255, 165, 0), 3); // Darker Orange

        // Assign indicators to each MA object (core only)
        mas[0].topHighIndicator = ma1TopHigh;
        mas[0].botLowIndicator = ma1BotLow;
        mas[1].topHighIndicator = ma2TopHigh;
        mas[1].botLowIndicator = ma2BotLow;
        mas[2].topHighIndicator = ma3TopHigh;
        mas[2].botLowIndicator = ma3BotLow;
        mas[3].topHighIndicator = ma4TopHigh;
        mas[3].botLowIndicator = ma4BotLow;
        mas[4].topHighIndicator = ma5TopHigh;
        mas[4].botLowIndicator = ma5BotLow;
        mas[1].topHighStdIndicator = ma2TopStdIndicator;
        mas[1].botLowStdIndicator = ma2BotStdIndicator;
        mas[2].topHighStdIndicator = ma3TopStdIndicator;
        mas[2].botLowStdIndicator = ma3BotStdIndicator;
        mas[3].topHighStdIndicator = ma4TopStdIndicator;
        mas[3].botLowStdIndicator = ma4BotStdIndicator;
        mas[4].topHighStdIndicator = ma5TopStdIndicator;
        mas[4].botLowStdIndicator = ma5BotStdIndicator;

        // Only create development indicators if enabled
        if (Boolean.TRUE.equals(enableDevelopmentIndicators)) {
            // Dimmed MA indicators
            ma1TopHighDimmed = registerIndicator("MA1 Top High Dimmed", GraphType.PRIMARY, dimRedColor, 1);
            ma1BotLowDimmed = registerIndicator("MA1 Bot Low Dimmed", GraphType.PRIMARY, dimGreenColor, 1);
            ma2TopHighDimmed = registerIndicator("MA2 Top High Dimmed", GraphType.PRIMARY, dimRedColor, 2);
            ma2BotLowDimmed = registerIndicator("MA2 Bot Low Dimmed", GraphType.PRIMARY, dimGreenColor, 2);
            ma3TopHighDimmed = registerIndicator("MA3 Top High Dimmed", GraphType.PRIMARY, dimRedColor, 4);
            ma3BotLowDimmed = registerIndicator("MA3 Bot Low Dimmed", GraphType.PRIMARY, dimGreenColor, 4);
            ma4TopHighDimmed = registerIndicator("MA4 Top High Dimmed", GraphType.PRIMARY, dimRedColor, 6);
            ma4BotLowDimmed = registerIndicator("MA4 Bot Low Dimmed", GraphType.PRIMARY, dimGreenColor, 6);
            ma5TopHighDimmed = registerIndicator("MA5 Top High Dimmed", GraphType.PRIMARY, dimRedColor, 8);
            ma5BotLowDimmed = registerIndicator("MA5 Bot Low Dimmed", GraphType.PRIMARY, dimGreenColor, 8);

            // MA height indicators
            ma1HeightIndicator = registerIndicator("MA1 Height", GraphType.BOTTOM, ma1HeightColor, 1);
            ma2HeightIndicator = registerIndicator("MA2 Height", GraphType.BOTTOM, ma2HeightColor, 2);
            ma3HeightIndicator = registerIndicator("MA3 Height", GraphType.BOTTOM, ma3HeightColor, 3);
            ma4HeightIndicator = registerIndicator("MA4 Height", GraphType.BOTTOM, ma4HeightColor, 4);
            ma5HeightIndicator = registerIndicator("MA5 Height", GraphType.BOTTOM, new Color(128, 0, 255), 5);

            // Slope/Trend/Confirmation indicators
            maSlopeConfirmationIndicator = registerIndicator("MA Slope Confirmation", GraphType.BOTTOM,
                    new Color(100, 200, 235), 3);
            ma2BotSlopeIndicator = registerIndicator("MA2 Bot Slope", GraphType.BOTTOM, ma2BotSlopeColor, 1);
            ma3BotSlopeIndicator = registerIndicator("MA3 Bot Slope", GraphType.BOTTOM, ma3BotSlopeColor, 1);
            ma4BotSlopeIndicator = registerIndicator("MA4 Bot Slope", GraphType.BOTTOM, ma4BotSlopeColor, 1);
            ma5BotSlopeIndicator = registerIndicator("MA5 Bot Slope", GraphType.BOTTOM, ma5BotSlopeColor, 1);
            ma2TopSlopeIndicator = registerIndicator("MA2 Top Slope", GraphType.BOTTOM, ma2TopSlopeColor, 1);
            ma3TopSlopeIndicator = registerIndicator("MA3 Top Slope", GraphType.BOTTOM, ma3TopSlopeColor, 1);
            ma4TopSlopeIndicator = registerIndicator("MA4 Top Slope", GraphType.BOTTOM, ma4TopSlopeColor, 1);
            ma5TopSlopeIndicator = registerIndicator("MA5 Top Slope", GraphType.BOTTOM, ma5TopSlopeColor, 1);
            ma2SlopeTrendIndicator = registerIndicator("MA2 Slope Trend", GraphType.BOTTOM, ma2SlopeTrendColor, 2);
            ma3SlopeTrendIndicator = registerIndicator("MA3 Slope Trend", GraphType.BOTTOM, ma3SlopeTrendColor, 2);
            ma4SlopeTrendIndicator = registerIndicator("MA4 Slope Trend", GraphType.BOTTOM, ma4SlopeTrendColor, 2);
            ma5SlopeTrendIndicator = registerIndicator("MA5 Slope Trend", GraphType.BOTTOM, ma5SlopeTrendColor, 2);
            ma2SlopeTrendConfirmationIndicator = registerIndicator("MA2 Slope Trend Confirmation", GraphType.BOTTOM,
                    new Color(255, 140, 0), 3);

            // Visibility/Opportunity indicators
            ma3TopVisibleBarsIndicator = registerIndicator("MA3 Top Visible Bars", GraphType.BOTTOM, ma3VisibleColor,
                    1);
            ma3BotVisibleBarsIndicator = registerIndicator("MA3 Bot Visible Bars", GraphType.BOTTOM, ma3VisibleColor,
                    1);
            ma4TopVisibleBarsIndicator = registerIndicator("MA4 Top Visible Bars", GraphType.BOTTOM, ma4VisibleColor,
                    1);
            ma4BotVisibleBarsIndicator = registerIndicator("MA4 Bot Visible Bars", GraphType.BOTTOM, ma4VisibleColor,
                    1);
            ma5TopVisibleBarsIndicator = registerIndicator("MA5 Top Visible Bars", GraphType.BOTTOM, ma5VisibleColor,
                    1);
            ma5BotVisibleBarsIndicator = registerIndicator("MA5 Bot Visible Bars", GraphType.BOTTOM, ma5VisibleColor,
                    1);
            ma3TopInvisibleBarsIndicator = registerIndicator("MA3 Top Invisible Bars", GraphType.BOTTOM,
                    ma3InvisibleColor, 1);
            ma3BotInvisibleBarsIndicator = registerIndicator("MA3 Bot Invisible Bars", GraphType.BOTTOM,
                    ma3InvisibleColor, 1);
            ma4TopInvisibleBarsIndicator = registerIndicator("MA4 Top Invisible Bars", GraphType.BOTTOM,
                    ma4InvisibleColor, 1);
            ma4BotInvisibleBarsIndicator = registerIndicator("MA4 Bot Invisible Bars", GraphType.BOTTOM,
                    ma4InvisibleColor, 1);
            ma5TopInvisibleBarsIndicator = registerIndicator("MA5 Top Invisible Bars", GraphType.BOTTOM,
                    ma5InvisibleColor, 1);
            ma5BotInvisibleBarsIndicator = registerIndicator("MA5 Bot Invisible Bars", GraphType.BOTTOM,
                    ma5InvisibleColor, 1);

            // Market price indicator (development only)
            marketPriceIndicator = registerIndicator("Nubia Market Price", GraphType.PRIMARY, Color.WHITE, 1);
            // ===== Abnormal Market Conditions =====
            if (Boolean.TRUE.equals(enableAbnormalPriceJumpDetection)) {
                abnormalPriceJumpIndicator = registerIndicator(
                        "Abnormal Price Jump",
                        GraphType.BOTTOM,
                        new Color(255, 69, 0), // OrangeRed for visibility
                        2);
            } else {
                abnormalPriceJumpIndicator = null;
            }

            bidAskDiffIndicator = registerIndicator("BidAskDiffIndicator", GraphType.BOTTOM, dimGreenColor, 2);
        }

        if (Boolean.TRUE.equals(enableExperimentalIndicators)) {
            entryTripleL1LongIndicator = registerIndicator("Entry L1 Long", GraphType.PRIMARY, entryL1LongColor, 2);
            entryTripleL1ShortIndicator = registerIndicator("Entry L1 Short", GraphType.PRIMARY, entryL1ShortColor, 2);
            entryTripleL2LongIndicator = registerIndicator("Entry L2 Long", GraphType.PRIMARY, entryL2LongColor, 2);
            entryTripleL2ShortIndicator = registerIndicator("Entry L2 Short", GraphType.PRIMARY, entryL2ShortColor, 2);
            entryTripleL3LongIndicator = registerIndicator("Entry L3 Long", GraphType.PRIMARY, entryL3LongColor, 2);
            entryTripleL3ShortIndicator = registerIndicator("Entry L3 Short", GraphType.PRIMARY, entryL3ShortColor, 2);

            upperMiniAtrBandIndicator = registerIndicator("Upper Mini ATR Band", GraphType.PRIMARY, miniAtrUpperColor,
                    2);
            lowerMiniAtrBandIndicator = registerIndicator("Lower Mini ATR Band", GraphType.PRIMARY, miniAtrLowerColor,
                    2);
            middleMiniAtrBandIndicator = registerIndicator("Middle Mini ATR Band", GraphType.PRIMARY, miniAtrMidColor,
                    2);
            ma1TrendIndicator = registerIndicator("MA1 Trend", GraphType.BOTTOM, trendColor, 1);
            ma2TrendIndicator = registerIndicator("MA2 Trend", GraphType.BOTTOM, trendColor, 2);
            ma3TrendIndicator = registerIndicator("MA3 Trend", GraphType.BOTTOM, trendColor, 4);
            ma4TrendIndicator = registerIndicator("MA4 Trend", GraphType.BOTTOM, trendColor, 6);
            ma5TrendIndicator = registerIndicator("MA5 Trend", GraphType.BOTTOM, trendColor, 8);

            accountPositionIndicator = api.registerIndicator("Account Position", GraphType.BOTTOM);
            accountPositionIndicator.setColor(Color.decode("#1E90FF")); // DodgerBlue
            accountUnrealizedPnlIndicator = api.registerIndicator("Account Unrealized PnL", GraphType.BOTTOM);
            accountUnrealizedPnlIndicator.setColor(Color.decode("#FFD700")); // Gold
            accountRealizedPnlIndicator = api.registerIndicator("Account Realized PnL", GraphType.BOTTOM);
            accountRealizedPnlIndicator.setColor(Color.decode("#32CD32")); // LimeGreen
            accountVolumeIndicator = api.registerIndicator("Account Volume", GraphType.BOTTOM);
            accountVolumeIndicator.setColor(Color.decode("#FF8C00")); // DarkOrange
            accountWorkingBuysIndicator = api.registerIndicator("Account Working Buys", GraphType.BOTTOM);
            accountWorkingBuysIndicator.setColor(Color.decode("#00CED1")); // DarkTurquoise
            accountWorkingSellsIndicator = api.registerIndicator("Account Working Sells", GraphType.BOTTOM);
            accountWorkingSellsIndicator.setColor(Color.decode("#DC143C")); // Crimson
            accountBalanceIndicator = api.registerIndicator("Account Balance", GraphType.BOTTOM);
            accountBalanceIndicator.setColor(Color.decode("#8A2BE2")); // BlueViolet
        }

        // Register VPA indicators
        pocIndicator = registerIndicator("VPA POC", GraphType.PRIMARY, new Color(255, 0, 255), 3); // Magenta, thicker
                                                                                                   // line

        // Register additional VPA indicators
        valIndicator = registerIndicator("VPA Value Area Low", GraphType.PRIMARY, new Color(0, 100, 200), 2); // Blue
        vahIndicator = registerIndicator("VPA Value Area High", GraphType.PRIMARY, new Color(200, 0, 100), 2); // Red-purple

        // Register HVN indicators (distinct colors)
        hvn1Indicator = registerIndicator("VPA HVN 1", GraphType.PRIMARY, new Color(0, 255, 255), 2); // Cyan
        hvn2Indicator = registerIndicator("VPA HVN 2", GraphType.PRIMARY, new Color(30, 180, 180), 2); // Darker cyan
        hvn3Indicator = registerIndicator("VPA HVN 3", GraphType.PRIMARY, new Color(0, 200, 100), 2); // Greenish
        hvn4Indicator = registerIndicator("VPA HVN 4", GraphType.PRIMARY, new Color(0, 128, 128), 2); // Teal
        hvn5Indicator = registerIndicator("VPA HVN 5", GraphType.PRIMARY, new Color(0, 80, 180), 2); // Blue-green

        // Register LVN indicators (distinct colors)
        lvn1Indicator = registerIndicator("VPA LVN 1", GraphType.PRIMARY, new Color(255, 165, 0), 2); // Orange
        lvn2Indicator = registerIndicator("VPA LVN 2", GraphType.PRIMARY, new Color(200, 130, 0), 2); // Darker orange
        lvn3Indicator = registerIndicator("VPA LVN 3", GraphType.PRIMARY, new Color(180, 100, 0), 2); // Brownish
        lvn4Indicator = registerIndicator("VPA LVN 4", GraphType.PRIMARY, new Color(128, 64, 0), 2); // Dark brown
        lvn5Indicator = registerIndicator("VPA LVN 5", GraphType.PRIMARY, new Color(100, 80, 0), 2); // Olive

        // --- Independent Triple Stop Indicators ---

        int indTripleAlpha = (int) Math
                .round(Math
                        .min(100.0, Math.max(0.0, indTriple_StopColorAlphaPercent)) / 100.0 * 255.0);
        independentTripleWarnIndicator = registerIndicator("Ind. Triple: Warn", GraphType.PRIMARY,
                new Color(Color.YELLOW.getRed(), Color.YELLOW.getGreen(), Color.YELLOW.getBlue(), indTripleAlpha), 2);
        independentTripleTriggerIndicator = registerIndicator("Ind. Triple: Trigger", GraphType.PRIMARY,
                new Color(Color.WHITE.getRed(), Color.WHITE.getGreen(), Color.WHITE.getBlue(), indTripleAlpha), 2);
        independentTripleCatIndicator = registerIndicator("Ind. Triple: Cat", GraphType.PRIMARY,
                new Color(209, 0, 209, indTripleAlpha), 4); // Changed color to RGB(209,0,209), width to 4

        TradeEntryReasonIndicator = registerIndicator("Trade Entry Reason", GraphType.BOTTOM, new Color(0, 200, 0), 3); // Green
        TradeExitReasonIndicator = registerIndicator("Trade Exit Reason", GraphType.BOTTOM, new Color(200, 0, 0), 3); // Red
        maProfitProtectorActiveIndicator = registerIndicator("MA Profit Protector Active", GraphType.BOTTOM,
                maProfitProtectorActiveColor, 1);

    }

    private Indicator registerIndicator(String name, GraphType graphType, Color color, int width) {
        Indicator indicator = api.registerIndicator(name, graphType);
        indicator.setColor(color);
        indicator.setWidth(width);
        return indicator;
    }

    /**
     * Calculates a trend bias based on the visibility of top and bottom indicators.
     * The bias ranges from -1.0 (all tops visible, bearish) to 1.0 (all bottoms
     * visible, bullish).
     * Uses visibility information directly from AVwapMAs instances.
     */
    private double calculateVisibilityTrendBias() {
        int visibleTops = 0;
        int visibleBottoms = 0;
        int totalPairs = 0;

        StringBuilder debugInfo = new StringBuilder();
        debugInfo.append("VisibilityTrendBias calculation:\n");

        // Get visibility information directly from AVwapMAs instances
        for (int i = 0; i < mas.length; i++) {
            if (mas[i] != null && mas[i].available) {
                totalPairs++;

                // Get visibility directly from the AVwapMAs class
                boolean topVisible = mas[i].isTopVisible();
                boolean botVisible = mas[i].isBotVisible();

                if (topVisible)
                    visibleTops++;
                if (botVisible)
                    visibleBottoms++;

                debugInfo.append("  MA").append(i + 1).append(": Top=").append(topVisible)
                        .append(", Bot=").append(botVisible).append("\n");
            }
        }

        // Calculate bias: ranges from -1.0 (all tops visible, no bottoms) to 1.0 (all
        // bottoms visible, no tops)
        double biasValue = 0.0;
        if (totalPairs > 0) {
            double topRatio = visibleTops / (double) totalPairs;
            double botRatio = visibleBottoms / (double) totalPairs;
            biasValue = botRatio - topRatio;

            // Scale to make small differences more visible
            biasValue = Math.min(1.0, Math.max(-1.0, biasValue * 2.0));

            debugInfo.append("  Totals: VisibleTops=").append(visibleTops)
                    .append(", VisibleBottoms=").append(visibleBottoms)
                    .append(", TotalPairs=").append(totalPairs)
                    .append(", Bias=").append(biasValue);
        } else {
            debugInfo.append("  No valid MA pairs detected");
        }

        if (debugLogging && !disableAllLogging) {
            Log.info(debugInfo.toString());
        }

        return biasValue;
    }

    @Override
    public long getInterval() {
        synchronized (lock) {
            return use500ms ? Intervals.INTERVAL_500_MILLISECONDS : Intervals.INTERVAL_1_SECOND;
        }
    }

    @Override
    public void onInterval() {

        if (ordersTestUIV3Instance == null) {
            ordersTestUIV3Instance = OrdersTestUIV3.getInstance(instrumentInfo, OSCInstance, null);
                this.setBboAnalysedDataListener(ordersTestUIV3Instance);

        }

        last_balanceInfo = getLast_balanceInfo();
        last_statusInfo = getLast_statusInfo();

        if (last_balanceInfo != null) {
            if (last_balanceInfo.balancesInCurrency != null && !last_balanceInfo.balancesInCurrency.isEmpty()) {
                for (var currencyBalance : last_balanceInfo.balancesInCurrency) {
                    key = last_balanceInfo.accountId + "|" + currencyBalance.netLiquidityValue;
                    LastAccountBalance = currencyBalance.netLiquidityValue;
                    if (key != oldkey) {
                        sendtologDebugUI("TSC Balance available in the loop: " + key + " = " + currencyBalance.balance);
                    }
                    oldkey = key;

                    // accountBalances.put(key, currencyBalance.balance);
                }

            }
        }

        if (last_statusInfo != null) {
            Log.info("TSC Status available: " + last_statusInfo.position + " = " + last_statusInfo.workingBuys);
            Log.info("TSC Status available: " + last_statusInfo);

            LastAccountPosition = last_statusInfo.position;
            LastAccountunrealizedPnl = last_statusInfo.unrealizedPnl;
            LastAccountrealizedPnl = last_statusInfo.realizedPnl;

            LastAccountvolume = last_statusInfo.volume;
            LastAccountworkingBuys = last_statusInfo.workingBuys;
            LastAccountworkingSells = last_statusInfo.workingSells;

        }

        if (accountPositionIndicator != null) {
            accountPositionIndicator.addPoint((double) LastAccountPosition);
        }
        if (accountUnrealizedPnlIndicator != null) {
            accountUnrealizedPnlIndicator.addPoint(LastAccountunrealizedPnl);
        }
        if (accountRealizedPnlIndicator != null) {
            accountRealizedPnlIndicator.addPoint(LastAccountrealizedPnl);
        }
        if (accountVolumeIndicator != null) {
            accountVolumeIndicator.addPoint(LastAccountvolume);
        }
        if (accountWorkingBuysIndicator != null) {
            accountWorkingBuysIndicator.addPoint((double) LastAccountworkingBuys);
        }
        if (accountWorkingSellsIndicator != null) {
            accountWorkingSellsIndicator.addPoint((double) LastAccountworkingSells);
        }
        if (accountBalanceIndicator != null) {
            accountBalanceIndicator.addPoint(LastAccountBalance);
        }

        if (barsSinceSessionStart < 1) {
            return;
        }

        synchronized (lock) {
            intervalCallCounter++;
            if (intervalCallCounter >= 5) {
                intervalCallCounter = 0;
                // Use Objects.equals for safe null comparison
                if (!java.util.Objects.equals(lastEnableHealthMonitoring, enableHealthMonitoring)) {
                    Log.info("[HEALTH_UI_DEBUG] In onInterval - Change detected. lastEnableHealthMonitoring: "
                            + lastEnableHealthMonitoring + ", current enableHealthMonitoring: "
                            + enableHealthMonitoring); // Added Log
                    setEnableHealthMonitoring(enableHealthMonitoring);
                    lastEnableHealthMonitoring = enableHealthMonitoring;
                }
            }

        }

        // --- 2-Second Bar Aggregation Trigger ---
        if (currentTimestampNanos > 0) {
            // Initialize the start time anchor on the first valid timestamp
            if (agg2s_startTimeNanos == 0) {
                agg2s_startTimeNanos = currentTimestampNanos;
                agg2s_barNeedsReset = true; // Ensure bar resets on first trade after this
                if (debugLogging && !disableAllLogging)
                    Log.info("Agg2s (onTimestamp): Initialized startTimeNanos to " + agg2s_startTimeNanos);
            }

            // Check if 2 seconds (2,000,000,000 nanoseconds) have passed
            if (agg2s_startTimeNanos > 0 && currentTimestampNanos >= agg2s_startTimeNanos + 2_000_000_000L) {
                if (debugLogging && !disableAllLogging)
                    Log.info("Agg2s (onTimestamp): 2 seconds passed. Current: " + currentTimestampNanos + ", Start: "
                            + agg2s_startTimeNanos);

                // Check if the bar object actually received any trades (check if open is valid)
                if (!agg2s_barNeedsReset && !Double.isNaN(agg2s_bar.getOpen())) {
                    // Create a NEW Bar instance and copy data
                    Bar completedBar = new Bar();
                    completedBar.setOpen(agg2s_bar.getOpen());
                    completedBar.setHigh(agg2s_bar.getHigh());
                    completedBar.setLow(agg2s_bar.getLow());
                    completedBar.setClose(agg2s_bar.getClose());
                    completedBar.setVolumeBuy(agg2s_bar.getVolumeBuy());
                    completedBar.setVolumeSell(agg2s_bar.getVolumeSell());
                    // Optional: Copy VWAP if potentially needed
                    // completedBar.setVwap(agg2s_bar.getVwap());

                    agg2s_lastCompletedBar = completedBar; // Store the copy
                    if (debugLogging && !disableAllLogging)
                        Log.info("Agg2s (onTimestamp): Copied finalized 2s Bar data. H:" + completedBar.getHigh()
                                + " L:" + completedBar.getLow() + " C:" + completedBar.getClose() + " V:"
                                + completedBar.getVolumeTotal());

                    // --- Independent Triple Stop Visualization ---
                    Position currentCalcPosition = null;
                    if (currentPosition == 1 && entryPrice > 0) {
                        currentCalcPosition = new Position(Position.Side.LONG, entryPrice);
                    } else if (currentPosition == -1 && entryPrice > 0) {
                        currentCalcPosition = new Position(Position.Side.SHORT, entryPrice);
                    }

                    if (currentCalcPosition != null && independentTripleStopSpec != null
                            && independentTripleStopSpec.tripleSpec != null) {

                        double indHysteresisAmount = indTriple_HysteresisPips * pips;

                        // Calculate raw values
                        double rawWarn = TrailingStopControllerV3.getStopPrice(currentCalcPosition, completedBar,
                                independentTripleStopSpec.tripleSpec.warn);
                        double rawTrigger = TrailingStopControllerV3.getStopPrice(currentCalcPosition, completedBar,
                                independentTripleStopSpec); // Use the main spec for trigger
                        double rawCat = TrailingStopControllerV3.getStopPrice(currentCalcPosition, completedBar,
                                independentTripleStopSpec.tripleSpec.catastrophic);

                        // Apply hysteresis and plot Warn
                        double adjustedWarn = rawWarn;
                        if (!Double.isNaN(rawWarn) && !Double.isInfinite(rawWarn)) {
                            adjustedWarn = currentCalcPosition.isLong() ? rawWarn - indHysteresisAmount
                                    : rawWarn + indHysteresisAmount;
                        }
                        if (independentTripleWarnIndicator != null)
                            independentTripleWarnIndicator.addPoint(adjustedWarn);

                        // Apply hysteresis and plot Trigger
                        double adjustedTrigger = rawTrigger;
                        if (!Double.isNaN(rawTrigger) && !Double.isInfinite(rawTrigger)) {
                            adjustedTrigger = currentCalcPosition.isLong() ? rawTrigger - indHysteresisAmount
                                    : rawTrigger + indHysteresisAmount;
                        }
                        if (independentTripleTriggerIndicator != null)
                            independentTripleTriggerIndicator.addPoint(adjustedTrigger);

                        // Apply hysteresis and plot Cat
                        double adjustedCat = rawCat;
                        if (!Double.isNaN(rawCat) && !Double.isInfinite(rawCat)) {
                            adjustedCat = currentCalcPosition.isLong() ? rawCat - indHysteresisAmount
                                    : rawCat + indHysteresisAmount;
                        }
                        if (independentTripleCatIndicator != null)
                            independentTripleCatIndicator.addPoint(adjustedCat);

                        // Store latest MA adjusted stops
                        latestMaAdjustedWarnStop = adjustedWarn;
                        latestMaAdjustedTrigStop = adjustedTrigger;
                        latestMaAdjustedCatStop = adjustedCat;

                    } else {
                        // Plot NaN if no position or spec is not ready
                        if (independentTripleWarnIndicator != null)
                            independentTripleWarnIndicator.addPoint(Double.NaN);
                        if (independentTripleTriggerIndicator != null)
                            independentTripleTriggerIndicator.addPoint(Double.NaN);
                        latestMaAdjustedWarnStop = Double.NaN;
                        latestMaAdjustedTrigStop = Double.NaN;
                        latestMaAdjustedCatStop = Double.NaN;
                        if (independentTripleCatIndicator != null)
                            independentTripleCatIndicator.addPoint(Double.NaN);
                    }
                    // --- End Independent Triple Stop Visualization ---

                } else {
                    agg2s_lastCompletedBar = null; // No trades were added
                    if (debugLogging && !disableAllLogging)
                        Log.info(
                                "Agg2s (onTimestamp): No trades added to agg2s_bar in this interval. Setting completed Bar to null.");
                }

                // Mark the *original* accumulating bar for reset on the next trade
                // and update the time anchor for the next 2-second window
                agg2s_barNeedsReset = true;
                agg2s_startTimeNanos = currentTimestampNanos; // Start next window
                if (debugLogging && !disableAllLogging)
                    Log.info("Agg2s (onTimestamp): Marked agg2s_bar for reset, set next startTimeNanos to "
                            + agg2s_startTimeNanos);

            } // else: not 2 seconds yet, do nothing with agg2s_lastCompletedBar
        } // else: currentTimestampNanos <= 0, do nothing
          // --- End 2-Second Bar Aggregation Trigger ---

        // Update profit targets on interval if enabled
        if (Boolean.TRUE.equals(enableProfitTargetSystem) && profitTargetGenerator != null) {
            double currentPrice = getLastValidPrice(); // You need a method to get the current market price reliably
            if (!Double.isNaN(currentPrice)) {
                updatePotentialTargets(currentPrice);
                updateProfitTargetIndicators();
            }
        }
    }

    // --- Add the missing setter for health monitoring propagation ---
    private void setEnableHealthMonitoring(Boolean enabled) {
        Log.info("[HEALTH_UI_DEBUG] setEnableHealthMonitoring called with: " + enabled); // Added Log
        if (mas != null) {
            for (AVwapMAs ma : mas) {
                if (ma != null) {
                    ma.setHealthMonitoringEnabled(enabled);
                }
            }
        }
        if (OSCInstance != null) {
            OSCInstance.setHealthMonitoringEnabled(enabled);
        }
        if (OSCInstanceMain != null) {
            OSCInstanceMain.setHealthMonitoringEnabled(enabled);
        }
        if (vwapCalculator != null) {
            vwapCalculator.setHealthMonitoringEnabled(enabled);
        }
        if (stdBands != null) {
            stdBands.setHealthMonitoringEnabled(enabled);
        }
        if (miniAtrBandsCalculator != null) {
            miniAtrBandsCalculator.setHealthMonitoringEnabled(enabled);
        }
        if (Boolean.TRUE.equals(enabled)) {
            InputDataHealthTrackerV2.refreshUI(); // Added
        } else {
            NubiaHealthStatusUI.disposeInstance(); // Added
        }
    }

    // Add a placeholder for closing the health UI
    // private void closeHealthStatusUI() { // Removed - Handled by
    // NubiaHealthStatusUI.disposeInstance() or tracker disposal
    // } // Removed

    private boolean validatePriceData(double high, double low, double close) {
        if (Double.isNaN(high) || Double.isInfinite(high) ||
                Double.isNaN(low) || Double.isInfinite(low) ||
                Double.isNaN(close) || Double.isInfinite(close)) {
            if (debugLogging && !disableAllLogging)
                Log.info("Skipping bar with invalid price data: high=" + high + ", low=" + low + ", close=" + close);
            return false;
        }
        return true;
    }

    private int barsSinceSessionStart = 0;

    @Override
    public void onBar(OrderBook orderBook, Bar bar) {

        if (bar == null) {
            return;
        }

        barsSinceSessionStart++;

        synchronized (lock) {
            // Update parameters that were previously in onInterval
            if (miniAtrBandsCalculator != null) {
                miniAtrBandsCalculator.updateParameters(miniAtrLength, miniAtrFactor, miniAtrMinWidthPips,
                        miniAtrThresholdBufferPips, miniAtrConfirmationBars, miniAtrTrendStrengthScale);
                if (debugLogging && !disableAllLogging)
                    Log.info("Updated MiniATR parameters: length=" + miniAtrLength + ", factor=" + miniAtrFactor
                            + ", minWidthPips=" + miniAtrMinWidthPips);
            }

            if (enableReversalDetector && reversalDetector != null) {
                reversalDetector.updateParameters(
                        widthDetectorLookback,
                        widthDetectorThresholdPct,
                        widthDetectorMinBarsBetween,
                        widthDetectorPriceMovementPct);
                if (debugLogging && !disableAllLogging)
                    Log.info("Updated ReversalDetector parameters.");
            }

            // Update chop zone calculator parameters if they changed
            if (chopZoneCalculator != null) {
                chopZoneCalculator.updateParameters(
                        ma2SlopeThreshold,
                        ma3SlopeThreshold,
                        crossoverLookbackPeriod,
                        minCrossoversForChop,
                        chopConfirmationBars);
                if (debugLogging && !disableAllLogging)
                    Log.info("Updated ChopZoneCalculator parameters.");
            }

            // Process bar data
            double high = bar.getHigh();
            double low = bar.getLow();
            double close = bar.getClose();
            updateEntryTripleIndicators(bar);
            double volume = (double) bar.getVolumeTotal();
            double barwvap = bar.getVwap();

            // Apply minimum volume filter
            if (volume < minVolumeFilter) {
                if (debugLogging && !disableAllLogging)
                    Log.info("Skipping bar with volume below threshold: " + volume + " < " + minVolumeFilter);
                return;
            }

            if (!validatePriceData(high, low, close)) {
                // Already logged in validatePriceData
                return;
            }

            // --- Update Proximity Checks for MA Profit Protector Activation ---
            boolean isVwapProximityMet = false;
            if (this.vwapProximityChecker != null && vwapCalculator != null && vwapCalculator.isReady()
                    && bar != null) {
                double currentCloseForVwapCheck = bar.getClose();
                double vwapValue = vwapCalculator.getVWAP();
                double vwapUpperBand = vwapCalculator.getVWAPAdd2Std();
                double vwapLowerBand = vwapCalculator.getVWAPMinus2Std();

                if (!Double.isNaN(vwapValue) && !Double.isNaN(vwapUpperBand) && !Double.isNaN(vwapLowerBand)) {
                    List<Double> vwapReferenceValues = new ArrayList<>();
                    vwapReferenceValues.add(vwapValue);
                    vwapReferenceValues.add(vwapUpperBand);
                    vwapReferenceValues.add(vwapLowerBand);
                    // Call update first, which returns ProximityEventDetails
                    this.vwapProximityChecker.update(currentCloseForVwapCheck,
                            vwapReferenceValues);
                    // Then get the boolean status from isConditionActive()
                    isVwapProximityMet = this.vwapProximityChecker.isConditionActive();

                    // <--- ADD THIS LOGGING BLOCK --- START --->
                    if (true) {
                        sendtologDebugUI(String.format(
                                "VWAP Prox Check: Close=%.5f, VWAP=%.5f, Upper=%.5f, Lower=%.5f, Thresh=%.2f, Lookback=%d, Result=%b",
                                currentCloseForVwapCheck, vwapValue, vwapUpperBand, vwapLowerBand,
                                VWAP_PROXIMITY_THRESHOLD_PIPS, VWAP_PROXIMITY_LOOKBACK_PERIOD, isVwapProximityMet));
                    }
                    // <--- ADD THIS LOGGING BLOCK --- END --->

                } else {
                    if (true) {
                        sendtologDebugUI(alias
                                + ": VWAP values are NaN for proximity check. VWAP proximity considered false for this bar.");
                    }
                    // isVwapProximityMet remains false (its initial value)
                }
            } else {
                if (this.vwapProximityChecker == null && true) {
                    sendtologDebugUI(alias
                            + ": vwapProximityChecker is null, VWAP proximity check skipped and considered false.");
                }
                // isVwapProximityMet remains false
            }

            boolean isVpaProximityMet = false;
            if (this.vpaProximityChecker != null && this.vpa != null && this.vpa.isReady() && bar != null
                    && this.pips > 0) {
                double currentCloseForVpaCheck = bar.getClose();
                VolumeProfileAnalyzer.VPASnapshot vpaSnapshot = this.vpa.getSnapshot(this.pips);

                if (vpaSnapshot != null &&
                        !Double.isNaN(vpaSnapshot.getPocPriceTicks()) &&
                        !Double.isNaN(vpaSnapshot.getValueAreaHighTicks()) &&
                        !Double.isNaN(vpaSnapshot.getValueAreaLowTicks())) {

                    List<Double> vpaReferenceValues = new ArrayList<>();
                    double pocPriceTicks = vpaSnapshot.getPocPriceTicks(); // Store in temp var
                    double vahPriceTicks = vpaSnapshot.getValueAreaHighTicks(); // Store in temp var
                    double valPriceTicks = vpaSnapshot.getValueAreaLowTicks(); // Store in temp var

                    vpaReferenceValues.add(pocPriceTicks);
                    vpaReferenceValues.add(vahPriceTicks);
                    vpaReferenceValues.add(valPriceTicks);
                    // Call update first, which returns ProximityEventDetails
                    this.vpaProximityChecker.update(currentCloseForVpaCheck, vpaReferenceValues);
                    // Then get the boolean status from isConditionActive()
                    isVpaProximityMet = this.vpaProximityChecker.isConditionActive();

                    // <--- ADD THIS LOGGING BLOCK --- START --->
                    if (true) {
                        sendtologDebugUI(String.format(
                                "VPA Prox Check: Close=%.5f, POC=%.5f, VAH=%.5f, VAL=%.5f, Thresh=%.2f, Lookback=%d, Result=%b",
                                currentCloseForVpaCheck, pocPriceTicks, vahPriceTicks, valPriceTicks,
                                VPA_PROXIMITY_THRESHOLD_PIPS, VPA_PROXIMITY_LOOKBACK_PERIOD, isVpaProximityMet));
                    }
                    // <--- ADD THIS LOGGING BLOCK --- END --->
                } else {
                    if (true) {
                        sendtologDebugUI(alias
                                + ": VPA values (POC/VAH/VAL) are NaN or snapshot unavailable for proximity check. VPA proximity considered false for this bar.");
                    }
                    // isVpaProximityMet remains false
                }
            } else {
                if (this.vpaProximityChecker == null && true) {
                    sendtologDebugUI(
                            alias + ": vpaProximityChecker is null, VPA proximity check skipped and considered false.");
                }
                // isVpaProximityMet remains false (e.g. if pips <= 0 or vpa not ready)
            }

            boolean previousActivateMAProfitProtectorState = this.ActivateMAProfitProtector;
            this.ActivateMAProfitProtector = isVwapProximityMet || isVpaProximityMet;

            if (true) {
                if (this.ActivateMAProfitProtector && !previousActivateMAProfitProtectorState) {
                    sendtologDebugUI(alias + ": ActivateMAProfitProtector CHANGED to TRUE. VWAP Met: "
                            + isVwapProximityMet + ", VPA Met: " + isVpaProximityMet);
                } else if (!this.ActivateMAProfitProtector && previousActivateMAProfitProtectorState) {
                    sendtologDebugUI(alias + ": ActivateMAProfitProtector CHANGED to FALSE. VWAP Met: "
                            + isVwapProximityMet + ", VPA Met: " + isVpaProximityMet);
                }
            }
            // --- End of MA Profit Protector Activation Update ---

            if (maProfitProtectorActiveIndicator != null && bar != null) {
                maProfitProtectorActiveIndicator.addPoint(this.ActivateMAProfitProtector ? 1.0 : 0.0);
            }

            updateMaTrailingExit(high, low, close);

            if (volume > 0) {
                OSCInstance.updateMarketPrice(close);
                // Guard marketPriceIndicator usage
                if (Boolean.TRUE.equals(enableDevelopmentIndicators) && marketPriceIndicator != null) {
                    marketPriceIndicator.addPoint(close);
                }
            }

            int currentIndex = head;
            highPrices[currentIndex] = high;
            lowPrices[currentIndex] = low;
            closePrices[currentIndex] = close;
            volumeValues[currentIndex] = volume;

            double lastCumVol = size == 0 ? 0 : cumulativeVolume[(currentIndex - 1 + capacity) % capacity];
            double lastCumWgtHigh = size == 0 ? 0 : cumulativeWgtHigh[(currentIndex - 1 + capacity) % capacity];
            double lastCumWgtLow = size == 0 ? 0 : cumulativeWgtLow[(currentIndex - 1 + capacity) % capacity];

            cumulativeVolume[currentIndex] = lastCumVol + volume;
            cumulativeWgtHigh[currentIndex] = lastCumWgtHigh + volume * high;
            cumulativeWgtLow[currentIndex] = lastCumWgtLow + volume * low;

            if (miniAtrBandsCalculator != null) {
                double prevClose = size > 0 ? closePrices[(currentIndex - 1 + capacity) % capacity] : close;

                // Get MA3 values to pass to the miniAtrBandsCalculator
                // These values will be set by calculateMidasSet later, but we can use the
                // previous values here
                double ma3Top = latestMa3TopHighValue;
                double ma3Bot = latestMa3BotLowValue;

                // Pass MA3 values to the calculator
                miniAtrBandsCalculator.addBar(high, low, prevClose, close, ma3Top, ma3Bot, currentTimestampNanos);
            }

            // Feed data to the WidthATRReversalDetector if enabled
            if (enableReversalDetector && reversalDetector != null && miniAtrBandsCalculator != null
                    && miniAtrBandsCalculator.isReady()) {
                reversalDetector.addData(miniAtrBandsCalculator.getWidth(), close);
                if (debugLogging && !disableAllLogging)
                    Log.info("ReversalDetector fed with width=" + miniAtrBandsCalculator.getWidth() + ", close="
                            + close);
            }

            head = (head + 1) % capacity;
            if (size < capacity)
                size++;

            // --- Increment barCounter after processing valid bar ---
            barCounter++;

            if (size >= 2) {

                double prevClose = size > 0 ? closePrices[(currentIndex - 1 + capacity) % capacity] : close;
                vwapCalculator.update(high, low, close, volume, prevClose, currentTimestampNanos);

                calculateAndUpdateIndicators();

                // Add call to detect MA slope trends after calculating slopes
                updateMASlopeTrends();

                // --- Add MA2 Std Entry Suppression update here ---
                updateMA2StdEntrySuppression(close);

                updateMaTrailingExit(high, low, close);

                tradeLogic(lastTradePrice);
                updateTradingIndicators(lastTradePrice);

            }

            // Feed data to chop zone calculator
            if (enableChopDetection && chopZoneCalculator != null) {
                double atrWidth = 0.0;
                if (miniAtrBandsCalculator != null && miniAtrBandsCalculator.isReady()) {
                    atrWidth = miniAtrBandsCalculator.getWidth();
                }

                chopZoneCalculator.update(

                        latestMa1TopHighValue, latestMa1BotLowValue,
                        latestMa2TopHighValue, latestMa2BotLowValue,
                        mas[1].isTopVisible(), mas[1].isBotVisible(),
                        latestMa3TopHighValue, latestMa3BotLowValue,
                        latestMa4TopHighValue, latestMa4BotLowValue,
                        close, // Current price
                        atrWidth, // ATR band width
                        barwvap, // VWAP
                        vpa.getPocPrice(), // POC
                        pips // Pips
                );

                // Update chop zone indicators
                boolean isChopZone = chopZoneCalculator.isChopZone();
                double chopIntensity = chopZoneCalculator.getChopIntensity();

                chopZoneIndicator.addPoint(isChopZone ? 1.0 : 0.0);
                chopIntensityIndicator.addPoint(chopIntensity);

                if (debugLogging && isChopZone && !disableAllLogging) {
                    Log.info("Chop zone detected: intensity=" + chopIntensity +
                            ", crossovers=" + chopZoneCalculator.getCrossoversCount());
                }
            } else {
                // Add NaN points if chop detection is disabled
                chopZoneIndicator.addPoint(Double.NaN);
                chopIntensityIndicator.addPoint(Double.NaN);
            }

            // The MA3 inside MiniATR indicator is now updated within the
            // miniAtrBandsCalculator logic above

            // --- VPA/Session feed ---
            // Simulate a trade at close price with bar volume (Bookmap bars may aggregate
            // trades)
            // int priceInt = (int)Math.round(bar.getVwap() / pips); // Convert price to
            // integer ticks
            // long size = bar.getVolumeTotal();
            // vpa.onTrade(priceInt, (int) size, currentTimestampNanos);

            // Refresh VPA and broadcast metrics
            vpa.refresh(currentTimestampNanos);
            if (vpaNotesBroadcaster != null) {
                vpaNotesBroadcaster.updateMetrics(vpa, currentTimestampNanos);
            }
            // vpaSessionManager.onTimer(currentTimestampNanos);

            // === Update VPA indicators using the snapshot ===
            if (vpa != null) {
                // Get the snapshot AFTER refreshing VPA
                VolumeProfileAnalyzer.VPASnapshot vpaSnapshot = vpa.getSnapshot(pips);

                // Check if the snapshot data is ready
                if (vpaSnapshot.isReady()) {
                    // Get snapshot values (these are double prices)
                    double poc = vpaSnapshot.getPocPriceTicks();
                    double vah = vpaSnapshot.getValueAreaHighTicks();
                    double val = vpaSnapshot.getValueAreaLowTicks();
                    List<VolumeProfileAnalyzer.VPASnapshot.VolumeNodeSnapshot> hvnNodesSnapshot = vpaSnapshot
                            .getHvnNodes();
                    List<VolumeProfileAnalyzer.VPASnapshot.VolumeNodeSnapshot> lvnNodesSnapshot = vpaSnapshot
                            .getLvnNodes();

                    // Update POC, VAH, VAL indicators
                    pocIndicator.addPoint(Double.isNaN(poc) ? Double.NaN : poc);
                    vahIndicator.addPoint(Double.isNaN(vah) ? Double.NaN : vah);
                    valIndicator.addPoint(Double.isNaN(val) ? Double.NaN : val);

                    // Update HVN/LVN indicators (up to 5)
                    Indicator[] hvnIndicators = { hvn1Indicator, hvn2Indicator, hvn3Indicator, hvn4Indicator,
                            hvn5Indicator };
                    for (int i = 0; i < hvnIndicators.length; i++) {
                        if (hvnIndicators[i] != null) {
                            if (hvnNodesSnapshot != null && hvnNodesSnapshot.size() > i) {
                                // Get the double price from the snapshot node
                                double centerPrice = hvnNodesSnapshot.get(i).getCenterPriceTicks();
                                hvnIndicators[i].addPoint(Double.isNaN(centerPrice) ? Double.NaN : centerPrice);
                            } else {
                                hvnIndicators[i].addPoint(Double.NaN);
                            }
                        }
                    }

                    Indicator[] lvnIndicators = { lvn1Indicator, lvn2Indicator, lvn3Indicator, lvn4Indicator,
                            lvn5Indicator };
                    for (int i = 0; i < lvnIndicators.length; i++) {
                        if (lvnIndicators[i] != null) {
                            if (lvnNodesSnapshot != null && lvnNodesSnapshot.size() > i) {
                                // Get the double price from the snapshot node
                                double centerPrice = lvnNodesSnapshot.get(i).getCenterPriceTicks();
                                lvnIndicators[i].addPoint(Double.isNaN(centerPrice) ? Double.NaN : centerPrice);
                            } else {
                                lvnIndicators[i].addPoint(Double.NaN);
                            }
                        }
                    }
                } else {
                    // Snapshot not ready, add NaN to all VPA indicators
                    pocIndicator.addPoint(Double.NaN);
                    vahIndicator.addPoint(Double.NaN);
                    valIndicator.addPoint(Double.NaN);
                    Indicator[] hvnIndicators = { hvn1Indicator, hvn2Indicator, hvn3Indicator, hvn4Indicator,
                            hvn5Indicator };
                    Indicator[] lvnIndicators = { lvn1Indicator, lvn2Indicator, lvn3Indicator, lvn4Indicator,
                            lvn5Indicator };
                    for (Indicator ind : hvnIndicators)
                        if (ind != null)
                            ind.addPoint(Double.NaN);
                    for (Indicator ind : lvnIndicators)
                        if (ind != null)
                            ind.addPoint(Double.NaN);
                }
            } else {
                // VPA instance itself is null - Add NaN to all VPA indicators
                pocIndicator.addPoint(Double.NaN);
                vahIndicator.addPoint(Double.NaN);
                valIndicator.addPoint(Double.NaN);
                Indicator[] hvnIndicators = { hvn1Indicator, hvn2Indicator, hvn3Indicator, hvn4Indicator,
                        hvn5Indicator };
                Indicator[] lvnIndicators = { lvn1Indicator, lvn2Indicator, lvn3Indicator, lvn4Indicator,
                        lvn5Indicator };
                for (Indicator ind : hvnIndicators)
                    if (ind != null)
                        ind.addPoint(Double.NaN);
                for (Indicator ind : lvnIndicators)
                    if (ind != null)
                        ind.addPoint(Double.NaN);
            }
            // === End VPA indicator update using snapshot ===

            // --- Example: Access previous session stats for strategy logic ---
            // SessionSnap prev = vpaHistoryStore.get(0); // 0 = last completed session
            // if (prev != null) {
            // int yPOC = prev.poc;
            // int yVAH = prev.vah;
            // // Example: Use yPOC/yVAH in your logic, e.g.:
            // // if (Math.abs(priceInt - yPOC) < 5) { ... }
            // }

        }
        // --- Periodic Health UI Update ---
        if (Boolean.TRUE.equals(enableHealthMonitoring)) { // Added
            InputDataHealthTrackerV2.refreshUI(); // Added
        } // Added

    }

    /**
     * Updates the NubiaHealthStatusUI with the latest health/ready state for all
     * tracked components.
     * Only updates the UI if any value has changed since the last update.
     * Thread/EDT safe.
     */
    // private void updateHealthStatusUI() { // Removed - Logic moved to
    // InputDataHealthTrackerV2.refreshUI()
    // } // Removed

    private int findMaxHighIndex(int length) {
        synchronized (lock) {
            if (size == 0)
                return -1;
            int lookbackLength = Math.min(length, size);
            // Cap the search window for very long MAs
            if (lookbackLength > MAX_ANCHOR_SEARCH_WINDOW) {
                lookbackLength = MAX_ANCHOR_SEARCH_WINDOW;
            }
            double maxHigh = Double.NEGATIVE_INFINITY;
            int maxIndex = -1;
            int startPos = (head - 1 + capacity) % capacity;

            int step = (length > SPARSE_ANCHOR_THRESHOLD) ? SPARSE_ANCHOR_STEP : 1;
            for (int i = 0; i < lookbackLength; i += step) {
                int index = (startPos - i + capacity) % capacity;
                if (highPrices[index] > maxHigh) {
                    maxHigh = highPrices[index];
                    maxIndex = index;
                }
            }
            return maxIndex;
        }
    }

    private int findMinLowIndex(int length) {
        synchronized (lock) {
            if (size == 0)
                return -1;
            int lookbackLength = Math.min(length, size);
            // Cap the search window for very long MAs
            if (lookbackLength > MAX_ANCHOR_SEARCH_WINDOW) {
                lookbackLength = MAX_ANCHOR_SEARCH_WINDOW;
            }
            double minLow = Double.POSITIVE_INFINITY;
            int minIndex = -1;
            int startPos = (head - 1 + capacity) % capacity;

            int step = (length > SPARSE_ANCHOR_THRESHOLD) ? SPARSE_ANCHOR_STEP : 1;
            for (int i = 0; i < lookbackLength; i += step) {
                int index = (startPos - i + capacity) % capacity;
                if (lowPrices[index] < minLow) {
                    minLow = lowPrices[index];
                    minIndex = index;
                }
            }
            return minIndex;
        }
    }

    private void calculateAndUpdateIndicators() {
        synchronized (lock) {
            try {
                if (size < 2)
                    return;

                int lastIndex = (head - 1 + capacity) % capacity;
                double currentClose = closePrices[lastIndex];
                double currentLow = lowPrices[lastIndex];
                double currentHigh = highPrices[lastIndex];

                // Calculate MA1
                double ma1Top = calculateVWAP(findMaxHighIndex(ma1Length), ma1Length, true);
                double ma1Bot = calculateVWAP(findMinLowIndex(ma1Length), ma1Length, false);
                mas[0].update(ma1Top, ma1Bot, currentClose, currentTimestampNanos); // pass current price and nanoTime
                int ma1RawTrend = mas[0].detectTrend(trendThreshold);
                mas[0].smoothTrend(ma1RawTrend);
                mas[0].updateIndicators(alwaysShow, currentHigh, currentLow, Double.NaN, Double.NaN, ma1Length,
                        ma1Length, ma2Length, ma3Length, ma4Length, ma5Length);

                // Calculate MA2
                double ma2Top = calculateVWAP(findMaxHighIndex(ma2Length), ma2Length, true);
                double ma2Bot = calculateVWAP(findMinLowIndex(ma2Length), ma2Length, false);
                mas[1].update(ma2Top, ma2Bot, currentClose, currentTimestampNanos);
                int ma2RawTrend = mas[1].detectTrend(trendThreshold);
                mas[1].smoothTrend(ma2RawTrend);
                mas[1].updateIndicators(alwaysShow, currentHigh, currentLow, mas[0].topHighValue, mas[0].botLowValue,
                        ma2Length, ma1Length, ma2Length, ma3Length, ma4Length, ma5Length);

                // Calculate MA3
                double ma3Top = calculateVWAP(findMaxHighIndex(ma3Length), ma3Length, true);
                double ma3Bot = calculateVWAP(findMinLowIndex(ma3Length), ma3Length, false);
                mas[2].update(ma3Top, ma3Bot, currentClose, currentTimestampNanos);
                int ma3RawTrend = mas[2].detectTrend(trendThreshold);
                mas[2].smoothTrend(ma3RawTrend);
                mas[2].updateIndicators(alwaysShow, currentHigh, currentLow, mas[1].topHighValue, mas[1].botLowValue,
                        ma3Length, ma1Length, ma2Length, ma3Length, ma4Length, ma5Length);

                // Calculate MA4
                double ma4Top = calculateVWAP(findMaxHighIndex(ma4Length), ma4Length, true);
                double ma4Bot = calculateVWAP(findMinLowIndex(ma4Length), ma4Length, false);
                mas[3].update(ma4Top, ma4Bot, currentClose, currentTimestampNanos);
                int ma4RawTrend = mas[3].detectTrend(trendThreshold);
                mas[3].smoothTrend(ma4RawTrend);
                mas[3].updateIndicators(alwaysShow, currentHigh, currentLow, mas[2].topHighValue, mas[2].botLowValue,
                        ma4Length, ma1Length, ma2Length, ma3Length, ma4Length, ma5Length);

                // Calculate MA5
                double ma5Top = calculateVWAP(findMaxHighIndex(ma5Length), ma5Length, true);
                double ma5Bot = calculateVWAP(findMinLowIndex(ma5Length), ma5Length, false);
                mas[4].update(ma5Top, ma5Bot, currentClose, currentTimestampNanos);
                int ma5RawTrend = mas[4].detectTrend(trendThreshold);
                mas[4].smoothTrend(ma5RawTrend);
                mas[4].updateIndicators(alwaysShow, currentHigh, currentLow, mas[3].topHighValue, mas[3].botLowValue,
                        ma5Length, ma1Length, ma2Length, ma3Length, ma4Length, ma5Length);

                // Removed redundant direct slope calculations and external MASlopeMetrics
                // updates here

                // Only log in debug mode, and use debug level
                if (debugLogging && !disableAllLogging) {
                    Log.info("MA Raw Slopes (from AVwapMAs): MA2 Bot=" + mas[1].botSlope + " Top=" + mas[1].topSlope +
                            ", MA3 Bot=" + mas[2].botSlope + " Top=" + mas[2].topSlope +
                            ", MA4 Bot=" + mas[3].botSlope + " Top=" + mas[3].topSlope);
                    Log.info("MA Availability (from AVwapMAs): MA2=" + mas[1].available +
                            ", MA3=" + mas[2].available +
                            ", MA4=" + mas[3].available + ", MA5=" + mas[4].available);
                }

                // Amplify slopes by factor of 1000 for better visibility (they're usually very
                // small)
                double amplificationFactor = 1000.0;
                double upperClip = 1001.0;
                double lowerClip = -1001.0;

                // Add points to MA slope indicators using values from AVwapMAs objects
                // Use new encapsulated methods for clipped slopes
                if (Boolean.TRUE.equals(enableDevelopmentIndicators)) {
                    if (ma2BotSlopeIndicator != null)
                        ma2BotSlopeIndicator
                                .addPoint(mas[1].getClippedBotSlope(amplificationFactor, lowerClip, upperClip, pips));
                    if (ma3BotSlopeIndicator != null)
                        ma3BotSlopeIndicator
                                .addPoint(mas[2].getClippedBotSlope(amplificationFactor, lowerClip, upperClip, pips));
                    if (ma4BotSlopeIndicator != null)
                        ma4BotSlopeIndicator
                                .addPoint(mas[3].getClippedBotSlope(amplificationFactor, lowerClip, upperClip, pips));
                    if (ma5BotSlopeIndicator != null)
                        ma5BotSlopeIndicator
                                .addPoint(mas[4].getClippedBotSlope(amplificationFactor, lowerClip, upperClip, pips));
                    if (ma2TopSlopeIndicator != null)
                        ma2TopSlopeIndicator
                                .addPoint(mas[1].getClippedTopSlope(amplificationFactor, lowerClip, upperClip, pips));
                    if (ma3TopSlopeIndicator != null)
                        ma3TopSlopeIndicator
                                .addPoint(mas[2].getClippedTopSlope(amplificationFactor, lowerClip, upperClip, pips));
                    if (ma4TopSlopeIndicator != null)
                        ma4TopSlopeIndicator
                                .addPoint(mas[3].getClippedTopSlope(amplificationFactor, lowerClip, upperClip, pips));
                    if (ma5TopSlopeIndicator != null)
                        ma5TopSlopeIndicator
                                .addPoint(mas[4].getClippedTopSlope(amplificationFactor, lowerClip, upperClip, pips));
                }

                // Update data availability indicator based on AVwapMAs availability flags
                boolean enoughDataForSlopes = mas[1].available || mas[2].available || mas[3].available
                        || mas[4].available;
                if (maSlopeDataAvailableIndicator != null) {
                    maSlopeDataAvailableIndicator.addPoint(enoughDataForSlopes ? 1.0 : 0.0);
                }

                // redundant - Call the detector for slope trends (this will now use the
                // internal metrics)
                // updateMASlopeTrends();

                // Calculate and update the visibility trend bias
                visibilityTrendBiasValue = calculateVisibilityTrendBias();
                if (visibilityTrendBiasIndicator != null) {
                    visibilityTrendBiasIndicator.addPoint(visibilityTrendBiasValue);
                }

                if (debugLogging && !disableAllLogging) {
                    Log.info("Visibility Trend Bias: " + visibilityTrendBiasValue);
                }

                // --- Mini ATR band indicator updates centralized here ---
                if (miniAtrBandsCalculator != null) {
                    if (miniAtrBandsCalculator.isReady()) {
                        if (upperMiniAtrBandIndicator != null)
                            upperMiniAtrBandIndicator.addPoint(miniAtrBandsCalculator.getUpperBand());
                        if (lowerMiniAtrBandIndicator != null)
                            lowerMiniAtrBandIndicator.addPoint(miniAtrBandsCalculator.getLowerBand());
                        if (middleMiniAtrBandIndicator != null)
                            middleMiniAtrBandIndicator.addPoint(miniAtrBandsCalculator.getMiddleBand());
                        if (widthMiniAtrBandIndicator != null)
                            widthMiniAtrBandIndicator.addPoint(miniAtrBandsCalculator.getWidth());
                        if (miniAtrTrendIndicator != null)
                            miniAtrTrendIndicator.addPoint(miniAtrBandsCalculator.getCurrentTrend());
                        if (miniAtrTrendStrengthIndicator != null)
                            miniAtrTrendStrengthIndicator.addPoint(miniAtrBandsCalculator.getTrendStrength());
                        if (ma3BandsInsideMiniAtrIndicator != null)
                            ma3BandsInsideMiniAtrIndicator
                                    .addPoint(miniAtrBandsCalculator.isMA3InsideMiniATR() ? 1.0 : 0.0);
                    } else {
                        if (upperMiniAtrBandIndicator != null)
                            upperMiniAtrBandIndicator.addPoint(Double.NaN);
                        if (lowerMiniAtrBandIndicator != null)
                            lowerMiniAtrBandIndicator.addPoint(Double.NaN);
                        if (middleMiniAtrBandIndicator != null)
                            middleMiniAtrBandIndicator.addPoint(Double.NaN);
                        if (widthMiniAtrBandIndicator != null)
                            widthMiniAtrBandIndicator.addPoint(Double.NaN);
                        if (miniAtrTrendIndicator != null)
                            miniAtrTrendIndicator.addPoint(Double.NaN);
                        if (miniAtrTrendStrengthIndicator != null)
                            miniAtrTrendStrengthIndicator.addPoint(Double.NaN);
                        if (ma3BandsInsideMiniAtrIndicator != null)
                            ma3BandsInsideMiniAtrIndicator.addPoint(Double.NaN);
                    }
                }
                // --- End Mini ATR band indicator updates ---

                // --- StdDev Bands update centralized here ---
                if (receivedFirstTimestamp) {
                    try {
                        stdBands.updatePrice(currentClose, currentTimestampNanos);
                        // Could use 'var' here (Java 10+), but explicit type for clarity:
                        BucketedAverageWithSTDBands.SignalSnapshot stdSnap = stdBands.getSignalSnapshot();
                        if (stdSnap.isReady()) {
                            if (upperBandIndicator != null)
                                upperBandIndicator.addPoint(stdSnap.getUpperBand());
                            if (lowerBandIndicator != null)
                                lowerBandIndicator.addPoint(stdSnap.getLowerBand());
                            if (stdBandsNormPriceDistIndicator != null)
                                stdBandsNormPriceDistIndicator.addPoint(stdSnap.getNormalizedPriceDistanceFromMean());
                        } else {
                            if (upperBandIndicator != null)
                                upperBandIndicator.addPoint(Double.NaN);
                            if (lowerBandIndicator != null)
                                lowerBandIndicator.addPoint(Double.NaN);
                            if (stdBandsNormPriceDistIndicator != null)
                                stdBandsNormPriceDistIndicator.addPoint(Double.NaN);
                        }
                    } catch (Exception e) {
                        if (!disableAllLogging)
                            Log.error("Error updating StdDev Bands: " + e.getMessage(), e);
                        if (upperBandIndicator != null)
                            upperBandIndicator.addPoint(Double.NaN);
                        if (lowerBandIndicator != null)
                            lowerBandIndicator.addPoint(Double.NaN);
                        if (stdBandsNormPriceDistIndicator != null)
                            stdBandsNormPriceDistIndicator.addPoint(Double.NaN);
                    }
                }
                // --- End StdDev Bands update ---

                // --- Compute min/max std values across MA3, MA4, MA5 ---
                double[] topHighStdVals = {
                        mas[2].getLastTopHighStdValue(),
                        mas[3].getLastTopHighStdValue(),
                        mas[4].getLastTopHighStdValue()
                };
                double[] botLowStdVals = {
                        mas[2].getLastBotLowStdValue(),
                        mas[3].getLastBotLowStdValue(),
                        mas[4].getLastBotLowStdValue()
                };

                // Reset min/max accumulators for the current calculation cycle
                topHighStdMax345 = Double.NaN;
                topHighStdMin345 = Double.NaN;
                botLowStdMax345 = Double.NaN;
                botLowStdMin345 = Double.NaN;

                // Update max/min for topHighStd (ignore NaN)
                for (double v : topHighStdVals) {
                    if (!Double.isNaN(v)) {
                        if (Double.isNaN(topHighStdMax345) || v > topHighStdMax345)
                            topHighStdMax345 = v;
                        if (Double.isNaN(topHighStdMin345) || v < topHighStdMin345)
                            topHighStdMin345 = v;
                    }
                }
                // Update max/min for botLowStd (ignore NaN)
                for (double v : botLowStdVals) {
                    if (!Double.isNaN(v)) {
                        if (Double.isNaN(botLowStdMax345) || v > botLowStdMax345)
                            botLowStdMax345 = v;
                        if (Double.isNaN(botLowStdMin345) || v < botLowStdMin345)
                            botLowStdMin345 = v;
                    }
                }

                // Add points to the new indicators
                if (topHighStdMax345Indicator != null)
                    topHighStdMax345Indicator.addPoint(topHighStdMax345);
                if (topHighStdMin345Indicator != null)
                    topHighStdMin345Indicator.addPoint(topHighStdMin345);
                if (botLowStdMax345Indicator != null)
                    botLowStdMax345Indicator.addPoint(botLowStdMax345);
                if (botLowStdMin345Indicator != null)
                    botLowStdMin345Indicator.addPoint(botLowStdMin345);

                // Add call to update visibility indicators
                updateVisibilityIndicators();

                // --- VWAP indicators update ---
                VWAPCalculatorOptimized.VWAPSignalSnapshot snap = vwapCalculator
                        .getSignalSnapshot(closePrices[(head - 1 + capacity) % capacity]);
                if (snap.isReady()) {
                    if (vwapIndicator != null)
                        vwapIndicator.addPoint(Double.isFinite(snap.getVwap()) ? snap.getVwap() : Double.NaN);
                    if (vwapUpperBandIndicator != null)
                        vwapUpperBandIndicator
                                .addPoint(Double.isFinite(snap.getVwapAdd2Std()) ? snap.getVwapAdd2Std() : Double.NaN);
                    if (vwapLowerBandIndicator != null)
                        vwapLowerBandIndicator
                                .addPoint(Double.isFinite(snap.getVwapMinus2Std()) ? snap.getVwapMinus2Std()
                                        : Double.NaN);
                    if (vwapSlopeIndicator != null)
                        vwapSlopeIndicator
                                .addPoint(Double.isFinite(snap.getVwapSlope()) ? snap.getVwapSlope() : Double.NaN);
                    if (vwapPriceDistIndicator != null)
                        vwapPriceDistIndicator
                                .addPoint(Double.isFinite(snap.getPriceDistanceFromVwap())
                                        ? snap.getPriceDistanceFromVwap()
                                        : Double.NaN);
                    if (vwapNormPriceDistIndicator != null)
                        vwapNormPriceDistIndicator.addPoint(Double.isFinite(snap.getNormalizedPriceDistanceFromVwap())
                                ? snap.getNormalizedPriceDistanceFromVwap()
                                : Double.NaN);
                } else {
                    if (vwapIndicator != null)
                        vwapIndicator.addPoint(Double.NaN);
                    if (vwapUpperBandIndicator != null)
                        vwapUpperBandIndicator.addPoint(Double.NaN);
                    if (vwapLowerBandIndicator != null)
                        vwapLowerBandIndicator.addPoint(Double.NaN);
                    if (vwapSlopeIndicator != null)
                        vwapSlopeIndicator.addPoint(Double.NaN);
                    if (vwapPriceDistIndicator != null)
                        vwapPriceDistIndicator.addPoint(Double.NaN);
                    if (vwapNormPriceDistIndicator != null)
                        vwapNormPriceDistIndicator.addPoint(Double.NaN);
                }

                // --- Chop Zone Boundary Indicators Update ---
                if (chopZoneCalculator != null) {
                    double chopZoneUpperLimit = chopZoneCalculator.getChopZoneUpperLimit();
                    double chopZoneLowerLimit = chopZoneCalculator.getChopZoneLowerLimit();
                    if (chopZoneUpperLimitIndicator != null) {
                        if (!Double.isNaN(chopZoneUpperLimit)) {
                            chopZoneUpperLimitIndicator.addPoint(chopZoneUpperLimit);
                        } else {
                            chopZoneUpperLimitIndicator.addPoint(Double.NaN);
                        }
                    }
                    if (chopZoneLowerLimitIndicator != null) {
                        if (!Double.isNaN(chopZoneLowerLimit)) {
                            chopZoneLowerLimitIndicator.addPoint(chopZoneLowerLimit);
                        } else {
                            chopZoneLowerLimitIndicator.addPoint(Double.NaN);
                        }
                    }
                }

            } catch (Exception e) {
                if (!disableAllLogging)
                    Log.error("Error in calculateAndUpdateIndicators: " + e.getMessage(), e);
            }
        }
    }

    private double calculateVWAP(int anchorIndex, int length, boolean isHigh) {
        synchronized (lock) {
            try {
                if (anchorIndex < 0 || anchorIndex >= size) {
                    if (debugLogging && !disableAllLogging)
                        Log.info("Invalid anchor index: " + anchorIndex + " (size: " + size + ")");
                    return getLatestValue(length, isHigh);
                }

                int endIndex = (head - 1 + capacity) % capacity;
                double cumVolEnd = cumulativeVolume[endIndex];
                double cumWgtValueEnd = isHigh ? cumulativeWgtHigh[endIndex] : cumulativeWgtLow[endIndex];
                double cumVolBeforeStart = (anchorIndex == 0) ? 0
                        : cumulativeVolume[(anchorIndex - 1 + capacity) % capacity];
                double cumWgtValueBeforeStart = (anchorIndex == 0) ? 0
                        : (isHigh ? cumulativeWgtHigh[(anchorIndex - 1 + capacity) % capacity]
                                : cumulativeWgtLow[(anchorIndex - 1 + capacity) % capacity]);

                double sumVol = cumVolEnd - cumVolBeforeStart;
                if (sumVol <= 0) {
                    if (debugLogging)
                        Log.info("Zero or negative volume in VWAP: sumVol=" + sumVol);
                    return getLatestValue(length, isHigh);
                }

                double sumWgtValue = cumWgtValueEnd - cumWgtValueBeforeStart;
                double valueVwap = sumWgtValue / sumVol;

                if (Double.isNaN(valueVwap) || Double.isInfinite(valueVwap)) {
                    if (debugLogging)
                        Log.info("Invalid VWAP result: " + valueVwap);
                    return getLatestValue(length, isHigh);
                }
                return valueVwap;
            } catch (Exception e) {
                Log.error("Error in calculateVwap: " + e.getMessage(), e);
                return getLatestValue(length, isHigh);
            }
        }
    }

    private double getLatestValue(int length, boolean isHigh) {
        synchronized (lock) {
            if (isHigh) {
                return length == ma1Length ? latestMa1TopHighValue
                        : length == ma2Length ? latestMa2TopHighValue
                                : length == ma3Length ? latestMa3TopHighValue
                                        : length == ma4Length ? latestMa4TopHighValue
                                                : length == ma5Length ? latestMa5TopHighValue : Double.NaN;
            } else {
                return length == ma1Length ? latestMa1BotLowValue
                        : length == ma2Length ? latestMa2BotLowValue
                                : length == ma3Length ? latestMa3BotLowValue
                                        : length == ma4Length ? latestMa4BotLowValue
                                                : length == ma5Length ? latestMa5BotLowValue : Double.NaN;
            }
        }
    }

    private double calculateATR(int lookback) {
        synchronized (lock) {
            if (size < lookback + 1) {
                if (debugLogging)
                    Log.info("Not enough data to calculate ATR. Size: " + size + ", lookback: " + lookback);
                return 0.0;
            }
            double sumTR = 0.0;
            for (int i = 1; i <= lookback; i++) {
                int currentIndex = (head - i + capacity) % capacity;
                int prevIndex = (head - i - 1 + capacity) % capacity;
                double high = highPrices[currentIndex];
                double low = lowPrices[currentIndex];
                double prevClose = closePrices[prevIndex];
                double tr = Math.max(high - low, Math.max(Math.abs(high - prevClose), Math.abs(low - prevClose)));
                sumTR += tr;
            }
            return sumTR / lookback;
        }
    }

    private void tradeLogic(double currentPrice) {

        /*
         * TradeReasonIndicator Codes:
         * -----------------------------------------------------------------------------
         * -------------------------
         * Entries (Positive Codes):
         * 1: MA Rejection Long Entry (tradeLogic -> checkMARejectionOpportunity)
         * 2: MA Rejection Short Entry (tradeLogic -> checkMARejectionOpportunity)
         * 3: MA Pending Long Triggered (executeTradeEntries -> MA Pending Entry Setup
         * -> Triggered)
         * 4: MA Pending Short Triggered (executeTradeEntries -> MA Pending Entry Setup
         * -> Triggered)
         * 5: Standard MA Long Entry - Breakout Style 1 (vs MA3/MA2/MA1)
         * (executeTradeEntries -> Direct Entry)
         * 6: Standard MA Short Entry - Breakout Style 1 (vs MA1/MA2)
         * (executeTradeEntries -> Direct Entry)
         * 7: Standard MA Long Entry - Pullback to MA1 w/ ATR Stop (executeTradeEntries
         * -> Direct Entry)
         * 8: Standard MA Long Entry - Trend Following Pullback to MA1 w/ Fixed Stop
         * (executeTradeEntries -> Direct Entry)
         * 9: Standard MA Short Entry - Pullback to MA1 w/ ATR Stop (executeTradeEntries
         * -> Direct Entry)
         * 10: Standard MA Short Entry - Trend Following Pullback to MA1 w/ Fixed Stop
         * (executeTradeEntries -> Direct Entry)
         * 11: Standard MA Short Entry - Re-entry after Exit Pullback
         * (executeTradeEntries -> Direct Entry)
         * 13: MA Pending Long Triggered (Gen. Setup*) (executeTradeEntries -> MA
         * Pending Entry Setup -> Triggered)
         * 14: MA Pending Short Triggered (Gen. Setup*) (executeTradeEntries -> MA
         * Pending Entry Setup -> Triggered)
         * 15: Entry Triple Long Entry (tradeLogic -> Entry Triple Stop Logic)
         * 16: Entry Triple Short Entry (tradeLogic -> Entry Triple Stop Logic)
         * 
         * Note: Codes 13/14 indicate a pending entry was triggered, using the reason
         * code assigned during setup.
         * If setup logic becomes more complex, these codes would represent the specific
         * setup reason.
         * 
         * Exits (Negative Codes):
         * -1: Target Hit (MA or Non-MA)
         * -2: Standard Stop / MA Trigger Stop Hit
         * -3: Catastrophic Stop Hit (MA or Non-MA)
         * -4: MA Rejection Exit (before reversal)
         * -5: MA1 Profit Long Exit
         * -6: MA1 Profit Short Exit
         * -99: Unknown/Other Exit Reason
         * 
         * Indicator Behavior when Flat:
         * 0: No active trade / No specific reason plotted
         ** 
         * IMPORTANT: Maintain reason code short versions in playTradeSound method when
         * this list is updated.
         * -----------------------------------------------------------------------------
         * -------------------------
         */

        synchronized (lock) {
            // --- MA Rejection Opportunity Logic ---
            double hysteresis = maOpportunityHysteresisPips * pips;
            int prevBarIdx = (head - 2 + capacity) % capacity; // Check if size > 1 before accessing? Assumed ok for
                                                               // now.
            double prevClose = closePrices[prevBarIdx];
            int triggeredDirection = 0; // 1=long, -1=short

            boolean ma3Checked = false, ma4Checked = false;
            boolean ma3Sufficient = false, ma4Sufficient = false;
            boolean ma3BotVisible = false, ma3TopVisible = false;
            boolean ma4BotVisible = false, ma4TopVisible = false;
            boolean triggeredFromMA3 = false, triggeredFromMA4 = false;

            // MA3 rejection logic
            if (enableMA3OpportunityEntry != null && enableMA3OpportunityEntry) {
                ma3Checked = true;
                if (mas[2] != null) { // Null check for safety
                    ma3Sufficient = mas[2].isSufficient();
                    ma3BotVisible = mas[2].isBotVisible();
                    ma3TopVisible = mas[2].isTopVisible();
                    if (ma3Sufficient) {
                        Log.info("[MA REJECTION DEBUG] Calling checkMARejectionOpportunity for MA3 with prevClose="
                                + prevClose + ", currClose=" + currentPrice + ", barCounter=" + barCounter
                                + ", hysteresis="
                                + hysteresis + ", lastCross=" + ma3LastCross[0] + "," + ma3LastCross[1]);
                        double vwap = vwapCalculator != null ? vwapCalculator.getVWAP() : Double.NaN;
                        triggeredDirection = checkMARejectionOpportunity(
                                mas[2], prevClose, currentPrice, barCounter, hysteresis, ma3LastCross,
                                maOpportunityLookbackBars, vwap);
                        if (triggeredDirection != 0) {
                            triggeredFromMA3 = true;
                        }
                        Log.info("[MA REJECTION DEBUG] MA3 check result: triggeredDirection=" + triggeredDirection
                                + ", lastCross=" + ma3LastCross[0] + "," + ma3LastCross[1]);
                    }
                }
            }
            // MA4 rejection logic (only if MA3 did not trigger)
            if (triggeredDirection == 0 && enableMA4OpportunityEntry != null && enableMA4OpportunityEntry) {
                ma4Checked = true;
                if (mas[3] != null) { // Null check
                    ma4Sufficient = mas[3].isSufficient();
                    ma4BotVisible = mas[3].isBotVisible();
                    ma4TopVisible = mas[3].isTopVisible();
                    if (ma4Sufficient) {
                        Log.info("[MA REJECTION DEBUG] Calling checkMARejectionOpportunity for MA4 with prevClose="
                                + prevClose + ", currClose=" + currentPrice + ", barCounter=" + barCounter
                                + ", hysteresis="
                                + hysteresis + ", lastCross=" + ma4LastCross[0] + "," + ma4LastCross[1]);
                        double vwap = vwapCalculator != null ? vwapCalculator.getVWAP() : Double.NaN;
                        triggeredDirection = checkMARejectionOpportunity(
                                mas[3], prevClose, currentPrice, barCounter, hysteresis, ma4LastCross,
                                maOpportunityLookbackBars, vwap);
                        if (triggeredDirection != 0) {
                            triggeredFromMA4 = true;
                        }
                        Log.info("[MA REJECTION DEBUG] MA4 check result: triggeredDirection=" + triggeredDirection
                                + ", lastCross=" + ma4LastCross[0] + "," + ma4LastCross[1]);
                    }
                }
            }

            // If an opportunity entry is triggered
            if (triggeredDirection != 0) {
                // MA2 Std Dev Suppression Check
                if ((triggeredDirection == 1 && ma2StdEntrySuppressionDirection == 1) ||
                        (triggeredDirection == -1 && ma2StdEntrySuppressionDirection == -1)) {
                    if (debugLogging && !disableAllLogging) {
                        Log.info("MA2 Std Entry Suppression: Blocking MA Rejection " +
                                (triggeredDirection == 1 ? "LONG" : "SHORT") + " entry at price " + currentPrice);
                    }
                    // Do not proceed with entry if suppressed
                } else {
                    // Proceed with entry/reversal
                    double atr = calculateATR(14); // Calculate ATR if needed for stop/target (currently fixed)
                    if (triggeredFromMA3) {
                        ma3RejectionOpportunityCountdown = 100;
                    }
                    if (triggeredFromMA4) {
                        ma4RejectionOpportunityCountdown = 100;
                    }
                    if ((currentPosition == 1 && triggeredDirection == -1) ||
                            (currentPosition == -1 && triggeredDirection == 1)) {
                        // Reverse existing position
                        double tradePL = (currentPosition == 1)
                                ? (currentPrice - entryPrice) * 50 * currentSizeMultiplier
                                : (entryPrice - currentPrice) * 50 * currentSizeMultiplier;
                        exitPosition(currentPrice, tradePL, "MA Rejection Exit (reverse)");
                        // Enter new position in opposite direction
                        if (triggeredDirection == 1) {
                            double target = (mas[1] != null && mas[1].available && !Double.isNaN(mas[1].topHighValue))
                                    ? Math.max(currentPrice + 10.0, mas[1].topHighValue)
                                    : currentPrice + 10.0;
                            double newStop = currentPrice - (maRejectionTrailingStopPips * pips);
                            enterLong(currentPrice, newStop, target, 1.0, true, maRejectionTrailingStopPips * pips, 1); // Reason:
                                                                                                                        // MA
                                                                                                                        // Rejection
                                                                                                                        // Long
                        } else {
                            double target = (mas[1] != null && mas[1].available && !Double.isNaN(mas[1].botLowValue))
                                    ? Math.min(currentPrice - 10.0, mas[1].botLowValue)
                                    : currentPrice - 10.0;
                            double newStop = currentPrice + (maRejectionTrailingStopPips * pips);
                            enterShort(currentPrice, newStop, target, 1.0, true, maRejectionTrailingStopPips * pips, 2); // Reason:
                                                                                                                         // MA
                                                                                                                         // Rejection
                                                                                                                         // Short
                        }
                        Log.info("MA Rejection Entry: Reversed position to "
                                + (triggeredDirection == 1 ? "LONG" : "SHORT")
                                + " at " + currentPrice);
                    } else if (currentPosition == 0) {
                        // Enter new position
                        if (triggeredDirection == 1) {
                            double target = (mas[1] != null && mas[1].available && !Double.isNaN(mas[1].topHighValue))
                                    ? Math.max(currentPrice + 10.0, mas[1].topHighValue)
                                    : currentPrice + 10.0;
                            double newStop = currentPrice - (maRejectionTrailingStopPips * pips);
                            enterLong(currentPrice, newStop, target, 1.0, true, maRejectionTrailingStopPips * pips, 1); // Reason:
                                                                                                                        // MA
                                                                                                                        // Rejection
                                                                                                                        // Long
                        } else {
                            double target = (mas[1] != null && mas[1].available && !Double.isNaN(mas[1].botLowValue))
                                    ? Math.min(currentPrice - 10.0, mas[1].botLowValue)
                                    : currentPrice - 10.0;
                            double newStop = currentPrice + (maRejectionTrailingStopPips * pips);
                            enterShort(currentPrice, newStop, target, 1.0, true, maRejectionTrailingStopPips * pips, 2); // Reason:
                                                                                                                         // MA
                                                                                                                         // Rejection
                                                                                                                         // Short
                        }
                        Log.info("MA Rejection Entry: Entered " + (triggeredDirection == 1 ? "LONG" : "SHORT") + " at "
                                + currentPrice);
                    }
                } // End MA2 suppression check else block
            } // End MA rejection entry trigger block

            // --- Update Opportunity Indicators ---
            // Update MA3 Rejection Opportunity Indicator
            if (ma3Checked && ma3RejectionOpportunityIndicator != null) {
                if (ma3RejectionOpportunityCountdown > 0) {
                    ma3RejectionOpportunityIndicator.addPoint(ma3RejectionOpportunityCountdown
                            * (triggeredFromMA3 ? (triggeredDirection > 0 ? 1 : (triggeredDirection < 0 ? -1 : 1))
                                    : 1));
                    ma3RejectionOpportunityCountdown = Math.max(0, ma3RejectionOpportunityCountdown - 5);
                } else {
                    ma3RejectionOpportunityIndicator.addPoint(0);
                }
            }
            // Update MA4 Rejection Opportunity Indicator
            if (ma4Checked && ma4RejectionOpportunityIndicator != null) {
                if (ma4RejectionOpportunityCountdown > 0) {
                    ma4RejectionOpportunityIndicator.addPoint(ma4RejectionOpportunityCountdown
                            * (triggeredFromMA4 ? (triggeredDirection > 0 ? 1 : (triggeredDirection < 0 ? -1 : 1))
                                    : 1));
                    ma4RejectionOpportunityCountdown = Math.max(0, ma4RejectionOpportunityCountdown - 5);
                } else {
                    ma4RejectionOpportunityIndicator.addPoint(0);
                }
            }

            // --- Mini ATR Trend Trading (If Enabled) ---
            // Update mode from parameter
            useMiniAtrTrendForTrading = useMiniAtrTrendForTradingInt;
            if (!disableAllLogging)
                Log.info("tradeLogic called with useMiniAtrTrendForTrading=" + useMiniAtrTrendForTrading);

            if (currentPosition == 0) {
                barsSinceLastExit++;
            } else {
                barsSinceLastExit = 0;
            }

            double atr = calculateATR(14);

            // Execute Mini ATR Trend trades if enabled (mode 1 or 2)
            if (useMiniAtrTrendForTrading > 0) {
                // executeMiniAtrTrendTrades(currentPrice, atr); // Assuming atr calculation
                // happens elsewhere or is not needed here
                // If executeMiniAtrTrendTrades is enabled and active, it might handle its own
                // entries/exits.
                // The original code had a return here if useMiniAtrTrendForTrading == 2
                // (exclusive mode).
                // Let's keep that structure.
            }

            // --- Update Confirmation/Suppression Flags ---
            // These need to be updated regardless of trading mode for indicators and
            // potential use in non-MA exits.
            updateMA3Confirmation(currentPrice);
            updateMASlopeConfirmation(currentPrice);
            updateMA2SlopeTrendConfirmation(currentPrice);
            updateVisibilityTrendBiasExitSuppression(currentPrice);

            // --- Early Exit for Exclusive Mini ATR Mode ---
            if (useMiniAtrTrendForTrading == 2) {
                if (debugLogging && !disableAllLogging) {
                    Log.info(
                            "tradeLogic: Returning early due to exclusive Mini ATR mode (useMiniAtrTrendForTrading == 2).");
                }

                return; // This return prevents the rest of tradeLogic from running
            }

            // Determine trend conditions when no position is active (using AVwapMAs
            // internal state)
            if (currentPosition == 0) {
                // Use canonical MA availability flags from AVwapMAs
                boolean ma1Available = mas[0].available;
                boolean ma2Available = mas[1].available;
                boolean ma3Available = mas[2].available;
                boolean ma4Available = mas[3].available;

                boolean isUptrendNow = false;
                boolean isDowntrendNow = false;

                if (ma1Available && ma2Available) {
                    if (ma3Available) {
                        isUptrendNow = mas[0].botLowValue > mas[1].botLowValue
                                && mas[1].botLowValue > mas[2].botLowValue && currentPrice > mas[1].botLowValue + 5.0;
                        isDowntrendNow = mas[0].topHighValue < mas[1].topHighValue
                                && mas[1].topHighValue < mas[2].topHighValue;
                    } else {
                        isUptrendNow = mas[0].botLowValue > mas[1].botLowValue
                                && currentPrice > mas[1].botLowValue + 5.0 && currentPrice > mas[0].botLowValue;
                        isDowntrendNow = mas[0].topHighValue < mas[1].topHighValue
                                && currentPrice < mas[1].topHighValue - 5.0 && currentPrice < mas[0].topHighValue;
                    }
                }

                boolean allMAsAvailable = ma1Available && ma2Available && ma3Available && ma4Available;

                isStrongUptrendNow = false;
                if (allMAsAvailable) {
                    boolean botMAsStacked = mas[0].botLowValue >= mas[1].botLowValue
                            && mas[1].botLowValue > mas[2].botLowValue && mas[2].botLowValue >= mas[3].botLowValue;
                    boolean priceAboveMAsWithGap = currentPrice > (mas[0].botLowValue + 5.0) &&
                            currentPrice > (mas[1].botLowValue + 5.0) &&
                            currentPrice > (mas[2].botLowValue + 5.0) &&
                            currentPrice > (mas[3].botLowValue + 5.0);
                    boolean topsAreClose = Math.abs(mas[0].topHighValue - mas[1].topHighValue) <= (3.0 + EPSILON) &&
                            Math.abs(mas[1].topHighValue - mas[2].topHighValue) <= (3.0 + EPSILON) &&
                            Math.abs(mas[2].topHighValue - mas[3].topHighValue) <= (3.0 + EPSILON);
                    isStrongUptrendNow = botMAsStacked && priceAboveMAsWithGap && topsAreClose;
                }

                isStrongDowntrendNow = false;
                if (allMAsAvailable) {
                    boolean topMAsStacked = mas[0].topHighValue <= mas[1].topHighValue
                            && mas[1].topHighValue < mas[2].topHighValue && mas[2].topHighValue <= mas[3].topHighValue;
                    boolean priceBelowMAsWithGap = currentPrice < (mas[0].topHighValue - 5.0) &&
                            currentPrice < (mas[1].topHighValue - 5.0) &&
                            currentPrice < (mas[2].topHighValue - 5.0) &&
                            currentPrice < (mas[3].topHighValue - 5.0);
                    boolean bottomsAreClose = Math.abs(mas[0].botLowValue - mas[1].botLowValue) <= (3.0 + EPSILON) &&
                            Math.abs(mas[1].botLowValue - mas[2].botLowValue) <= (3.0 + EPSILON) &&
                            Math.abs(mas[2].botLowValue - mas[3].botLowValue) <= (3.0 + EPSILON);
                    isStrongDowntrendNow = topMAsStacked && priceBelowMAsWithGap && bottomsAreClose;
                }

                // Update trend duration counters
                updateTrendDurations();

                // Determine if we're in a trend based on duration counters
                boolean isUptrend = (strongUptrendDuration >= 3) || (uptrendDuration >= 5);
                boolean isDowntrend = (strongDowntrendDuration >= 3) || (downtrendDuration >= 5);

                // Update the uptrend and downtrend indicators
                updateTrendIndicators(isUptrend, isDowntrend);

                // Consider re-entry condition
                boolean canReEnter = false;
                if (barsSinceLastExit <= 10 && lastExitPrice > 0) {
                    if (Math.abs(currentPrice - lastExitPrice) <= 5.0 + EPSILON) {
                        canReEnter = true;
                    }
                }

                // Execute standard MA trade entries (only if not in exclusive MiniATR mode)
                // Ensure MA Pending Entry logic is handled correctly - call executeTradeEntries
                executeTradeEntries(currentPrice, atr, ma1Available, ma2Available, ma3Available, ma4Available,
                        isUptrend, isDowntrend, canReEnter);
            }

            // ======== ENTRY TRIPLE STOP LOGIC ==========
            // Check for Entry Triple Stop entries when flat
            if (currentPosition == 0 && Boolean.TRUE.equals(entryTriple_Enabled)) {
                executeEntryTripleStopLogic(currentPrice);
            }
            // ======== END ENTRY TRIPLE STOP LOGIC ==========

            // ======== START NEW MA EXIT LOGIC CALL ==========
            // Check for MA exits using the new triple stop logic FIRST
            // This is crucial and should run BEFORE the old exit logic block
            executeMaTripleStopExitLogic(currentPrice, false);
            // ======== END NEW MA EXIT LOGIC CALL ===========

            // ======== MODIFIED ORIGINAL EXIT LOGIC BLOCK (FOR NON-MA TRADES OR MA TRADES
            // WITH DISABLED EXITS) =========
            // This block handles exits for:
            // 1. NON-MA trades (isMaTradeActive == false)
            // 2. MA trades (isMaTradeActive == true) IF both enableMaNormalExits AND
            // enableMaCatastrophicExit are false
            if (currentPosition != 0
                    && (!isMaTradeActive || (isMaTradeActive && !enableMaNormalExits && !enableMaCatastrophicExit))) {
                if (isMaTradeActive && debugLogging && !disableAllLogging) {
                    Log.info("MA Trade (Triple Stop Exits Disabled) - Evaluating with general exit logic.");
                }
                // Calculate trade P/L locally for this block
                double nonMaTradePL = 0.0;
                if (currentPosition == 1) {
                    nonMaTradePL = (currentPrice - entryPrice) * 50 * currentSizeMultiplier;
                } else if (currentPosition == -1) {
                    nonMaTradePL = (entryPrice - currentPrice) * 50 * currentSizeMultiplier;
                }

                // --- Define Catastrophic levels locally - set to NaN as originalStopDistance
                // is removed ---
                double catastrophicLongStopLevel = Double.NaN;
                double catastrophicShortStopLevel = Double.NaN;
                /*
                 * // Original calculation using removed variable:
                 * if (originalStopDistance > 0 && !disableCatastrophicStops) {
                 * catastrophicLongStopLevel = entryPrice - (originalStopDistance *
                 * catastrophicStopMultiplier);
                 * catastrophicShortStopLevel = entryPrice + (originalStopDistance *
                 * catastrophicStopMultiplier);
                 * }
                 */

                // --- Non-MA Exit Checks ---
                if (currentPosition == 1) { // Long Exit Check
                    Log.info("NON-MA LONG EXIT CHECK: price=" + currentPrice +
                            ", stop=" + stopPrice + // Note: stopPrice might be stale if useTrailingStop is
                                                    // false/removed
                            ", target=" + targetPrice +
                            // ", catastrophicStop=" + catastrophicLongStopLevel + // Commented out as it's
                            // NaN now
                            ", ma3ConfActive=" + ma3ConfirmationActive +
                            ", maSlopeConfActive=" + maSlopeConfirmationActive +
                            ", ma2SlopeTrendConfActive="
                            + (useMa2SlopeTrendConfirmation && ma2SlopeTrendConfirmationActive) +
                            ", visBiasExitSuppActive=" + visibilityTrendBiasExitSuppressionActive);

                    // Standard exit: stop loss or take profit. Check if stopPrice/targetPrice are
                    // valid.
                    boolean standardExitTriggered = (!Double.isNaN(stopPrice) && currentPrice <= stopPrice)
                            || (!Double.isNaN(targetPrice) && currentPrice >= targetPrice);
                    // Catastrophic exit: Check if enabled and level is valid (currently always NaN)
                    // boolean catastrophicStopTriggered = !disableCatastrophicStops &&
                    // !Double.isNaN(catastrophicLongStopLevel) && currentPrice <=
                    // catastrophicLongStopLevel;
                    boolean catastrophicStopTriggered = false; // Force false as it's no longer calculated

                    // Check if any suppression is active
                    boolean anySuppressionActive = (useMa3ConfirmationExitSuppression && ma3ConfirmationActive) ||
                            maSlopeConfirmationActive ||
                            (useMa2SlopeTrendConfirmation && ma2SlopeTrendConfirmationActive) ||
                            (useVisibilityTrendBiasExitSuppression && visibilityTrendBiasExitSuppressionActive);

                    // Trigger exit if standard exit occurs without suppression, OR if catastrophic
                    // stop hits
                    if ((standardExitTriggered && !anySuppressionActive) || catastrophicStopTriggered) {
                        String exitReason = catastrophicStopTriggered ? "CATASTROPHIC STOP (Non-MA - Disabled)"
                                : "Standard Exit (Non-MA)";
                        Log.info("Triggering NON-MA Long Exit: Reason=" + exitReason);
                        exitPosition(currentPrice, nonMaTradePL, exitReason);
                    }
                } else if (currentPosition == -1) { // Short Exit Check
                    Log.info("NON-MA SHORT EXIT CHECK: price=" + currentPrice +
                            ", stop=" + stopPrice + // Note: stopPrice might be stale
                            ", target=" + targetPrice +
                            // ", catastrophicStop=" + catastrophicShortStopLevel + // Commented out as it's
                            // NaN now
                            ", ma3ConfActive=" + ma3ConfirmationActive +
                            ", maSlopeConfActive=" + maSlopeConfirmationActive +
                            ", ma2SlopeTrendConfActive="
                            + (useMa2SlopeTrendConfirmation && ma2SlopeTrendConfirmationActive) +
                            ", visBiasExitSuppActive=" + visibilityTrendBiasExitSuppressionActive);

                    boolean standardExitTriggered = (!Double.isNaN(stopPrice) && currentPrice >= stopPrice)
                            || (!Double.isNaN(targetPrice) && currentPrice <= targetPrice);
                    // boolean catastrophicStopTriggered = !disableCatastrophicStops &&
                    // !Double.isNaN(catastrophicShortStopLevel) && currentPrice >=
                    // catastrophicShortStopLevel;
                    boolean catastrophicStopTriggered = false; // Force false

                    boolean anySuppressionActive = (useMa3ConfirmationExitSuppression && ma3ConfirmationActive) ||
                            maSlopeConfirmationActive ||
                            (useMa2SlopeTrendConfirmation && ma2SlopeTrendConfirmationActive) ||
                            (useVisibilityTrendBiasExitSuppression && visibilityTrendBiasExitSuppressionActive);

                    if ((standardExitTriggered && !anySuppressionActive) || catastrophicStopTriggered) {
                        String exitReason = catastrophicStopTriggered ? "CATASTROPHIC STOP (Non-MA - Disabled)"
                                : "Standard Exit (Non-MA)";
                        Log.info("Triggering NON-MA Short Exit: Reason=" + exitReason);
                        exitPosition(currentPrice, nonMaTradePL, exitReason);
                    }
                }
            } // <-- End of modified original exit logic block for NON-MA trades

            // --- Chop Zone Logic ---
            if (enableChopDetection && chopZoneCalculator != null && chopZoneCalculator.isChopZone()) {
                // Check if still in position after all exit logic
                if (currentPosition != 0) {
                    // Option 3: For existing positions, maybe widen stops (if applicable to non-MA
                    // trades)
                    // This depends on whether stopPrice is still meaningful for non-MA trades.
                    if (debugLogging && !disableAllLogging) {
                        Log.info(
                                "In chop zone with existing position - consider widening stops if applicable. Current Stop: "
                                        + stopPrice);
                    }
                    // double chopZoneStopMultiplier = 1.75;
                    // if (currentPosition == 1) {
                    // stopPrice = Math.min(stopPrice, highestPrice - (currentTrailingStopDistance *
                    // chopZoneStopMultiplier));
                    // } else if (currentPosition == -1) {
                    // stopPrice = Math.max(stopPrice, lowestPrice + (currentTrailingStopDistance *
                    // chopZoneStopMultiplier));
                    // }
                } else {
                    // Option 1: Avoid new entries in chop zone (this return prevents falling
                    // through to entry logic below)
                    if (debugLogging && !disableAllLogging) {
                        Log.info("In chop zone - avoiding new trade entry attempt within tradeLogic");
                    }
                    // return; // Be careful with returns inside logic blocks
                }

                // Option 2: Reduce size multiplier if entry were to occur (sizeMultiplier is
                // used in enterLong/Short)
                // This might be better handled within the entry logic itself if a chop zone
                // flag is passed or checked there.
                // currentSizeMultiplier = Math.min(currentSizeMultiplier, 0.5);
            }
        } // End synchronized block
    } // <<< Closing brace for tradeLogic method

    /**
     * Checks if the stored close prices for the specified number of recent data
     * points are valid
     *
     * @param dataPoints The number of recent data points to check
     * @return true if all close prices are valid, false if any are NaN or infinite
     */
    private boolean areClosePricesValid(int dataPoints) {
        synchronized (lock) {
            if (size == 0 || dataPoints <= 0)
                return false;

            int checkPoints = Math.min(dataPoints, size);
            int startPos = (head - 1 + capacity) % capacity;

            for (int i = 0; i < checkPoints; i++) {
                int index = (startPos - i + capacity) % capacity;
                double closePrice = closePrices[index];

                if (Double.isNaN(closePrice) || Double.isInfinite(closePrice)) {
                    return false;
                }
            }

            return true;
        }
    }

    private void updateTrendDurations() {
        synchronized (lock) {
            if (isStrongUptrendNow) {
                strongUptrendDuration++;
                strongDowntrendDuration = 0;
                uptrendDuration++;
                downtrendDuration = 0;
            } else if (isStrongDowntrendNow) {
                strongDowntrendDuration++;
                strongUptrendDuration = 0;
                downtrendDuration++;
                uptrendDuration = 0;
            } else {
                strongUptrendDuration = 0;
                strongDowntrendDuration = 0;
                if (uptrendDuration > 0)
                    uptrendDuration++;
                else if (downtrendDuration > 0)
                    downtrendDuration++;
                else {
                    uptrendDuration = 0;
                    downtrendDuration = 0;
                }
            }
        }
    }

    private void updateTrendIndicators(boolean isUptrend, boolean isDowntrend) {
        synchronized (lock) {
            uptrendIndicator.addPoint(strongUptrendDuration >= 3 ? 2.0 : isUptrend ? 1.0 : 0.0);
            downtrendIndicator.addPoint(strongDowntrendDuration >= 3 ? 2.0 : isDowntrend ? 1.0 : 0.0);
        }
    }

    private void executeTradeEntries(double currentPrice, double atr, boolean ma1Available, boolean ma2Available,
            boolean ma3Available, boolean ma4Available, boolean isUptrend, boolean isDowntrend,
            boolean canReEnter) {

        if (debugLogging && !disableAllLogging)
            Log.info("executeTradeEntries: barsSinceLastExit=" + barsSinceLastExit + ", canReEnter=" + canReEnter
                    + ", cooldownPeriod=" + cooldownPeriod);
        if (debugLogging && !disableAllLogging)
            Log.info("MA Availability: ma1=" + ma1Available + ", ma2=" + ma2Available + ", ma3=" + ma3Available
                    + ", ma4="
                    + ma4Available + ", size=" + size);
        if (debugLogging && !disableAllLogging)
            Log.info("MA Values: ma1Top=" + mas[0].topHighValue + ", ma1Bot=" + mas[0].botLowValue +
                    ", ma2Top=" + mas[1].topHighValue + ", ma2Bot=" + mas[1].botLowValue +
                    ", ma3Top=" + mas[2].topHighValue + ", ma3Bot=" + mas[2].botLowValue +
                    ", ma4Top=" + mas[3].topHighValue + ", ma4Bot=" + mas[3].botLowValue +
                    ", currentPrice=" + currentPrice);
        if (debugLogging && !disableAllLogging)
            Log.info("Trend States: isUptrend=" + isUptrend + ", isDowntrend=" + isDowntrend +
                    ", uptrendDuration=" + uptrendDuration + ", downtrendDuration=" + downtrendDuration);
        synchronized (lock) {
            // --- MA Trailing/Pending Entry Logic (must be first!) ---
            if (usePendingEntryStops != null && usePendingEntryStops) {
                // Only allow one pending MA entry at a time
                if (currentPosition == 0 && !hasMAPendingEntryOrder) {

                    // --- Start Detection & Assignment --- ADDED
                    int direction = 0;
                    int specificSetupReason = 0;

                    // Detect long entry condition (EXAMPLE - THIS IS THE GENERIC SETUP)
                    if (mas[3].available && currentPrice > mas[3].topHighValue &&
                            (mas[1].available && (mas[0].botLowValue > mas[1].botLowValue ||
                                    (Math.abs(mas[0].botLowValue - mas[1].botLowValue) < EPSILON &&
                                            mas[2].available
                                            && Math.abs(mas[1].botLowValue - mas[2].botLowValue) < EPSILON)))) {
                        direction = 1;
                        specificSetupReason = 13; // MA Pending Long Setup
                    } else if (mas[2].available && currentPrice > mas[2].topHighValue &&
                            (mas[1].available && (mas[0].botLowValue > mas[1].botLowValue ||
                                    (Math.abs(mas[0].botLowValue - mas[1].botLowValue) < EPSILON
                                            && Math.abs(mas[1].botLowValue - mas[2].botLowValue) < EPSILON)))) {
                        direction = 1;
                        specificSetupReason = 13; // MA Pending Long Setup
                    } else if (!mas[2].available && !mas[3].available && currentPrice > mas[1].topHighValue
                            && (mas[1].available ? mas[0].botLowValue > mas[1].botLowValue : false)) {
                        direction = 1;
                        specificSetupReason = 13; // MA Pending Long Setup
                    } else if (currentPrice < mas[1].botLowValue && mas[0].topHighValue < mas[1].topHighValue) {
                        direction = -1;
                        specificSetupReason = 14; // MA Pending Short Setup
                    } else if (mas[2].available && currentPrice < mas[2].botLowValue
                            && mas[0].topHighValue < mas[1].topHighValue) {
                        direction = -1;
                        specificSetupReason = 14; // MA Pending Short Setup
                    }
                    // --- End Detection & Assignment --- ADDED

                    // Only set up pending entry if direction is determined
                    if (direction != 0) {
                        hasMAPendingEntryOrder = true;
                        maPendingEntryDirection = direction;
                        maPendingEntrySetupReasonCode = specificSetupReason; // Store the specific setup reason
                        maBestPriceForTrailingEntry = currentPrice;
                        if (useMaAtrTrailingStop != null && useMaAtrTrailingStop) {
                            double atrDist = getMaAtrTrailingDistance();
                            maPendingEntryStopLevel = direction == 1
                                    ? currentPrice + atrDist
                                    : currentPrice - atrDist;
                            if (debugLogging)
                                Log.info("[MA ATR Trailing Entry] Initialized: direction=" + direction + ", price="
                                        + currentPrice + ", stop=" + maPendingEntryStopLevel + ", ATR dist=" + atrDist);
                        } else {
                            maPendingEntryStopLevel = direction == 1
                                    ? currentPrice + (pendingEntryStopPips * pips)
                                    : currentPrice - (pendingEntryStopPips * pips);
                        }
                        maPendingEntryConfirmationCounter = 0;
                        Log.info("MA Pending Entry initialized: direction=" + direction + ", price=" + currentPrice
                                + ", stop=" + maPendingEntryStopLevel);
                    }
                }

                // --- Process MA pending entry order ---
                if (hasMAPendingEntryOrder) {
                    // Confirmation bars logic
                    if (pendingEntryConfirmationBars > 0) {
                        maPendingEntryConfirmationCounter++;
                        if (maPendingEntryConfirmationCounter < pendingEntryConfirmationBars) {
                            // Update indicators for pending entry
                            if (pendingEntryStopIndicator != null)
                                pendingEntryStopIndicator.addPoint(maPendingEntryStopLevel);
                            if (bestPriceIndicator != null)
                                bestPriceIndicator.addPoint(maBestPriceForTrailingEntry);
                            return;
                        }
                    }
                    // Trailing logic
                    if (maPendingEntryDirection == 1 && currentPrice < maBestPriceForTrailingEntry) {
                        maBestPriceForTrailingEntry = currentPrice;
                        if (useMaAtrTrailingStop != null && useMaAtrTrailingStop) {
                            double atrDist = getMaAtrTrailingDistance();
                            maPendingEntryStopLevel = currentPrice + atrDist;
                            if (debugLogging)
                                Log.info("[MA ATR Trailing Entry] Updated best price=" + maBestPriceForTrailingEntry
                                        + ", stop=" + maPendingEntryStopLevel + ", ATR dist=" + atrDist);
                        } else {
                            maPendingEntryStopLevel = currentPrice + (pendingEntryStopPips * pips);
                        }
                    } else if (maPendingEntryDirection == -1 && currentPrice > maBestPriceForTrailingEntry) {
                        maBestPriceForTrailingEntry = currentPrice;
                        if (useMaAtrTrailingStop != null && useMaAtrTrailingStop) {
                            double atrDist = getMaAtrTrailingDistance();
                            maPendingEntryStopLevel = currentPrice - atrDist;
                            if (debugLogging)
                                Log.info("[MA ATR Trailing Entry] Updated best price=" + maBestPriceForTrailingEntry
                                        + ", stop=" + maPendingEntryStopLevel + ", ATR dist=" + atrDist);
                        } else {
                            maPendingEntryStopLevel = currentPrice - (pendingEntryStopPips * pips);
                        }
                    }
                    // Update indicators for pending entry
                    if (pendingEntryStopIndicator != null)
                        pendingEntryStopIndicator.addPoint(maPendingEntryStopLevel);
                    if (bestPriceIndicator != null)
                        bestPriceIndicator.addPoint(maBestPriceForTrailingEntry);
                    // Trigger entry
                    if ((maPendingEntryDirection == 1 && currentPrice >= maPendingEntryStopLevel) ||
                            (maPendingEntryDirection == -1 && currentPrice <= maPendingEntryStopLevel)) {
                        // Calculate stop/target similar to original logic
                        double stop, target;
                        double sizeMult = 1.0;
                        boolean useTrail = true;
                        double trailDist = (maPendingEntryDirection == 1)
                                ? breakoutTrailingStopDistance
                                : trendFollowingTrailingStopDistance;
                        if (maPendingEntryDirection == 1) {
                            stop = currentPrice - breakoutTrailingStopDistance;
                            target = Math.max(currentPrice + 10.0, mas[1].topHighValue);
                            enterLong(currentPrice, stop, target, sizeMult, useTrail, breakoutTrailingStopDistance,
                                    maPendingEntrySetupReasonCode); // USE THE STORED CODE
                            // isMaTradeActive = true; // Set inside enterLong/enterShort

                        } else { // Execute Short
                            stop = currentPrice + breakoutTrailingStopDistance;
                            target = Math.min(currentPrice - 10.0, mas[1].botLowValue);
                            enterShort(currentPrice, stop, target, sizeMult, useTrail, breakoutTrailingStopDistance,
                                    maPendingEntrySetupReasonCode); // USE THE STORED CODE
                            // isMaTradeActive = true; // Set inside enterLong/enterShort
                        }
                        // Reset pending order state ONLY after successful entry attempt
                        hasMAPendingEntryOrder = false;
                        maPendingEntryDirection = 0;
                        maPendingEntryStopLevel = 0.0;
                        maBestPriceForTrailingEntry = 0.0;
                        maPendingEntryConfirmationCounter = 0;
                        maPendingEntrySetupReasonCode = 0; // Reset setup reason code
                        // Reset Entry Triple simulation positions
                        resetEntryTripleSimulationPositions();
                        // Clear indicators after entry
                        if (pendingEntryStopIndicator != null)
                            pendingEntryStopIndicator.addPoint(Double.NaN);
                        if (bestPriceIndicator != null)
                            bestPriceIndicator.addPoint(Double.NaN);
                        Log.info("MA Pending Entry triggered at price=" + currentPrice);
                    }
                    return; // Skip immediate entries if using pending entry stops
                } else {
                    // No pending entry: clear indicators
                    if (pendingEntryStopIndicator != null)
                        pendingEntryStopIndicator.addPoint(Double.NaN);
                    if (bestPriceIndicator != null)
                        bestPriceIndicator.addPoint(Double.NaN);
                }
                return;
            }

            // ...existing code for immediate/direct entries (only runs if
            // usePendingEntryStops is false)...
            if (barsSinceLastExit > cooldownPeriod || canReEnter) {
                if (mas[3].available && currentPrice > mas[3].topHighValue &&
                        (mas[1].available && (mas[0].botLowValue > mas[1].botLowValue ||
                                (Math.abs(mas[0].botLowValue - mas[1].botLowValue) < EPSILON &&
                                        mas[2].available
                                        && Math.abs(mas[1].botLowValue - mas[2].botLowValue) < EPSILON)))) {
                    enterLong(currentPrice, currentPrice - breakoutTrailingStopDistance,
                            Math.max(currentPrice + 10.0, mas[1].topHighValue), Math.min(1.5, MAX_SIZE_MULTIPLIER),
                            true, breakoutTrailingStopDistance, 5); // Reason: Standard MA Long
                } else if (mas[2].available && currentPrice > mas[2].topHighValue &&
                        (mas[1].available && (mas[0].botLowValue > mas[1].botLowValue ||
                                (Math.abs(mas[0].botLowValue - mas[1].botLowValue) < EPSILON
                                        && Math.abs(mas[1].botLowValue - mas[2].botLowValue) < EPSILON)))) {
                    enterLong(currentPrice, currentPrice - breakoutTrailingStopDistance,
                            Math.max(currentPrice + 10.0, mas[1].topHighValue), Math.min(1.5, MAX_SIZE_MULTIPLIER),
                            true, breakoutTrailingStopDistance, 5); // Reason: Standard MA Long
                } else if (!mas[2].available && !mas[3].available && currentPrice > mas[1].topHighValue
                        && (mas[1].available ? mas[0].botLowValue > mas[1].botLowValue : false)) {
                    enterLong(currentPrice, currentPrice - breakoutTrailingStopDistance,
                            Math.max(currentPrice + 10.0, mas[1].topHighValue), Math.min(1.5, MAX_SIZE_MULTIPLIER),
                            true, breakoutTrailingStopDistance, 5); // Reason: Standard MA Long
                }

                if (currentPrice < mas[1].botLowValue && mas[0].topHighValue < mas[1].topHighValue) {
                    enterShort(currentPrice, currentPrice + breakoutTrailingStopDistance,
                            Math.min(currentPrice - 10.0, mas[1].botLowValue), Math.min(1.5, MAX_SIZE_MULTIPLIER), true,
                            breakoutTrailingStopDistance, 6); // Reason: Standard MA Short
                } else if (mas[2].available && currentPrice < mas[2].botLowValue
                        && mas[0].topHighValue < mas[1].topHighValue) {
                    enterShort(currentPrice, currentPrice + breakoutTrailingStopDistance,
                            Math.min(currentPrice - 10.0, mas[1].botLowValue), Math.min(1.5, MAX_SIZE_MULTIPLIER), true,
                            breakoutTrailingStopDistance, 6); // Reason: Standard MA Short
                }

                if (isUptrend && currentPrice <= mas[0].botLowValue + 2.0
                        && (mas[2].available
                                ? (mas[0].botLowValue > mas[2].botLowValue
                                        || Math.abs(mas[0].botLowValue - mas[2].botLowValue) < EPSILON
                                                && Math.abs(mas[1].botLowValue - mas[2].botLowValue) < EPSILON)
                                : (mas[1].available ? mas[0].botLowValue > mas[1].botLowValue : false))) {
                    double stopDistance = 1.5 * atr;
                    enterLong(currentPrice, currentPrice - stopDistance,
                            Math.max(currentPrice + 5.0, mas[0].topHighValue), Math.min(1.0, MAX_SIZE_MULTIPLIER),
                            false, 0.0, 7); // Reason: ATR Stop Long
                }

                if (isUptrend && currentPrice > mas[0].botLowValue
                        && (mas[2].available
                                ? (mas[0].botLowValue > mas[2].botLowValue
                                        || Math.abs(mas[0].botLowValue - mas[2].botLowValue) < EPSILON
                                                && Math.abs(mas[1].botLowValue - mas[2].botLowValue) < EPSILON)
                                : (mas[1].available ? mas[0].botLowValue > mas[1].botLowValue : false))) {
                    enterLong(currentPrice, currentPrice - trendFollowingTrailingStopDistance,
                            Math.max(currentPrice + 5.0, mas[0].topHighValue), Math.min(1.0, MAX_SIZE_MULTIPLIER), true,
                            trendFollowingTrailingStopDistance, 8); // Reason: Trend Following Long
                }

                if (isDowntrend && currentPrice >= mas[0].topHighValue - 2.0
                        && (mas[2].available ? mas[0].topHighValue < mas[2].topHighValue
                                : mas[0].topHighValue < mas[1].topHighValue)) {
                    double stopDistance = 1.5 * atr;
                    enterShort(currentPrice, currentPrice + stopDistance,
                            Math.min(currentPrice - 5.0, mas[0].botLowValue), Math.min(1.0, MAX_SIZE_MULTIPLIER), false,
                            0.0, 9); // Reason: ATR Stop Short
                }

                if (isDowntrend && currentPrice < mas[0].topHighValue
                        && (mas[2].available ? mas[0].topHighValue < mas[2].topHighValue
                                : mas[0].topHighValue < mas[1].topHighValue)) {
                    enterShort(currentPrice, currentPrice + trendFollowingTrailingStopDistance,
                            Math.min(currentPrice - 5.0, mas[0].botLowValue), Math.min(1.0, MAX_SIZE_MULTIPLIER), true,
                            trendFollowingTrailingStopDistance, 10); // Reason: Trend Following Short
                }

                if (lastExitPrice > 0 && currentPrice < lastExitPrice - 5.0
                        && (mas[2].available ? mas[0].topHighValue < mas[2].topHighValue
                                : mas[0].topHighValue < mas[1].topHighValue)
                        && isDowntrend) {
                    enterShort(currentPrice, currentPrice + trendFollowingTrailingStopDistance,
                            Math.min(currentPrice - 5.0, mas[0].botLowValue), Math.min(1.0, MAX_SIZE_MULTIPLIER), true,
                            trendFollowingTrailingStopDistance, 11); // Reason: Trend Following Short
                }
            }
        }
    }

    private void enterLong(double entryPrice, double stopPrice, double targetPrice, double sizeMultiplier,
            boolean useTrailing, double trailingDistance, int tradeReasonCode) {
        synchronized (lock) {
            this.isMaTradeActive = true; // THIS IS AN MA TRADE
            if (entryPrice < 0 || Double.isNaN(entryPrice) || Double.isInfinite(entryPrice)) {
                Log.warn("Invalid long entry price: " + entryPrice);
                return;
            }

            if (OSCInstance.isReduceOnlyMode()) {
                Log.info("Reduce Only Mode is active, skipping long entry");
                return;
            }

            boolean success = true;
            // Send real order if live trading is enabled
            if (enableLiveTrading && OSCInstance != null) {
                // Send market buy order with fixed position size
                success = OSCInstance.sendMarketBuyOrder(
                        positionSize, // Use fixed position size parameter
                        OrderDuration.GTC,
                        stopPrice,
                        targetPrice);

                if (!success) {
                    Log.error("Failed to send long entry order at price: " + entryPrice);
                } else {
                    Log.info("Live Long Entry Order Sent: Size=" + positionSize + ", Price=" + entryPrice);
                }
            }

            // Only update position state if simulated mode, or live trading and order was
            // successful
            if (!enableLiveTrading || (enableLiveTrading && success)) {
                currentPosition = 1;
                this.entryPrice = entryPrice;
                this.targetPrice = targetPrice;
                this.currentSizeMultiplier = Math.min(sizeMultiplier, MAX_SIZE_MULTIPLIER);
                this.highestPrice = entryPrice;
                this.ma3ConfirmationActive = false;

                // Add entry icon
                IconFactory.addTradeIcon(tradeIconsIndicator, entryPrice, true, true, debugLogging); // true = entry,
                                                                                                     // true = long
                if (TradeEntryReasonIndicator != null) {
                    TradeEntryReasonIndicator.addPoint(tradeReasonCode); // Plot entry reason
                }
                if (TradeExitReasonIndicator != null) {
                    TradeExitReasonIndicator.addPoint(0.0); // Clear exit reason
                }
                Log.info("Long Entry: Price=" + entryPrice + ", Initial Stop=" + stopPrice +
                        ", Target=" + targetPrice + ", Size=" + this.currentSizeMultiplier +
                        ", Trailing Stop=" + useTrailing); // Removed CatastrophicStop from log as it's changing
            }
        }
    }

    private void enterShort(double entryPrice, double stopPrice, double targetPrice, double sizeMultiplier,
            boolean useTrailing, double trailingDistance, int tradeReasonCode) {
        synchronized (lock) {
            this.isMaTradeActive = true; // THIS IS AN MA TRADE
            if (entryPrice < 0 || Double.isNaN(entryPrice) || Double.isInfinite(entryPrice)) {
                Log.warn("Invalid short entry price: " + entryPrice);
                return;
            }

            if (OSCInstance.isReduceOnlyMode()) {
                Log.info("Reduce Only Mode is active, skipping short entry");
                return;
            }

            boolean success = true;
            // Send real order if live trading is enabled
            if (enableLiveTrading && OSCInstance != null) {
                // Send market sell order with fixed position size
                success = OSCInstance.sendMarketSellOrder(
                        positionSize, // Use fixed position size parameter
                        OrderDuration.GTC,
                        stopPrice,
                        targetPrice);

                if (!success) {
                    Log.error("Failed to send short entry order at price: " + entryPrice);
                } else {
                    Log.info("Live Short Entry Order Sent: Size=" + positionSize + ", Price=" + entryPrice);
                }
            }

            // Only update position state if simulated mode, or live trading and order was
            // successful
            if (!enableLiveTrading || (enableLiveTrading && success)) {
                currentPosition = -1;
                this.entryPrice = entryPrice;
                this.targetPrice = targetPrice;
                this.currentSizeMultiplier = Math.min(sizeMultiplier, MAX_SIZE_MULTIPLIER);
                this.lowestPrice = entryPrice;
                this.ma3ConfirmationActive = false;

                // Add entry icon
                IconFactory.addTradeIcon(tradeIconsIndicator, entryPrice, true, false, debugLogging); // true = entry,
                                                                                                      // false = short
                if (TradeEntryReasonIndicator != null) {
                    TradeEntryReasonIndicator.addPoint(tradeReasonCode); // Plot entry reason
                }
                if (TradeExitReasonIndicator != null) {
                    TradeExitReasonIndicator.addPoint(0.0); // Clear exit reason
                }

                Log.info("Short Entry: Price=" + entryPrice + ", Initial Stop=" + stopPrice +
                        ", Target=" + targetPrice + ", Size=" + this.currentSizeMultiplier +
                        ", Trailing Stop=" + useTrailing); // Removed CatastrophicStop from log
            }
        }
    }

    private void exitPosition(double exitPrice, double tradePL, String exitReason) {
        exitPosition(exitPrice, tradePL, exitReason, false);
    }

    private void exitPosition(double exitPrice, double tradePL, String exitReason, boolean flattening) {
        synchronized (lock) {
            this.isMaTradeActive = false; // Reset MA trade flag on any exit
            boolean success = true;
            // Send real order if live trading is enabled
            if (enableLiveTrading && OSCInstance != null) {
                success = false;
                if (!flattening) {
                    if (currentPosition > 0) { // Exit long position
                        success = OSCInstance.sendMarketSellOrder(
                                positionSize, // Use fixed position size parameter
                                OrderDuration.GTC,
                                0, // No stop loss for exit orders
                                0 // No take profit for exit orders
                        );
                    } else if (currentPosition < 0) { // Exit short position
                        success = OSCInstance.sendMarketBuyOrder(
                                positionSize, // Use fixed position size parameter
                                OrderDuration.GTC,
                                0, // No stop loss for exit orders
                                0 // No take profit for exit orders
                        );
                    }

                    if (!success) {
                        Log.error("Failed to send exit order at price: " + exitPrice);
                    } else {
                        Log.info("Live Exit Order Sent (" + exitReason + "): Price=" + exitPrice);
                    }
                }

                if (flattening) {
                    currentPosition = 0;
                    Log.info("Position is set to 0, OSC flattening is requested(" + exitReason + "): Price="
                            + exitPrice);
                }
            }

            // Only update position state if simulated mode, or live trading and order was
            // successful
            if (!enableLiveTrading || (enableLiveTrading && success)) {
                // Reset pending exit state
                hasPendingExit = false;
                pendingExitDirection = 0;
                pendingExitStopLevel = 0.0;
                bestPriceForTrailingExit = 0.0;
                savedExitTrend = 0;
                pendingExitConfirmationBars = 0;

                // Add exit icon based on position direction
                IconFactory.addTradeIcon(tradeIconsIndicator, exitPrice, false, currentPosition > 0, debugLogging); // false
                                                                                                                    // =
                                                                                                                    // exit,
                                                                                                                    // true/false
                                                                                                                    // =
                                                                                                                    // long/short

                int tradeReasonCode = 0;
                if (exitReason.contains("MA Catastrophic Triple Stop Exit")) {
                    tradeReasonCode = -3;
                } else if (exitReason.contains("CATASTROPHIC STOP (Non-MA")) {
                    tradeReasonCode = -3;
                } else if (exitReason.contains("MA Trigger Triple Stop Exit")) {
                    tradeReasonCode = -2;
                } else if (exitReason.contains("Standard Exit (Non-MA)")) {
                    tradeReasonCode = -2;
                } else if (exitReason.contains("MA Target Exit")) {
                    tradeReasonCode = -1;
                } else if (exitReason.contains("MA Rejection Exit (reverse)")) {
                    tradeReasonCode = -4;
                } else if (exitReason.contains("MA1 Profit Long Exit")) {
                    tradeReasonCode = -5;
                } else if (exitReason.contains("MA1 Profit Short Exit")) {
                    tradeReasonCode = -6;
                } else if (exitReason.contains("OSC Flattening Request")) {
                    tradeReasonCode = -7;
                } else {
                    Log.warn("Unknown exit reason for TradeReasonIndicator: " + exitReason);
                    tradeReasonCode = -99; // Unknown/Other Exit Reason
                }

                // Play exit sound based on reason code
                playTradeSound(tradeReasonCode);

                if (TradeEntryReasonIndicator != null) {
                    TradeEntryReasonIndicator.addPoint(0.0); // Clear entry reason
                }
                if (TradeExitReasonIndicator != null) {
                    TradeExitReasonIndicator.addPoint(tradeReasonCode); // Plot exit reason
                }

                Log.info("Exit (" + exitReason + "): Price=" + exitPrice +
                        ", Trade P/L=" + tradePL + ", Total P/L Before=" + totalPL);
                if (tradePLs.size() >= 1000)
                    tradePLs.remove(0);
                tradePLs.add(tradePL);
                totalPL += tradePL;
                Log.info("Total P/L After=" + totalPL);
                lastExitPrice = exitPrice;

                // Reset Mini ATR-specific variables
                miniAtrTrailingStopPrice = 0.0;

                // Also reset trailing entry variables to avoid stale pending orders after exit
                hasPendingEntryOrder = false;
                pendingEntryDirection = 0;
                pendingEntryStopLevel = 0.0;
                bestPriceForTrailingEntry = 0.0;

                // Reset trade state AFTER logging and updating the indicator
                resetTradeState();

                // --- Reset 2s aggregation state ---
                agg2s_startTimeNanos = 0;
                agg2s_lastCompletedBar = null;
                agg2s_barNeedsReset = true;
                agg2s_bar.startNext(); // Reset the bar object too
                // --- End 2s reset ---
            }
        }
    }

    private void resetTradeState() {
        synchronized (lock) {
            currentPosition = 0;
            entryPrice = 0.0;
            // stopPrice = 0.0; // Retaining for now, potentially used by non-MA strategies
            targetPrice = 0.0;
            currentSizeMultiplier = 1.0;
            highestPrice = Double.MIN_VALUE;
            lowestPrice = Double.MAX_VALUE;
            // useTrailingStop = false; // Will be removed
            // currentTrailingStopDistance = 5.0; // Will be removed
            ma3ConfirmationActive = false;
            // originalStopDistance = 0.0; // Will be removed
            isMaTradeActive = false; // Add this reset

            // Latest MA Triple Stop Levels
            latestMaAdjustedCatStop = Double.NaN;
            latestMaAdjustedTrigStop = Double.NaN;
            latestMaAdjustedWarnStop = Double.NaN;
            // Reset pending exit state as well
            hasPendingExit = false;
            pendingExitDirection = 0;
            pendingExitStopLevel = 0.0;
            bestPriceForTrailingExit = 0.0;
            savedExitTrend = 0;
            pendingExitConfirmationBars = 0;

            // New: Reset MA slope confirmation
            maSlopeConfirmationActive = false;
            // Add this line
            ma2SlopeTrendConfirmationActive = false;
            // --- Additional robust resets ---
            barCounter = 0;
            previousMa1TopHighValue = previousMa1BotLowValue = Double.NaN;
            previousMa2TopHighValue = previousMa2BotLowValue = Double.NaN;
            previousMa3TopHighValue = previousMa3BotLowValue = Double.NaN;
            previousMa4TopHighValue = previousMa4BotLowValue = Double.NaN;
            previousMa5TopHighValue = previousMa5BotLowValue = Double.NaN;
            previousMa1Height = previousMa2Height = previousMa3Height = previousMa4Height = previousMa5Height = Double.NaN;
            latestMa1TopHighValue = latestMa1BotLowValue = Double.NaN;
            latestMa2TopHighValue = latestMa2BotLowValue = Double.NaN;
            latestMa3TopHighValue = latestMa3BotLowValue = Double.NaN;
            latestMa4TopHighValue = latestMa4BotLowValue = Double.NaN;
            latestMa5TopHighValue = latestMa5BotLowValue = Double.NaN;
            topHighStdMax345 = topHighStdMin345 = Double.NaN;
            botLowStdMax345 = botLowStdMin345 = Double.NaN;
            visibilityTrendBiasValue = 0.0;
            maSlopeConfirmationActive = false;
            ma2SlopeTrendConfirmationActive = false;
            visibilityTrendBiasExitSuppressionActive = false;

            // --- Reset 2s aggregation state ---
            agg2s_startTimeNanos = 0;
            agg2s_lastCompletedBar = null;
            agg2s_barNeedsReset = true;
            agg2s_bar.startNext(); // Reset the bar object too
            // --- End 2s reset ---

            lastTradePrice = Double.NaN;
        }
    }

    private void updateTradingIndicators(double currentPrice) {
        synchronized (lock) {
            positionIndicator.addPoint(currentPosition);

            double currentTradePL = 0.0;
            if (currentPosition == 1) {
                currentTradePL = (currentPrice - entryPrice) * 50 * currentSizeMultiplier;
            } else if (currentPosition == -1) {
                currentTradePL = (entryPrice - currentPrice) * 50 * currentSizeMultiplier;
            }
            tradePLIndicator.addPoint(currentTradePL);
            NubiaTotalPLIndicator.addPoint(totalPL);
            ma3ConfirmationIndicator.addPoint(ma3ConfirmationActive ? 1.0 : 0.0);

            // Display total number of completed trades
            totalTradesIndicator.addPoint(tradePLs.size());

            // Add Mini ATR trading indicators
            if (miniAtrTradingModeIndicator != null)
                miniAtrTradingModeIndicator.addPoint((double) useMiniAtrTrendForTrading);
            if (miniAtrTrailingStopIndicator != null)
                miniAtrTrailingStopIndicator
                        .addPoint(miniAtrTrailingStopPrice > 0 ? miniAtrTrailingStopPrice : Double.NaN);

            // --- Always update MA pending entry indicators on every bar ---
            if (pendingEntryStopIndicator != null) {
                if (hasMAPendingEntryOrder) {
                    pendingEntryStopIndicator.addPoint(maPendingEntryStopLevel);
                } else {
                    pendingEntryStopIndicator.addPoint(Double.NaN);
                }
            }
            if (bestPriceIndicator != null) {
                if (hasMAPendingEntryOrder) {
                    bestPriceIndicator.addPoint(maBestPriceForTrailingEntry);
                } else {
                    bestPriceIndicator.addPoint(Double.NaN);
                }
            }

            /*
             * // --- Always update MA pending exit indicators on every bar ---
             * if (pendingExitStopIndicator != null) {
             * if (useMaAtrTrailingStop != null && useMaAtrTrailingStop && currentPosition
             * != 0) {
             * pendingExitStopIndicator.addPoint(stopPrice);
             * } else {
             * pendingExitStopIndicator.addPoint(Double.NaN);
             * }
             * }
             * if (bestExitPriceIndicator != null) {
             * if (useMaAtrTrailingStop != null && useMaAtrTrailingStop && currentPosition
             * != 0) {
             * bestExitPriceIndicator.addPoint(currentPrice);
             * } else {
             * bestExitPriceIndicator.addPoint(Double.NaN);
             * }
             * }
             */

            // Update trailing entry indicators
            if (hasPendingEntryOrder) {
                if (pendingEntryStopIndicator != null)
                    pendingEntryStopIndicator.addPoint(pendingEntryStopLevel);
                if (bestPriceIndicator != null)
                    bestPriceIndicator.addPoint(bestPriceForTrailingEntry);
            } else {
                if (pendingEntryStopIndicator != null)
                    pendingEntryStopIndicator.addPoint(Double.NaN);
                if (bestPriceIndicator != null)
                    bestPriceIndicator.addPoint(Double.NaN);
            }

            // Display pending exit indicators
            if (hasPendingExit) {
                if (pendingExitStopIndicator != null)
                    pendingExitStopIndicator.addPoint(pendingExitStopLevel);
                if (bestExitPriceIndicator != null)
                    bestExitPriceIndicator.addPoint(bestPriceForTrailingExit);
            } else {
                if (pendingExitStopIndicator != null)
                    pendingExitStopIndicator.addPoint(Double.NaN);
                if (bestExitPriceIndicator != null)
                    bestExitPriceIndicator.addPoint(Double.NaN);
            }

            // --- Update MA2 Std Entry Suppression Indicator ---
            if (ma2StdEntrySuppressionIndicator != null) {
                // Show 1 if long suppressed, -1 if short suppressed, 0 otherwise
                ma2StdEntrySuppressionIndicator.addPoint((double) ma2StdEntrySuppressionDirection);
            }

            // --- MA3 Rejection Opportunity Indicator update ---
            if (ma3RejectionOpportunityCountdown > 0) {
                if (ma3RejectionOpportunityIndicator != null)
                    ma3RejectionOpportunityIndicator.addPoint(ma3RejectionOpportunityCountdown);
                ma3RejectionOpportunityCountdown = Math.max(0, ma3RejectionOpportunityCountdown - 5);
            } else {
                if (ma3RejectionOpportunityIndicator != null)
                    ma3RejectionOpportunityIndicator.addPoint(0);
            }

            // --- MA4 Rejection Opportunity Indicator update ---
            if (ma4RejectionOpportunityCountdown > 0) {
                if (ma4RejectionOpportunityIndicator != null)
                    ma4RejectionOpportunityIndicator.addPoint(ma4RejectionOpportunityCountdown);
                ma4RejectionOpportunityCountdown = Math.max(0, ma4RejectionOpportunityCountdown - 5);
            } else {
                if (ma4RejectionOpportunityIndicator != null)
                    ma4RejectionOpportunityIndicator.addPoint(0);
            }

            // --- Update global position and P&L indicators ---
            if (OSCInstance != null) {
                try {
                    int globalPos = OSCInstance.getCurrentPosition();
                    double globalPL = OSCInstance.getCurrentDailyLoss();
                    if (globalPositionIndicator != null)
                        globalPositionIndicator.addPoint(globalPos);
                    if (globalPLIndicator != null)
                        globalPLIndicator.addPoint(globalPL);
                } catch (Exception e) {
                    // Optionally log error
                }
            } else {
                if (globalPositionIndicator != null)
                    globalPositionIndicator.addPoint(Double.NaN);
                if (globalPLIndicator != null)
                    globalPLIndicator.addPoint(Double.NaN);
            }

            // Update global PnL indicator with realized + unrealized PnL
            if (globalPLIndicator != null) {
                double globalTotalPnL = OrderSenderControllerV2.getGlobalTotalPnL();
                globalPLIndicator.addPoint(globalTotalPnL);
            }
        }
    }

    @Override
    public void onTimestamp(long timestamp) {
        synchronized (lock) {
            currentTimestampNanos = timestamp;
            // Propagate Bookmap time to OrderSenderControllerV2 only if OSCInstance is not
            // null
            if (OSCInstance != null) {
                OrderSenderControllerV2.setReferenceTime(currentTimestampNanos);
            }
            receivedFirstTimestamp = true;
            lastProcessedTimestampNanos = timestamp;
            if (stdBands != null) {
                stdBands.updateTime(timestamp);
            }

        }
    }

    @Override
    public void stop() {
        // Always close the NubiaHealthStatusUI safely, regardless of
        // enableHealthMonitoring
        // closeHealthStatusUI(); // Removed - UI disposal handled by last tracker
        // disposing or explicitly when disabling monitoring.
        // Only dispose NubiaHealthStatusUI if health monitoring is enabled (legacy
        // logic, now redundant but left for clarity)
        // if (Boolean.TRUE.equals(enableHealthMonitoring)) {
        Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Starting stop procedure");
        synchronized (lock) {
            // Cleanup OrderSenderControllerV2

            try {
                // Call the enhanced cleanup method which will handle all UI closing
                OrderSenderControllerV2.cleanupAll();
                Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Successfully cleaned up OrderSenderControllerV2");
            } catch (Exception e) {
                Log.error("NubiaAutoMidasAnchoredVWAPV5_3_0: Error during OrderSenderControllerV2 cleanup: "
                        + e.getMessage(), e);
            } finally {
                // Set to null to help garbage collection

            }

            // Log version and performance info when stopping
            Log.info("Stopping Nubia Auto Midas V" + VERSION + ". Total P/L: " + totalPL + ", Number of trades: "
                    + tradePLs.size());

            double avgTradePL = tradePLs.isEmpty() ? 0 : totalPL / tradePLs.size();
            Log.info("Average Trade P/L: " + avgTradePL);

            // Reset calculators
            try {
                if (vwapCalculator != null) {
                    vwapCalculator.reset();
                    Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Reset vwapCalculator");
                }

                // Reset all AVwapMAs instances
                if (mas != null) {
                    for (int i = 0; i < mas.length; i++) {
                        if (mas[i] != null) {
                            mas[i].reset();
                        }
                    }
                    Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Reset all AVwapMAs");
                }

                if (stdBands != null) {
                    stdBands.reset();
                    Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Reset stdBands");
                }

                if (miniAtrBandsCalculator != null) {
                    miniAtrBandsCalculator.reset();
                    Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Reset miniAtrBandsCalculator");
                }

                if (reversalDetector != null) {
                    reversalDetector.reset();
                    Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Reset reversalDetector");
                }

                if (chopZoneCalculator != null) {
                    chopZoneCalculator.reset();
                    Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Reset chopZoneCalculator");
                }
            } catch (Exception e) {
                Log.error("NubiaAutoMidasAnchoredVWAPV5_3_0: Error resetting calculators: " + e.getMessage(), e);
            }

            // Reset data arrays to help garbage collection
            try {
                highPrices = new double[capacity];
                lowPrices = new double[capacity];
                closePrices = new double[capacity];
                volumeValues = new double[capacity];
                cumulativeVolume = new double[capacity];
                cumulativeWgtHigh = new double[capacity];
                cumulativeWgtLow = new double[capacity];
                Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Reset data arrays");
            } catch (Exception e) {
                Log.error("NubiaAutoMidasAnchoredVWAPV5_3_0: Error resetting data arrays: " + e.getMessage(), e);
            }

            // Reset trade tracking
            try {
                tradePLs.clear();
                head = 0;
                size = 0;
                totalPL = 0.0;
                Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Reset trade tracking variables");
            } catch (Exception e) {
                Log.error("NubiaAutoMidasAnchoredVWAPV5_3_0: Error resetting trade variables: " + e.getMessage(), e);
            }

            // Reset Mini ATR trading variables
            try {
                previousMiniAtrTrend = 0;
                miniAtrEntryPrice = 0.0;
                miniAtrTrailingStopPrice = 0.0;
                miniAtrConfirmationCounter = 0;
                miniAtrDataValidated = false; // Reset the validation flag when stopping
                Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Reset Mini ATR trading variables");
            } catch (Exception e) {
                Log.error("NubiaAutoMidasAnchoredVWAPV5_3_0: Error resetting Mini ATR variables: " + e.getMessage(), e);
            }

            // Reset trailing entry variables
            hasPendingEntryOrder = false;
            pendingEntryDirection = 0;
            pendingEntryStopLevel = 0.0;
            bestPriceForTrailingEntry = 0.0;

            // Reset pending exit state
            hasPendingExit = false;
            pendingExitDirection = 0;
            pendingExitStopLevel = 0.0;
            bestPriceForTrailingExit = 0.0;
            savedExitTrend = 0;
            pendingExitConfirmationBars = 0;
            maPendingEntrySetupReasonCode = 0; // Reset MA pending setup reason

            // New: Reset MA slope confirmation
            maSlopeConfirmationActive = false;

            resetTradeState();
            uptrendDuration = 0;
            downtrendDuration = 0;
            strongUptrendDuration = 0;
            strongDowntrendDuration = 0;
            if (chopZoneCalculator != null) {
                chopZoneCalculator.reset();
            }

            // --- Add MASlopeMetrics cleanup ---
            ma2SlopeMetrics.reset();
            ma3SlopeMetrics.reset();
            ma4SlopeMetrics.reset();

            // --- Clear Trailing Stop Controller Cache ---
            try {
                TrailingStopControllerV3.clearCache();
                Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Cleared TrailingStopControllerV3 cache.");
            } catch (Exception e) {
                Log.error("NubiaAutoMidasAnchoredVWAPV5_3_0: Error clearing TrailingStopControllerV3 cache: "
                        + e.getMessage(), e);
            }

            // Clean up indicators by setting them to null
            Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Releasing indicator references");

            // MA indicators
            ma1TopHigh = ma1BotLow = ma2TopHigh = ma2BotLow = ma3TopHigh = ma3BotLow = ma4TopHigh = ma4BotLow = null;
            ma1TopHighDimmed = ma1BotLowDimmed = ma2TopHighDimmed = ma2BotLowDimmed = null;
            ma3TopHighDimmed = ma3BotLowDimmed = ma4TopHighDimmed = ma4BotLowDimmed = null;
            ma1TrendIndicator = ma2TrendIndicator = ma3TrendIndicator = ma4TrendIndicator = null;

            // Position and PL indicators
            positionIndicator = tradePLIndicator = NubiaTotalPLIndicator = ma3ConfirmationIndicator = null;
            uptrendIndicator = downtrendIndicator = strongBullishIndicator = strongBearishIndicator = null;

            // Height indicators
            ma1HeightIndicator = ma2HeightIndicator = ma3HeightIndicator = ma4HeightIndicator = totalTradesIndicator = null;
            // ATR and trend indicators
            upperMiniAtrBandIndicator = lowerMiniAtrBandIndicator = middleMiniAtrBandIndicator = widthMiniAtrBandIndicator = null;
            miniAtrTrendIndicator = miniAtrTrendStrengthIndicator = null;
            miniAtrTradingModeIndicator = miniAtrTrailingStopIndicator = null;

            // Pending entry/exit indicators
            pendingEntryStopIndicator = bestPriceIndicator = null;
            pendingExitStopIndicator = bestExitPriceIndicator = null;

            // Icons indicator
            tradeIconsIndicator = null;

            // Band indicators
            upperBandIndicator = lowerBandIndicator = null;

            // Chop zone indicators
            chopZoneIndicator = chopIntensityIndicator = ma3BandsInsideMiniAtrIndicator = null;
            // --- Nullify Chop Zone Boundary Indicators ---
            chopZoneUpperLimitIndicator = chopZoneLowerLimitIndicator = null;
            // -------------------------------------------

            // Slope indicators
            ma2BotSlopeIndicator = ma3BotSlopeIndicator = ma4BotSlopeIndicator = null;
            ma2TopSlopeIndicator = ma3TopSlopeIndicator = ma4TopSlopeIndicator = null;
            maSlopeDataAvailableIndicator = null;

            // Trend slope indicators
            ma2SlopeTrendIndicator = ma3SlopeTrendIndicator = ma4SlopeTrendIndicator = null;
            ma2SlopeTrendConfirmationIndicator = null;
            maSlopeConfirmationIndicator = null;
            visibilityTrendBiasExitSuppressionIndicator = null;

            // --- Nullify MA2 Std Entry Suppression Indicator ---
            ma2StdEntrySuppressionIndicator = null;

            // Null out all HVN/LVN indicators
            pocIndicator = null;
            valIndicator = null;
            vahIndicator = null;
            hvn1Indicator = hvn2Indicator = hvn3Indicator = hvn4Indicator = hvn5Indicator = null;
            lvn1Indicator = lvn2Indicator = lvn3Indicator = lvn4Indicator = lvn5Indicator = null;

            // --- Null out Independent Triple Stop Indicators and State ---
            independentTripleWarnIndicator = null;
            independentTripleTriggerIndicator = null;
            independentTripleCatIndicator = null;
            independentTripleStopSpec = null;

            // Null out TradeReasonIndicator
            TradeEntryReasonIndicator = null;
            TradeExitReasonIndicator = null;

            Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Stop procedure completed successfully");

        }

        // --- Robust MA reset logic ---
        if (mas != null) {
            for (AVwapMAs ma : mas) {
                if (ma != null) {
                    ma.reset();
                }
            }
        }
        // Reset MA-related primitive state variables
        ma1Trend = ma2Trend = ma3Trend = ma4Trend = ma5Trend = 0;
        ma1TrendSmoothingCounter = ma2TrendSmoothingCounter = ma3TrendSmoothingCounter = ma4TrendSmoothingCounter = ma5TrendSmoothingCounter = 0;
        ma1LastTrend = ma2LastTrend = ma3LastTrend = ma4LastTrend = ma5LastTrend = 0;
        ma2BotSlope = ma3BotSlope = ma4BotSlope = ma5BotSlope = 0.0;
        ma2TopSlope = ma3TopSlope = ma4TopSlope = ma5TopSlope = 0.0;
        ma2Available = ma3Available = ma4Available = ma5Available = false;
        ma3ConfirmationActive = false;
        // Reset opportunity/rejection state
        ma3LastCross = new int[] { 0, -1 };
        ma4LastCross = new int[] { 0, -1 };
        ma3RejectionOpportunityCountdown = 0;
        ma4RejectionOpportunityCountdown = 0;
        ma3RejectionOpportunityInsufficientCountdown = 0;
        ma4RejectionOpportunityInsufficientCountdown = 0;
        // Optionally, reset trade state
        resetTradeState();

        // --- Reset Independent Triple Stop State ---
        independentTripleStopSpec = null; // Spec will be rebuilt on next init if needed

        // --- Reset 2s aggregation state ---
        agg2s_startTimeNanos = 0;
        agg2s_lastCompletedBar = null;
        agg2s_barNeedsReset = true;
        agg2s_bar.startNext(); // Ensure bar is reset
        // --- End 2s reset ---

        // Reset proximity checker state
        if (vpaProximityChecker != null) {
            vpaProximityChecker.reset();
        }
        // Reset proximity checker state
        if (vwapProximityChecker != null) {
            vwapProximityChecker.reset();
        }

        // Explicitly ensure the Health UI is disposed on the EDT
        SwingUtilities.invokeLater(() -> {
            try {
                NubiaHealthStatusUI.disposeInstance();
                Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Explicitly requested Health UI disposal.");
            } catch (Exception e) {
                Log.error(
                        "NubiaAutoMidasAnchoredVWAPV5_3_0: Error during explicit Health UI disposal: " + e.getMessage(),
                        e);
            }
        });

 
            MaindebugUI.disposeInstance();
 
            PerformanceDashboardUI.disposeInstance();
            OrderSenderControllerDebugUI.disposeInstance();

            Log.info("NubiaAutoMidasAnchoredVWAPV5_3_0: Disposed all debug UIs and Shutdowns completed");
      
    }

    /**
     * Updates MA3 confirmation status based on price position relative to MA3 bands
     *
     * MA3 confirmation:
     */
    private boolean isMA3BandsInsideMiniATR() {
        // Use the miniAtrBandsCalculator to check the condition
        if (miniAtrBandsCalculator == null || !miniAtrBandsCalculator.isReady()) {
            return false;
        }

        // Return the result from the calculator
        return miniAtrBandsCalculator.isMA3InsideMiniATR();
    }

    /**
     * Updates MA3 confirmation status based on price position relative to MA3 bands
     *
     * MA3 confirmation:
     * 1. Activates when price is correctly positioned relative to MA3 bands
     * - For longs: price > both MA3 bands
     * - For shorts: price < both MA3 bands
     * 2. When active, suppresses regular exit signals (prevents premature exits)
     * 3. Only catastrophic stops will override an active MA3 confirmation
     * 
     * @param currentPrice The current market price
     */
    private void updateMA3Confirmation(double currentPrice) {
        synchronized (lock) {
            // Respect the parameter toggle
            if (!useMa3ConfirmationExitSuppression) {
                if (ma3ConfirmationActive) {
                    ma3ConfirmationActive = false;
                    Log.info("MA3 confirmation DISABLED by parameter.");
                }
                return;
            }

            double ma3Top = mas[2].topHighValue;
            double ma3Bot = mas[2].botLowValue;

            boolean wasConfirmationActive = ma3ConfirmationActive;

            // Check if price confirms position direction
            boolean ma3ConfirmsLong = !Double.isNaN(ma3Top) && !Double.isNaN(ma3Bot) &&
                    currentPrice > ma3Top && currentPrice > ma3Bot;
            boolean ma3ConfirmsShort = !Double.isNaN(ma3Top) && !Double.isNaN(ma3Bot) &&
                    currentPrice < ma3Top && currentPrice < ma3Bot;

            // Update MA3 confirmation active flag
            if ((currentPosition == 1 && ma3ConfirmsLong) ||
                    (currentPosition == -1 && ma3ConfirmsShort)) {
                if (!ma3ConfirmationActive) {
                    ma3ConfirmationActive = true;

                    Log.info("MA3 confirmation ACTIVATED - suppressing regular exits (catastrophic stops still apply)");
                }
            } else if (ma3ConfirmationActive) {
                ma3ConfirmationActive = false;

                // Reset stop levels when confirmation ends
                if (currentPosition == 1) {
                    stopPrice = Math.max(stopPrice, currentPrice - currentTrailingStopDistance);
                    Log.info("MA3 confirmation ENDED - reset long stop to: " + stopPrice);
                } else if (currentPosition == -1) {
                    stopPrice = Math.min(stopPrice, currentPrice + currentTrailingStopDistance);
                    Log.info("MA3 confirmation ENDED - reset short stop to: " + stopPrice);
                }
            }

            // If confirmation state changed, explain why
            if (wasConfirmationActive != ma3ConfirmationActive) {
                if (!ma3ConfirmsLong && currentPosition == 1) {
                    Log.info("MA3 confirmation disabled for long - price (" + currentPrice +
                            ") not above both MA3 bands (top=" + ma3Top + ", bot=" + ma3Bot + ")");
                } else if (!ma3ConfirmsShort && currentPosition == -1) {
                    Log.info("MA3 confirmation disabled for short - price (" + currentPrice +
                            ") not below both MA3 bands (top=" + ma3Top + ", bot=" + ma3Bot + ")");
                }
            }
        }
    }

    /**
     * Updates MA slope confirmation status based on the slopes of MA2Bot, MA3Bot,
     * and MA4Bot (if available)
     * Suppresses exits for long positions if slopes are non-negative, and for short
     * positions if slopes are non-positive
     * 
     * @param currentPrice The current market price
     */
    private void updateMASlopeConfirmation(double currentPrice) {
        synchronized (lock) {
            // Skip calculations if current price is invalid
            if (Double.isNaN(currentPrice) || Double.isInfinite(currentPrice)) {
                // Still add a point to the indicator to maintain synchronization
                // maSlopeConfirmationIndicator.addPoint(Double.NaN);
                return;
            }

            boolean wasSlopeConfirmationActive = maSlopeConfirmationActive;

            // Determine trend based on pre-computed slopes
            boolean positiveTrend = false;
            boolean negativeTrend = false;

            boolean enoughData = mas[1].available || mas[2].available || mas[3].available;

            if (enoughData) {
                int positiveCount = 0, negativeCount = 0, totalCount = 0;

                if (mas[1].available) {
                    if (mas[1].botSlope > 0)
                        positiveCount++; // Bullish: bottom line rising
                    if (mas[1].topSlope < 0)
                        negativeCount++; // Bearish: top line falling
                    totalCount++;
                }

                if (mas[2].available) {
                    if (mas[2].botSlope > 0)
                        positiveCount++; // Bullish: bottom line rising
                    if (mas[2].topSlope < 0)
                        negativeCount++; // Bearish: top line falling
                    totalCount++;
                }

                if (mas[3].available) {
                    if (mas[3].botSlope > 0)
                        positiveCount++; // Bullish: bottom line rising
                    if (mas[3].topSlope < 0)
                        negativeCount++; // Bearish: top line falling
                    totalCount++;
                }

                if (totalCount > 0) {
                    // If we have more positive than negative slopes, it's a positive trend
                    positiveTrend = positiveCount > negativeCount;
                    // If we have more negative than positive slopes, it's a negative trend
                    negativeTrend = negativeCount > positiveCount;

                    // In case of a tie, favor the current position
                    if (positiveCount == negativeCount) {
                        positiveTrend = currentPosition == 1;
                        negativeTrend = currentPosition == -1;
                    }
                }

                // Update suppression flag based on position and trend
                if (currentPosition == 1 && positiveTrend) {
                    if (!maSlopeConfirmationActive) {
                        maSlopeConfirmationActive = true;
                        if (debugLogging) {
                            Log.info("MA Slope confirmation ACTIVATED for LONG" +
                                    " (pos=" + positiveCount + "/" + totalCount +
                                    ", neg=" + negativeCount + "/" + totalCount + ")");
                        }
                    }
                } else if (currentPosition == -1 && negativeTrend) {
                    if (!maSlopeConfirmationActive) {
                        maSlopeConfirmationActive = true;
                        if (debugLogging) {
                            Log.info("MA Slope confirmation ACTIVATED for SHORT" +
                                    " (pos=" + positiveCount + "/" + totalCount +
                                    ", neg=" + negativeCount + "/" + totalCount + ")");
                        }
                    }
                } else if (maSlopeConfirmationActive) {
                    maSlopeConfirmationActive = false;
                    if (debugLogging) {
                        Log.info("MA Slope confirmation DEACTIVATED");
                    }
                }
            }

            // Always update indicator - even if we don't have enough data, show 0
            double indicatorValue = 0.0;
            if (maSlopeConfirmationActive && enoughData) {
                indicatorValue = currentPosition == 1 ? 2.0 : -2.0;
            }

            if (Boolean.TRUE.equals(enableDevelopmentIndicators)) {
                maSlopeConfirmationIndicator.addPoint(indicatorValue);
            }
        }
    }

    // Add this new method to call the detector and update indicators
    private void updateMASlopeTrends() {
        // Use encapsulated MASlopeMetrics trend state from AVwapMAs objects
        ma2SlopeTrend = mas[1].slopeMetrics.getCurrentTrend();
        if (Boolean.TRUE.equals(enableDevelopmentIndicators) && ma2SlopeTrendIndicator != null) {
            ma2SlopeTrendIndicator.addPoint(ma2SlopeTrend);
        }

        ma3SlopeTrend = mas[2].slopeMetrics.getCurrentTrend();
        if (Boolean.TRUE.equals(enableDevelopmentIndicators) && ma3SlopeTrendIndicator != null) {
            ma3SlopeTrendIndicator.addPoint(ma3SlopeTrend);
        }

        ma4SlopeTrend = mas[3].slopeMetrics.getCurrentTrend();
        if (Boolean.TRUE.equals(enableDevelopmentIndicators) && ma4SlopeTrendIndicator != null) {
            ma4SlopeTrendIndicator.addPoint(ma4SlopeTrend);
        }

        if (debugLogging) {
            Log.info("MA Slope Trends (from AVwapMAs): MA2=" + ma2SlopeTrend + ", MA3=" + ma3SlopeTrend +
                    ", MA4=" + ma4SlopeTrend);
        }
    }

    /**
     * Updates MA2 Slope Trend confirmation status based on alignment between
     * position direction and MA2 slope trend
     * When active, suppresses exits similar to MA3 confirmation
     * 
     * @param currentPrice The current market price
     */
    private void updateMA2SlopeTrendConfirmation(double currentPrice) {
        synchronized (lock) {
            // Diagnostic: check if method is called and indicator is initialized
            if (ma2SlopeTrendConfirmationIndicator == null) {
                Log.error(
                        "ma2SlopeTrendConfirmationIndicator is NULL! Check initializeIndicators() for registration issues.");
                return;
            }
            if (true) {
                Log.info("updateMA2SlopeTrendConfirmation called. MA2 Slope Trend: " + ma2SlopeTrend + ", Position: "
                        + currentPosition);
            }

            boolean wasConfirmationActive = ma2SlopeTrendConfirmationActive;

            if (debugLogging) {
                Log.info("MA2 Slope Trend: " + ma2SlopeTrend + ", Position: " + currentPosition);
            }

            // Check if MA2 slope trend confirms position direction
            boolean trendConfirmsLong = (currentPosition == 1 && ma2SlopeTrend == 1);
            boolean trendConfirmsShort = (currentPosition == -1 && ma2SlopeTrend == -1);

            // Update confirmation active flag
            if (trendConfirmsLong || trendConfirmsShort) {
                if (!ma2SlopeTrendConfirmationActive) {
                    ma2SlopeTrendConfirmationActive = true;

                    Log.info("MA2 Slope Trend confirmation ACTIVATED - suppressing exits " +
                            "(MA2 Slope Trend = " + ma2SlopeTrend + ", Position = " + currentPosition + ")");
                }
            } else if (ma2SlopeTrendConfirmationActive) {
                ma2SlopeTrendConfirmationActive = false;

                Log.info("MA2 Slope Trend confirmation ENDED - MA2 Slope Trend = " + ma2SlopeTrend +
                        " no longer confirms position = " + currentPosition);
            }

            // Set indicator value based on different states
            double indicatorValue;

            if (ma2SlopeTrendConfirmationActive) {
                // When confirmation is active (suppressing exits) - value 1.0
                indicatorValue = 1.0;
            } else if (currentPosition != 0) {
                // Position exists but no confirmation - value 0.3
                indicatorValue = 0.3;
            } else if (ma2SlopeTrend != 0) {
                // No position, but trend exists - show trend direction scaled
                indicatorValue = ma2SlopeTrend * 0.5;
            } else {
                // No trend, no position - zero
                indicatorValue = 0.0;
            }

            // Always update the indicator
            ma2SlopeTrendConfirmationIndicator.addPoint(indicatorValue);

            // Diagnostic: log the value being added
            if (debugLogging) {
                Log.info("ma2SlopeTrendConfirmationIndicator.addPoint(" + indicatorValue + ")");
            }
        }
    }

    /** Utility: Checks if a price is invalid (NaN or infinite) */
    private static boolean isInvalidPrice(double price) {
        return Double.isNaN(price) || Double.isInfinite(price);
    }

    /**
     * Updates the exit suppression status based on visibilityTrendBias
     * - For long positions: suppresses exits when visibilityTrendBias > 0.4
     * (strongly bullish)
     * - For short positions: suppresses exits when visibilityTrendBias < -0.4
     * (strongly bearish)
     * 
     * @param currentPrice The current market price
     */
    private void updateVisibilityTrendBiasExitSuppression(double currentPrice) {
        // MANDATORY DEBUG LINE - Always log this to confirm method execution
        if (debugLogging && !disableAllLogging)
            Log.info("VISIBILITY EXIT SUPPRESSION METHOD START: price=" + currentPrice +
                    ", bias=" + visibilityTrendBiasValue +
                    ", position=" + currentPosition);
        synchronized (lock) {
            // Always log the current state regardless of debug setting
            if (debugLogging && !disableAllLogging)
                Log.info("VisibilityTrendBias Exit Suppression Check: value=" + visibilityTrendBiasValue +
                        ", position=" + currentPosition +
                        ", parameter=" + useVisibilityTrendBiasExitSuppression +
                        ", isActive=" + visibilityTrendBiasExitSuppressionActive);

            // Skip calculations if current price is invalid or feature is disabled
            if (Double.isNaN(currentPrice) || Double.isInfinite(currentPrice)
                    || !useVisibilityTrendBiasExitSuppression) {
                // If feature is disabled but was previously active, reset the state
                if (visibilityTrendBiasExitSuppressionActive) {
                    visibilityTrendBiasExitSuppressionActive = false;

                    if (debugLogging && !disableAllLogging)
                        Log.info("Visibility Trend Bias exit suppression ENDED - feature disabled by parameter");
                }

                // Still update the indicator with 0 to maintain synchronization
                visibilityTrendBiasExitSuppressionIndicator.addPoint(0.0);
                return;
            }

            boolean wasSuppressionActive = visibilityTrendBiasExitSuppressionActive;

            // Configure thresholds for strong bias - LOWERED THRESHOLDS
            double strongBullishThreshold = 0.4; // Changed from 0.75 to match debug logging
            double strongBearishThreshold = -0.4; // Changed from -0.75 to match debug logging

            // Force activation if bias is extremely strong (>0.9)
            if (currentPosition == 1 && visibilityTrendBiasValue > 0.9) {
                visibilityTrendBiasExitSuppressionActive = true;

                if (debugLogging && !disableAllLogging)
                    Log.info("FORCING Visibility Trend Bias exit suppression ACTIVATED - very strong bias: "
                            + visibilityTrendBiasValue);

                // Set indicator value for active suppression
                visibilityTrendBiasExitSuppressionIndicator.addPoint(1.0);
                return;
            }

            // Check if visibilityTrendBias confirms the current position
            boolean biasConfirmsLong = (currentPosition == 1 && visibilityTrendBiasValue > strongBullishThreshold);
            boolean biasConfirmsShort = (currentPosition == -1 && visibilityTrendBiasValue < strongBearishThreshold);

            // Always log this critical check
            if (debugLogging && !disableAllLogging)
                Log.info("Bias confirmation check: biasConfirmsLong=" + biasConfirmsLong +
                        ", biasConfirmsShort=" + biasConfirmsShort +
                        ", bias=" + visibilityTrendBiasValue +
                        ", threshold=" + strongBullishThreshold);

            // Update suppression active flag
            if (biasConfirmsLong || biasConfirmsShort) {
                if (!visibilityTrendBiasExitSuppressionActive) {
                    visibilityTrendBiasExitSuppressionActive = true;

                    if (debugLogging && !disableAllLogging)
                        Log.info("Visibility Trend Bias exit suppression ACTIVATED - suppressing exits " +
                                "(Bias = " + String.format("%.3f", visibilityTrendBiasValue) +
                                ", Position = " + currentPosition + ")");
                } else {
                    // Already active, just log
                    if (debugLogging && !disableAllLogging)
                        Log.info("Visibility Trend Bias exit suppression remains ACTIVE");
                }
            } else if (visibilityTrendBiasExitSuppressionActive) {
                visibilityTrendBiasExitSuppressionActive = false;

                if (debugLogging && !disableAllLogging)
                    Log.info("Visibility Trend Bias exit suppression ENDED - Bias = " +
                            String.format("%.3f", visibilityTrendBiasValue) +
                            " no longer strongly confirms position = " + currentPosition);
            }

            // Set indicator value based on different states
            double indicatorValue;

            if (visibilityTrendBiasExitSuppressionActive) {
                // When suppression is active (suppressing exits) - value 1.0
                indicatorValue = 1.0;
            } else if (currentPosition != 0) {
                // Position exists but no suppression - value 0.3
                indicatorValue = 0.3;
            } else if (Math.abs(visibilityTrendBiasValue) > 0.5) {
                // No position, but strong bias exists - show bias direction scaled
                indicatorValue = (visibilityTrendBiasValue > 0) ? 0.5 : -0.5;
            } else {
                // No strong bias, no position - zero
                indicatorValue = 0.0;
            }

            // Always update the indicator
            visibilityTrendBiasExitSuppressionIndicator.addPoint(indicatorValue);

            // Log final state at end of method
            if (debugLogging && !disableAllLogging)
                Log.info("Final exit suppression state: " + visibilityTrendBiasExitSuppressionActive +
                        ", indicator value=" + indicatorValue);
        }
    }

    /**
     * Updates MA2 Std entry suppression flag for MiniATR trading.
     * Suppresses only "wrong-way" entries:
     * - Suppress LONG entry if price < MA2 Top AND price < MA2 Top Std
     * - Suppress SHORT entry if price > MA2 Bot AND price > MA2 Bot Std
     * Sets suppression flag only if above is true for entry direction.
     * 
     * @return int 0 if no suppression, 1 if long entry should be suppressed, -1 if
     *         short entry should be suppressed.
     */
    private int updateMA2StdEntrySuppression(double currentPrice) {
        synchronized (lock) {
            // Reset suppression state at the start of each update
            ma2StdEntrySuppressionActive = false;
            ma2StdEntrySuppressionDirection = 0;

            // Check if the feature is enabled
            if (!enableMa2StdEntrySuppression || mas == null || mas.length <= 1 || mas[1] == null
                    || !mas[1].available) {
                if (ma2StdEntrySuppressionIndicator != null) {
                    ma2StdEntrySuppressionIndicator.addPoint(Double.NaN); // Add 0 if disabled or data not ready
                }
                return 0; // No suppression if disabled or MA2 data not available
            }

            double ma2Top = mas[1].topHighValue;
            double ma2Bot = mas[1].botLowValue;
            double ma2TopStd = mas[1].getLastTopHighStdValue();
            double ma2BotStd = mas[1].getLastBotLowStdValue();

            boolean ma2DataValid = !isInvalidPrice(ma2Top) && !isInvalidPrice(ma2Bot)
                    && !isInvalidPrice(ma2TopStd) && !isInvalidPrice(ma2BotStd);

            if (!ma2DataValid) {
                if (ma2StdEntrySuppressionIndicator != null) {
                    ma2StdEntrySuppressionIndicator.addPoint(0.0); // Add 0 if data is invalid
                }
                return 0; // No suppression if data is invalid
            }

            // Check suppression conditions
            boolean suppressLongCondition = (currentPrice < ma2Top && currentPrice < ma2TopStd);
            boolean suppressShortCondition = (currentPrice > ma2Bot && currentPrice > ma2BotStd);

            // Determine suppression direction
            if (suppressLongCondition) {
                ma2StdEntrySuppressionActive = true;
                ma2StdEntrySuppressionDirection = 1; // Suppress Long
                if (debugLogging)
                    Log.info("MA2 Std Suppression ACTIVE for LONG: Price=" + currentPrice + " < MA2Top=" + ma2Top
                            + " AND Price=" + currentPrice + " < MA2TopStd=" + ma2TopStd);
            } else if (suppressShortCondition) {
                ma2StdEntrySuppressionActive = true;
                ma2StdEntrySuppressionDirection = -1; // Suppress Short
                if (debugLogging)
                    Log.info("MA2 Std Suppression ACTIVE for SHORT: Price=" + currentPrice + " > MA2Bot=" + ma2Bot
                            + " AND Price=" + currentPrice + " > MA2BotStd=" + ma2BotStd);
            } else {
                ma2StdEntrySuppressionActive = false;
                ma2StdEntrySuppressionDirection = 0; // No suppression
            }

            // Update the indicator
            if (ma2StdEntrySuppressionIndicator != null) {
                ma2StdEntrySuppressionIndicator.addPoint((double) ma2StdEntrySuppressionDirection);
            }

            return ma2StdEntrySuppressionDirection;
        }
    }

    /**
     * Checks for MA rejection opportunity and updates state.
     * 
     * @param ma           AVwapMAs instance (MA3 or MA4)
     * @param prevClose    previous bar close
     * @param currClose    current bar close
     * @param currBarIdx   current bar index
     * @param hysteresis   hysteresis in price units
     * @param lastCross    int[2]: [0]=last cross dir, [1]=bar idx
     * @param lookbackBars lookback window for cross
     * @param vwap         current VWAP value
     * @return 1 for long entry (rejection from bottom), -1 for short entry
     *         (rejection from top), 0 for none
     */
    private int checkMARejectionOpportunity(AVwapMAs ma, double prevClose, double currClose, int currBarIdx,
            double hysteresis, int[] lastCross, int lookbackBars, double vwap) {
        // --- DEBUG LOGGING: All relevant variables on every call ---
        sendtologDebugUI("checkMARejectionOpportunity: prevClose=" + prevClose + ", currClose=" + currClose +
                ", botLow=" + (ma != null ? ma.botLowValue : "null") + ", topHigh="
                + (ma != null ? ma.topHighValue : "null") +
                ", botVisible=" + (ma != null ? ma.isBotVisible() : "null") + ", topVisible="
                + (ma != null ? ma.isTopVisible() : "null") +
                ", lastCross=" + lastCross[0] + "," + lastCross[1] + ", currBarIdx=" + currBarIdx + ", hysteresis="
                + hysteresis + ", lookbackBars=" + lookbackBars + ", vwap=" + vwap + ", minDistanceToVWAPPips="
                + minDistanceToVWAPPips + ", maxBandToPricePips=" + maxBandToPricePips);

        if (ma == null || !ma.available || Double.isNaN(ma.topHighValue) || Double.isNaN(ma.botLowValue))
            return 0;
        int triggered = 0;

        // --- Long rejection logic ---
        boolean longVWAPFar = Math.abs(ma.botLowValue - vwap) >= minDistanceToVWAPPips;
        boolean longPriceNearBand = Math.abs(currClose - ma.botLowValue) < maxBandToPricePips;
        sendtologDebugUI("Long rejection: |botLow - vwap|=" + Math.abs(ma.botLowValue - vwap) + " (threshold="
                + minDistanceToVWAPPips + "), |currClose - botLow|=" + Math.abs(currClose - ma.botLowValue)
                + " (threshold=" + maxBandToPricePips + ")");
        if (!longVWAPFar) {
            sendtologDebugUI("Long rejection suppressed: botLow too close to VWAP");
        }
        if (!longPriceNearBand) {
            sendtologDebugUI("Long rejection suppressed: price too far from botLow");
        }
        if (prevClose <= ma.botLowValue && ma.getBotInvisibleBars() < 5) {
            lastCross[0] = -1;
            lastCross[1] = currBarIdx;
            sendtologDebugUI("Crossed below botLow: lastCross set to -1 at bar " + currBarIdx);
        }
        if (lastCross[0] == -1 && prevClose <= ma.botLowValue && currClose >= ma.botLowValue + hysteresis) {
            if (longVWAPFar && longPriceNearBand) {
                if (currBarIdx >= lastCross[1] && currBarIdx - lastCross[1] <= lookbackBars) {
                    triggered = 1;
                    sendtologDebugUI("Long opportunity triggered at bar " + currBarIdx);
                }
                lastCross[0] = 1;
                lastCross[1] = currBarIdx;
                sendtologDebugUI("lastCross set to 1 at bar " + currBarIdx);
            } else {
                sendtologDebugUI("Long opportunity NOT triggered due to VWAP/band proximity");
            }
        }
        // --- VWAP filter for long ---
        if (triggered == 1 && currClose >= vwap) {
            sendtologDebugUI("Long opportunity suppressed: currClose >= VWAP (" + currClose + " >= " + vwap + ")");
            triggered = 0;
        }
        // --- Short rejection logic ---
        boolean shortVWAPFar = Math.abs(ma.topHighValue - vwap) >= minDistanceToVWAPPips;
        boolean shortPriceNearBand = Math.abs(currClose - ma.topHighValue) < maxBandToPricePips;
        sendtologDebugUI("Short rejection: |topHigh - vwap|=" + Math.abs(ma.topHighValue - vwap) + " (threshold="
                + minDistanceToVWAPPips + "), |currClose - topHigh|=" + Math.abs(currClose - ma.topHighValue)
                + " (threshold=" + maxBandToPricePips + ")");
        if (!shortVWAPFar) {
            sendtologDebugUI("Short rejection suppressed: topHigh too close to VWAP");
        }
        if (!shortPriceNearBand) {
            sendtologDebugUI("Short rejection suppressed: price too far from topHigh");
        }
        if (prevClose >= ma.topHighValue && ma.getTopInvisibleBars() < 5) {
            lastCross[0] = 1;
            lastCross[1] = currBarIdx;
            sendtologDebugUI("Crossed above topHigh: lastCross set to 1 at bar " + currBarIdx);
        }
        if (lastCross[0] == 1 && prevClose >= ma.topHighValue && currClose <= ma.topHighValue - hysteresis) {
            if (shortVWAPFar && shortPriceNearBand) {
                if (currBarIdx >= lastCross[1] && currBarIdx - lastCross[1] <= lookbackBars) {
                    triggered = -1;
                    sendtologDebugUI("Short opportunity triggered at bar " + currBarIdx);
                }
                lastCross[0] = -1;
                lastCross[1] = currBarIdx;
                sendtologDebugUI("lastCross set to -1 at bar " + currBarIdx);
            } else {
                sendtologDebugUI("Short opportunity NOT triggered due to VWAP/band proximity");
            }
        }
        // --- VWAP filter for short ---
        if (triggered == -1 && currClose <= vwap) {
            sendtologDebugUI("Short opportunity suppressed: currClose <= VWAP (" + currClose + " <= " + vwap + ")");
            triggered = 0;
        }
        return triggered;
    }

    private void updateVisibilityIndicators() {
        synchronized (lock) {
            // Update for MA3 to MA5 only (indices 2, 3, 4 in mas array)
            if (Boolean.TRUE.equals(enableDevelopmentIndicators)) {
                for (int i = 2; i < mas.length; i++) {
                    if (mas[i] != null && mas[i].available) {
                        // Get indicators based on MA index
                        Indicator topVisibleInd = i == 2 ? ma3TopVisibleBarsIndicator
                                : i == 3 ? ma4TopVisibleBarsIndicator : ma5TopVisibleBarsIndicator;
                        Indicator botVisibleInd = i == 2 ? ma3BotVisibleBarsIndicator
                                : i == 3 ? ma4BotVisibleBarsIndicator : ma5BotVisibleBarsIndicator;
                        Indicator topInvisibleInd = i == 2 ? ma3TopInvisibleBarsIndicator
                                : i == 3 ? ma4TopInvisibleBarsIndicator : ma5TopInvisibleBarsIndicator;
                        Indicator botInvisibleInd = i == 2 ? ma3BotInvisibleBarsIndicator
                                : i == 3 ? ma4BotInvisibleBarsIndicator : ma5BotInvisibleBarsIndicator;

                        // Add points to indicators - using negative values for invisible to separate on
                        // chart
                        if (topVisibleInd != null)
                            topVisibleInd.addPoint(mas[i].isTopVisible() ? mas[i].getTopVisibleBars() : 0);
                        if (botVisibleInd != null)
                            botVisibleInd.addPoint(mas[i].isBotVisible() ? mas[i].getBotVisibleBars() : 0);
                        if (topInvisibleInd != null)
                            topInvisibleInd.addPoint(!mas[i].isTopVisible() ? mas[i].getTopInvisibleBars() : 0);
                        if (botInvisibleInd != null)
                            botInvisibleInd.addPoint(!mas[i].isBotVisible() ? mas[i].getBotInvisibleBars() : 0);
                    }
                }
            }
        }
    }

    @Override
    public void onRealtimeStart() {
        NowRealtime = true;

        // System.runFinalization();
        OSCInstance.playtradealert(NowRealtime, "Realtime", alias);

        OrderSenderControllerV2.setRealtimeStatus(true); // Add this line
    }

    // Performance optimization: Cache time prefix strings to reduce overhead in hot paths
    private static volatile long cachedNanoseconds = -1;
    private static volatile String cachedTimePrefix = null;
    private static final String LOG_PREFIX = "NubiaAutoMidasAnchoredVWAPV5_3_0: ";
    
    // Pre-computed constants for hot path optimizations
    private static final double NANOS_TO_SECONDS = 1_000_000_000.0;
    private static final double HALF_DIVISOR = 0.5;
    private static final int BID_ASK_DIFF_THRESHOLD = 15;
    private static final int ABNORMAL_PRICE_JUMP_TICK_LIMIT = 25;
    private static final double TIME_DIFF_THRESHOLD = -0.001;
    private static final double TIME_DIFF_FAST_THRESHOLD = 0.1;
    private static final int REENGAGE_TIME_FAST = 150;
    private static final int REENGAGE_TIME_SLOW = 300;
    private static final int SESSION_START_BUFFER_BARS = 60;
    private static final double ABNORMAL_THRESHOLD_OFFSET = 2.0;
    
    // Cache for frequently used Boolean checks to avoid repeated Boolean.TRUE.equals() calls
    private volatile boolean abnormalDetectionEnabled = false;

    /**
     * Optimized time prefix generation with caching to reduce string allocation overhead.
     * Thread-safe implementation using volatile fields.
     */
    private static String getTimePrefixOptimized(long nanoseconds) {
        if (cachedNanoseconds != nanoseconds || cachedTimePrefix == null) {
            cachedTimePrefix = "[" + formatNanosecondsToTime(nanoseconds) + "] ";
            cachedNanoseconds = nanoseconds;
        }
        return cachedTimePrefix;
    }

    // Add a utility method to convert nanoseconds to "HH:mm:ss.SSS"
    /**
     * Converts the given nanoseconds (since epoch) to a formatted time string
     * "HH:mm:ss.SSS".
     * If input is 0 or negative, returns "00:00:00.000".
     */
    public static String formatNanosecondsToTime(long nanoseconds) {
        if (nanoseconds <= 0) {
            return "00:00:00.000";
        }
        long millis = nanoseconds / 1_000_000L;
        java.time.Instant instant = java.time.Instant.ofEpochMilli(millis);
        java.time.ZoneId zone = java.time.ZoneId.systemDefault();
        java.time.LocalTime time = instant.atZone(zone).toLocalTime();
        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss.SSS");
        return time.format(formatter);
    }

    // Add a utility method to convert nanoseconds to "HH:mm:ss.SSSSSS"
    /**
     * Converts the given nanoseconds (since epoch) to a formatted time string
     * "HH:mm:ss.SSS".
     * If input is 0 or negative, returns "00:00:00.000".
     */
    public static String formatNanosecondsToTimenanos(long nanoseconds) {
        if (nanoseconds <= 0) {
            return "00:00:00.000000";
        }
        long millis = nanoseconds / 1_000_000L;
        java.time.Instant instant = java.time.Instant.ofEpochMilli(millis);
        java.time.ZoneId zone = java.time.ZoneId.systemDefault();
        java.time.LocalTime time = instant.atZone(zone).toLocalTime();
        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss.SSSSSS");
        return time.format(formatter);
    }

    // Optimize the sendtolog method to reduce overhead
    public void sendtologDebugUI(String S, float F, String S1) {
        if (debugLogging) {
            StringBuilder sb = new StringBuilder(128);
            sb.append(getTimePrefixOptimized(currentTimestampNanos))
              .append(LOG_PREFIX)
              .append(S)
              .append(" ")
              .append(F)
              .append(" ")
              .append(S1);
            MaindebugUI.log(sb.toString());
        }
    }

    public void sendtologDebugUI(String S) {
        if (debugLogging) {
            StringBuilder sb = new StringBuilder(96);
            sb.append(getTimePrefixOptimized(currentTimestampNanos))
              .append(LOG_PREFIX)
              .append(S);
            MaindebugUI.log(sb.toString());
        }
    }

    public void sendtologDebugUIALWAYS(String S) {
        if (true) {
            StringBuilder sb = new StringBuilder(96);
            sb.append("[")
              .append(formatNanosecondsToTimenanos(currentTimestampNanos))
              .append("] ")
              .append(LOG_PREFIX)
              .append(S);
            MaindebugUI.log(sb.toString());
        }
    }

    // === ATR-based trailing stop for MA system ===
    private double getMaAtrTrailingDistance() {
        if (maAtrTrailingStopLookback == null || maAtrTrailingStopFactor == null) {
            Log.warn("MA ATR Trailing Stop parameters not initialized. Using default 0.");
            return 0.0; // Or some default/fallback
        }
        return calculateATR(maAtrTrailingStopLookback) * maAtrTrailingStopFactor;
    }

    // === MA trailing exit logic (REPLACED BY executeMaTripleStopExitLogic) ===
    private void updateMaTrailingExit(double high, double low, double close) {
        // This method is no longer used for MA trade exits.
        // Its logic relied on parameters/fields that have been removed
        // (maTrailingPosition, maTrailingStopSpec).
        // We keep the method signature but ensure it doesn't cause errors and clears
        // related indicators.

        // Clear any indicators that might have been plotted by the old logic
        if (pendingExitStopIndicator != null) {
            pendingExitStopIndicator.addPoint(Double.NaN);
        }
        if (bestExitPriceIndicator != null) {
            bestExitPriceIndicator.addPoint(Double.NaN);
        }

        // Original logic commented out to prevent errors:
        /*
         * if (useTrailingStop != null && useTrailingStop && maTrailingPosition != null
         * && maTrailingStopSpec != null
         * && currentPosition != 0) {
         * TrailingStopControllerV3.CandleBar bar = new
         * TrailingStopControllerV3.CandleBar(high, low, close);
         * double newStop = TrailingStopControllerV3.getStopPrice(maTrailingPosition,
         * bar, maTrailingStopSpec);
         * if (!Double.isNaN(newStop)) {
         * stopPrice = newStop;
         * if (pendingExitStopIndicator != null)
         * pendingExitStopIndicator.addPoint(stopPrice);
         * if (bestExitPriceIndicator != null)
         * bestExitPriceIndicator.addPoint(close);
         * if (debugLogging)
         * Log.info("[MA TrailingStopControllerV3 Exit] Updated stop to " + stopPrice);
         * } else {
         * // Even if stop not updated, keep indicators in sync
         * if (pendingExitStopIndicator != null)
         * pendingExitStopIndicator.addPoint(stopPrice); // Might plot NaN if newStop
         * was NaN
         * if (bestExitPriceIndicator != null)
         * bestExitPriceIndicator.addPoint(close);
         * }
         * } else {
         * // Not in position or trailing stop not enabled: clear indicators
         * if (pendingExitStopIndicator != null)
         * pendingExitStopIndicator.addPoint(Double.NaN);
         * if (bestExitPriceIndicator != null)
         * bestExitPriceIndicator.addPoint(Double.NaN);
         * }
         */
    }

    private double lastAbnormalPriceJumpIndicatorValue = Double.NaN;
    private double lastDiff = Double.NaN;
    private long lasttradetimestampNanos = 0;

    /**
     * Helper method to add a point to abnormalPriceJumpIndicator only if the new
     * value is different
     * from the last recorded value or if it's the first update.
     * This method assumes abnormalPriceJumpIndicator is not null when called.
     *
     * @param newValue the value to potentially add to the indicator (e.g., 0.0 or
     *                 1.0)
     */
    private void updateAbnormalConditions(double newValue) {
        if (!Double.isNaN(lastDiff) || (lastDiff != newValue && (newValue > 1.5 || lastDiff > 2))) {
            // abnormalPriceJumpIndicator is checked for null by the calling logic
            if (abnormalPriceJumpIndicator != null)
                abnormalPriceJumpIndicator.addPoint(newValue);
            lastAbnormalPriceJumpIndicatorValue = newValue;
        }
    }

    int lastBBOBidPrice = 0;
    int lastBBOAskPrice = 0;
    double lastBBOPrice = 0;
    double bidaskdiff = 0;

    @Override
    public void onBbo(int bidPrice, int bidSize, int askPrice, int askSize) {
        if (bidPrice <= 0 || askPrice <= 0) {
            return;
        }

        OSCInstance.onBbo(bidPrice, bidSize, askPrice, askSize);
        OSCInstanceMain.onBbo(bidPrice, bidSize, askPrice, askSize);

        // Early return optimization: avoid calculations if BBO hasn't changed
        if (bidPrice == lastBBOBidPrice && askPrice == lastBBOAskPrice) {
            return;
        }

        // Optimized calculations using bit shifting and pre-computed constants
        double price = (bidPrice + askPrice) * HALF_DIVISOR;
        bidaskdiff = Math.abs(bidPrice - askPrice);

        if (bidAskDiffIndicator != null)
            bidAskDiffIndicator.addPoint(bidaskdiff);

        // Only calculate time difference if needed for logging or abnormal detection
        double BBOdiffTimeNanos = 0.0;
        boolean timeCalculated = false;
        
        // Optimized logging with early guard check
        if (bidaskdiff > BID_ASK_DIFF_THRESHOLD && debugLogging) {
            if (!timeCalculated) {
                BBOdiffTimeNanos = (currentTimestampNanos - lasttradetimestampNanos) / NANOS_TO_SECONDS;
                timeCalculated = true;
            }
            StringBuilder sb = new StringBuilder(96);
            sb.append(getTimePrefixOptimized(currentTimestampNanos))
              .append(LOG_PREFIX)
              .append("Trading WARNING: Abnormal BidAskDiff detected: =")
              .append(bidaskdiff)
              .append(", threshold=").append(BID_ASK_DIFF_THRESHOLD)
              .append(" diffTime=")
              .append(BBOdiffTimeNanos);
            MaindebugUI.log(sb.toString());
        }

        // Optimized abnormal price jump detection with cached boolean check
        if (abnormalDetectionEnabled && abnormalPriceJumpIndicator != null) {
            if (!isInvalidPrice(price) && !Double.isNaN(lastBBOPrice)) {
                double diff = Math.abs(price - lastBBOPrice);
                updateAbnormalConditions(diff);
                
                // Calculate time difference only if needed
                if (!timeCalculated) {
                    BBOdiffTimeNanos = (currentTimestampNanos - lasttradetimestampNanos) / NANOS_TO_SECONDS;
                    timeCalculated = true;
                }
                
                // Optimized threshold calculations using pre-computed constants
                double adjustedThreshold = abnormalPriceJumpThresholdAmount + ABNORMAL_THRESHOLD_OFFSET;
                boolean withinTickLimit = diff < ABNORMAL_PRICE_JUMP_TICK_LIMIT || barsSinceSessionStart > SESSION_START_BUFFER_BARS;
                
                if (diff > adjustedThreshold && withinTickLimit && BBOdiffTimeNanos > TIME_DIFF_THRESHOLD) {
                    int reengagetime = (!OSCInstance.isReduceOnlyMode() && BBOdiffTimeNanos > TIME_DIFF_FAST_THRESHOLD && bidaskdiff < BID_ASK_DIFF_THRESHOLD) 
                        ? REENGAGE_TIME_FAST : REENGAGE_TIME_SLOW;
                    
                    OSCInstance.setReduceOnlyModeWithTimeout(true, reengagetime);
                    StringBuilder sb = new StringBuilder(128);
                    sb.append(getTimePrefixOptimized(currentTimestampNanos))
                      .append(LOG_PREFIX)
                      .append("Trading REDUCE ONLY MODE - ")
                      .append(reengagetime)
                      .append("secs: Abnormal price jump detected: diff=")
                      .append(diff)
                      .append(", threshold=")
                      .append(abnormalPriceJumpThresholdAmount)
                      .append(" diffTime=")
                      .append(BBOdiffTimeNanos);
                    MaindebugUI.log(sb.toString());
                } else if (diff > abnormalPriceJumpThresholdAmount && withinTickLimit) {
                    StringBuilder sb = new StringBuilder(128);
                    sb.append(getTimePrefixOptimized(currentTimestampNanos))
                      .append(LOG_PREFIX)
                      .append("Trading WARNING: Abnormal price jump detected: diff=")
                      .append(diff)
                      .append(", threshold=")
                      .append(abnormalPriceJumpThresholdAmount)
                      .append(" diffTime=")
                      .append(BBOdiffTimeNanos);
                    MaindebugUI.log(sb.toString());
                }

                lastDiff = diff;
            }
        }

    // Update Gauges in OrdersTestUIV3 via BBOAnalysedDataInterface
    if (bboAnalysedDataListener != null) {
        final double currentBidAskDiff = bidaskdiff;
        final double currentBboPriceDiff = lastDiff; // lastDiff is updated in the block above
        // The listener (OrdersTestUIV3) will handle SwingUtilities.invokeLater if needed
        bboAnalysedDataListener.onBboDataUpdate(currentBidAskDiff, currentBboPriceDiff);
    }
    /*
    // OLD DIRECT CALL - This block should be removed or commented out
    if (ordersTestUIV3Instance != null) {
        final double currentBidAskDiff = bidaskdiff;
        final double currentBboPriceDiff = lastDiff;
        SwingUtilities.invokeLater(() -> {
            ordersTestUIV3Instance.updateBidAskDiffGauge(currentBidAskDiff);
            // Check if bboPriceDiff is NaN before updating, as it might be if lastBBOPrice
            // was NaN initially
            if (!Double.isNaN(currentBboPriceDiff)) {
                ordersTestUIV3Instance.updateBboPriceDiffGauge(currentBboPriceDiff);
            }
        });
    }
    */

        lasttradetimestampNanos = currentTimestampNanos;
        lastBBOBidPrice = bidPrice;
        lastBBOAskPrice = askPrice;
        lastBBOPrice = price;
    }

    @Override
    public void onTrade(double price, int quantity, TradeInfo tradeInfo) {
        // Fast path: ignore obviously invalid trades
        if (Double.isNaN(price)) {
            return;
        }

        lastTradePrice = price;

        if (quantity <= 0) {
            return;
        }

        // --- Feed trade into the 2-second aggregation bar ---
        // In onTrade:
        if (agg2s_startTimeNanos > 0) {
            boolean resetNeeded = false;
            synchronized (lock) {
                if (agg2s_barNeedsReset) {
                    agg2s_bar.startNext();
                    agg2s_barNeedsReset = false;
                    resetNeeded = true;
                }
                agg2s_bar.addTrade(tradeInfo.isBidAggressor, (long) quantity, price);
            }
            if (resetNeeded && debugLogging && !disableAllLogging) {
                Log.info("Agg2s (onTrade): Reset agg2s_bar for new 2s window.");
            }
            if (debugLogging && !disableAllLogging) {
                Log.info("Agg2s (onTrade): Added Trade Price=" + price + " Qty=" + quantity + " to agg2s_bar");
            }
        } else if (debugLogging && !disableAllLogging) {
            Log.info("Agg2s (onTrade): Skipping trade add, startTimeNanos not initialized yet.");
        }

        // --- Existing VPA/Session feed ---
        if (vpa != null) {
            vpa.onTrade((int) Math.round(price), quantity, currentTimestampNanos);
        }

        // --- Fast stop logic: only check if in a position ---
        if (currentPosition != 0) {
            final double STOP_PROXIMITY_ticks = 1.0; // Consider making this a class constant or parameter
            double normalStop = latestMaAdjustedTrigStop;
            double catStop = latestMaAdjustedCatStop;

            boolean triggerExit = false;

            if (currentPosition > 0) { // Long
                double distNorm = price - normalStop;
                double distCat = price - catStop;
                if ((distNorm <= 0 && Math.abs(distNorm) <= STOP_PROXIMITY_ticks) ||
                        (distCat <= 0 && Math.abs(distCat) <= STOP_PROXIMITY_ticks)) {
                    triggerExit = true;
                }
            } else { // Short
                double distNorm = price - normalStop;
                double distCat = price - catStop;
                if ((distNorm >= 0 && Math.abs(distNorm) <= STOP_PROXIMITY_ticks) ||
                        (distCat >= 0 && Math.abs(distCat) <= STOP_PROXIMITY_ticks)) {
                    triggerExit = true;
                }
            }

            if (triggerExit) {
                OSCInstance.updateMarketPrice(price);
                executeMaTripleStopExitLogic(price, false);
            }

            boolean OSCflatteningRequest = false;
            if (OSCInstance.requestStrategyForFlatteningRealisedDisable == true
                    && OSCInstance.requestStrategyForFlatteningInProgress == false) {
                OSCflatteningRequest = true;
                OSCInstance.requestStrategyForFlatteningRealisedDisable = false;
                OSCInstance.requestStrategyForFlatteningInProgress = true;
                OSCInstance.updateMarketPrice(price);
                executeMaTripleStopExitLogic(price, true);

            }

            if (OSCInstance.requestStrategyForFlatteningUnrealised == true
                    && OSCInstance.requestStrategyForFlatteningInProgress == false) {
                OSCflatteningRequest = true;
                OSCInstance.requestStrategyForFlatteningUnrealised = false;
                OSCInstance.requestStrategyForFlatteningInProgress = true;
                OSCInstance.updateMarketPrice(price);
                executeMaTripleStopExitLogic(price, true);
            }

        }

        if (OSCInstance.requestStrategyForFlatteningInProgress && currentPosition == 0) {
            OSCInstance.requestStrategyForFlatteningInProgress = false;
        }

        // No further action taken
    }

    // --- Entry Triple Stop update ---

    /**
     * Updates the Entry‑Triple‑Stop indicators using the common
     * TrailingStopControllerV3
     * so all three layers (L1 = warn, L2 = trigger, L3 = cat) behave exactly like
     * the
     * exit‑side Independent Triple Stop – but calculated from a *synthetic*
     * position
     * that represents the side we would need to exit to ENTER the trade (i.e. a
     * SHORT
     * dummy position for a prospective long entry, and vice‑versa).
     *
     * @param highPrice  current bar high
     * @param lowPrice   current bar low
     * @param closePrice current bar close
     */

    private int prev_position = 0;
    private Position simShort = null;
    private Position simLong = null;

    private void resetEntryTripleSimulationPositions() {
        simShort = null;
        simLong = null;
    }

    private void updateEntryTripleIndicators(Bar bar) {
        // Check if Entry Triple is enabled AND there are pending entries (MA or general)
        boolean hasPendingEntries = (maPendingEntryDirection != 0) || (pendingEntryDirection != 0);
        
        if (!Boolean.TRUE.equals(entryTriple_Enabled) || !hasPendingEntries || currentPosition != 0) {
            // Clear indicators when disabled, no pending entries, or position is active
            if (entryTripleL1LongIndicator != null)
                entryTripleL1LongIndicator.addPoint(Double.NaN);
            if (entryTripleL2LongIndicator != null)
                entryTripleL2LongIndicator.addPoint(Double.NaN);
            if (entryTripleL3LongIndicator != null)
                entryTripleL3LongIndicator.addPoint(Double.NaN);
            if (entryTripleL1ShortIndicator != null)
                entryTripleL1ShortIndicator.addPoint(Double.NaN);
            if (entryTripleL2ShortIndicator != null)
                entryTripleL2ShortIndicator.addPoint(Double.NaN);
            if (entryTripleL3ShortIndicator != null)
                entryTripleL3ShortIndicator.addPoint(Double.NaN);
                
            // Clear stored stop levels
            entryTripleL2Long_CurrentStopLevel = Double.NaN;
            entryTripleL2Short_CurrentStopLevel = Double.NaN;
            
            // Reset simulation positions when no pending entries
            if (!hasPendingEntries) {
                resetEntryTripleSimulationPositions();
            }
            return;
        }

        double close = bar.getClose();

        // Show long entry levels when there's a pending long entry
        if ((maPendingEntryDirection == 1) || (pendingEntryDirection == 1)) {
            // Only recreate simShort if null
            if (simShort == null) {
                simShort = new Position(Position.Side.SHORT, close);
            }
            
            double l1 = TrailingStopControllerV3.getStopPrice(simShort, bar, entryTripleL1Spec);
            double l2 = TrailingStopControllerV3.getStopPrice(simShort, bar, entryTripleL2Spec);
            double l3 = TrailingStopControllerV3.getStopPrice(simShort, bar, entryTripleL3Spec);
            
            // Store L2 level for entry triple stop checking
            entryTripleL2Long_CurrentStopLevel = l2;
            
            if (entryTripleL1LongIndicator != null)
                entryTripleL1LongIndicator.addPoint(l1);
            if (entryTripleL2LongIndicator != null)
                entryTripleL2LongIndicator.addPoint(l2);
            if (entryTripleL3LongIndicator != null)
                entryTripleL3LongIndicator.addPoint(l3);
                
            // Clear short indicators
            if (entryTripleL1ShortIndicator != null)
                entryTripleL1ShortIndicator.addPoint(Double.NaN);
            if (entryTripleL2ShortIndicator != null)
                entryTripleL2ShortIndicator.addPoint(Double.NaN);
            if (entryTripleL3ShortIndicator != null)
                entryTripleL3ShortIndicator.addPoint(Double.NaN);
            entryTripleL2Short_CurrentStopLevel = Double.NaN;
        }
        // Show short entry levels when there's a pending short entry
        else if ((maPendingEntryDirection == -1) || (pendingEntryDirection == -1)) {
            // Only recreate simLong if null
            if (simLong == null) {
                simLong = new Position(Position.Side.LONG, close);
            }
            
            double l1 = TrailingStopControllerV3.getStopPrice(simLong, bar, entryTripleL1Spec);
            double l2 = TrailingStopControllerV3.getStopPrice(simLong, bar, entryTripleL2Spec);
            double l3 = TrailingStopControllerV3.getStopPrice(simLong, bar, entryTripleL3Spec);
            
            // Store L2 level for entry triple stop checking
            entryTripleL2Short_CurrentStopLevel = l2;
            
            if (entryTripleL1ShortIndicator != null)
                entryTripleL1ShortIndicator.addPoint(l1);
            if (entryTripleL2ShortIndicator != null)
                entryTripleL2ShortIndicator.addPoint(l2);
            if (entryTripleL3ShortIndicator != null)
                entryTripleL3ShortIndicator.addPoint(l3);
                
            // Clear long indicators
            if (entryTripleL1LongIndicator != null)
                entryTripleL1LongIndicator.addPoint(Double.NaN);
            if (entryTripleL2LongIndicator != null)
                entryTripleL2LongIndicator.addPoint(Double.NaN);
            if (entryTripleL3LongIndicator != null)
                entryTripleL3LongIndicator.addPoint(Double.NaN);
            entryTripleL2Long_CurrentStopLevel = Double.NaN;
        }

        prev_position = currentPosition;
    }

    // =================== START NEW MA EXIT LOGIC ===================
    private void executeMaTripleStopExitLogic(double currentPrice, boolean flattening) {
        // // Check if MA Triple Stop Exits are enabled // Removed old check
        // if (!Boolean.TRUE.equals(enableMaTripleStopExits)) {
        // return; // Exit stops are disabled for MA trades
        // }

        // This logic ONLY applies to active MA trades (identified by isMaTradeActive)
        if (!isMaTradeActive || currentPosition == 0) {
            return; // Not an active MA trade, nothing to do here.
        }

        // Calculate P/L based on current position
        double tradePL = 0.0;
        if (currentPosition >= 1) {
            tradePL = (currentPrice - entryPrice) * 50 * currentSizeMultiplier * currentPosition;
        } else if (currentPosition <= -1) {
            tradePL = (entryPrice - currentPrice) * 50 * currentSizeMultiplier * Math.abs(currentPosition);
        }

        // A. Flattening Check (Always Active for MA Trades, unless disabled by
        // parameter)
        if (flattening) {

            if (currentPosition >= 1) {
                Log.info("MA Flattening Request fulfillment: Price=" + currentPrice + " ");
                exitPosition(currentPrice, tradePL, "OSC Flattening Request ", flattening);
                return; // Exit triggered
            } else if (currentPosition <= -1) {
                Log.info("MA Flattening Request fulfillment: Price=" + currentPrice + " ");
                exitPosition(currentPrice, tradePL, "OSC Flattening Request", flattening);
                return; // Exit triggered
            }

        } // End Flattening check block

        // A. Catastrophic Stop Check (Always Active for MA Trades, unless disabled by
        // parameter)
        if (Boolean.TRUE.equals(enableMaCatastrophicExit)) {
            double catStopLevel = this.latestMaAdjustedCatStop;
            if (!Double.isNaN(catStopLevel)) {
                if (currentPosition == 1 && currentPrice <= catStopLevel) {
                    Log.info("MA Catastrophic Stop Hit (Long): Price=" + currentPrice + " <= Stop=" + catStopLevel);
                    exitPosition(currentPrice, tradePL, "MA Catastrophic Triple Stop Exit");
                    return; // Exit triggered
                } else if (currentPosition == -1 && currentPrice >= catStopLevel) {
                    Log.info("MA Catastrophic Stop Hit (Short): Price=" + currentPrice + " >= Stop=" + catStopLevel);
                    exitPosition(currentPrice, tradePL, "MA Catastrophic Triple Stop Exit");
                    return; // Exit triggered
                }
            }
        } // End Catastrophic check block

        // --- MA Profit Protector Logic (configurable MA) ---
        if (currentPosition != 0 && useMAtoProtectProfits && ActivateMAProfitProtector) {
            double unrealizedPnl = 0.0;
            if (this.OSCInstance != null) { // Check if OSCInstance is initialized
                unrealizedPnl = this.OSCInstance.getInstanceUnrealizedPnL();
            }
            boolean isProfitSufficient = unrealizedPnl > -50; // Profitability check (PnL > -50 based on last confirmed
                                                              // user snippet)

            // Select MA index (0-based)
            int maIdx = (maToUseForProfitProtection != null) ? maToUseForProfitProtection - 1 : 1; // Default to MA3 if
                                                                                                   // null
            if (maIdx < 0 || maIdx >= 5 || mas == null || mas.length <= maIdx || mas[maIdx] == null) {
                sendtologDebugUI(
                        "Profit Protector: Invalid MA selection or MA data unavailable (maToUseForProfitProtection="
                                + maToUseForProfitProtection + ")");
            } else {
                boolean isMaConditionMetTechnical = false;
                double relevantMaBandValue = Double.NaN;
                String maConditionDescriptionForLog = "N/A";
                if (currentPosition > 0) { // Long
                    relevantMaBandValue = mas[maIdx].botLowValue;
                    if (!Double.isNaN(relevantMaBandValue)) {
                        if (currentPrice < relevantMaBandValue) {
                            isMaConditionMetTechnical = true;
                            maConditionDescriptionForLog = String.format("Close < MA%d_BotLow(%.5f)", maIdx + 1,
                                    relevantMaBandValue);
                        } else {
                            maConditionDescriptionForLog = String.format("Close >= MA%d_BotLow(%.5f)", maIdx + 1,
                                    relevantMaBandValue);
                        }
                    } else {
                        maConditionDescriptionForLog = "MA_BotLow=NaN";
                    }
                } else if (currentPosition < 0) { // Short
                    relevantMaBandValue = mas[maIdx].topHighValue;
                    if (!Double.isNaN(relevantMaBandValue)) {
                        if (currentPrice > relevantMaBandValue) {
                            isMaConditionMetTechnical = true;
                            maConditionDescriptionForLog = String.format("Close > MA%d_TopHigh(%.5f)", maIdx + 1,
                                    relevantMaBandValue);
                        } else {
                            maConditionDescriptionForLog = String.format("Close <= MA%d_TopHigh(%.5f)", maIdx + 1,
                                    relevantMaBandValue);
                        }
                    } else {
                        maConditionDescriptionForLog = "MA_TopHigh=NaN";
                    }
                }
                String newLogMsg = String.format(
                        "%s: MA%d_Protect Eval: Pos=%d, Close=%.5f, PnL=%.2f, ProfitSufficient(>50)=%b, MA_Check=[%s], MA_Cond_Met_Tech=%b",
                        this.alias, maIdx + 1, currentPosition, currentPrice, unrealizedPnl, isProfitSufficient,
                        maConditionDescriptionForLog, isMaConditionMetTechnical);
                sendtologDebugUI(newLogMsg);

                boolean shouldActivateNow = isProfitSufficient;
                boolean maProfitProtectHit = false;
                String exitReasonForMAProtector = "";
                double triggerPrice = Double.NaN;
                if (currentPosition > 0) {
                    if (!Double.isNaN(mas[maIdx].botLowValue) && currentPrice < mas[maIdx].botLowValue) {
                        maProfitProtectHit = true;
                        triggerPrice = mas[maIdx].botLowValue;
                        exitReasonForMAProtector = "MA" + (maIdx + 1) + " Profit Long Exit";
                    }
                } else {
                    if (!Double.isNaN(mas[maIdx].topHighValue) && currentPrice > mas[maIdx].topHighValue) {
                        maProfitProtectHit = true;
                        triggerPrice = mas[maIdx].topHighValue;
                        exitReasonForMAProtector = "MA" + (maIdx + 1) + " Profit Short Exit";
                    }
                }
                if (shouldActivateNow && maProfitProtectHit) {
                    sendtologDebugUIALWAYS(alias + ": " + exitReasonForMAProtector
                            + " Triggered (Profitable & MA Condition Met). Current Price: "
                            + String.format("%.5f", currentPrice) + ", Trigger MA Level: "
                            + String.format("%.5f", triggerPrice) + ", PnL: " + String.format("%.2f", unrealizedPnl));
                    double maProtectorPL;
                    if (currentPosition > 0) {
                        maProtectorPL = (currentPrice - entryPrice) * currentSizeMultiplier * this.pips;
                    } else {
                        maProtectorPL = (entryPrice - currentPrice) * currentSizeMultiplier * this.pips;
                    }
                    exitPosition(currentPrice, maProtectorPL, exitReasonForMAProtector);
                    return;
                }
            }
        }

        // B, C, D: Normal Exit Logic (Suppression, Trigger, Target)
        // Only proceed if normal exits are enabled
        if (Boolean.TRUE.equals(enableMaNormalExits)) {
            // B. Suppression Logic (Only if Not Exited by Catastrophic)
            boolean anySuppressionActive = (useMa3ConfirmationExitSuppression && ma3ConfirmationActive) ||
                    maSlopeConfirmationActive ||
                    (useMa2SlopeTrendConfirmation && ma2SlopeTrendConfirmationActive) ||
                    (useVisibilityTrendBiasExitSuppression && visibilityTrendBiasExitSuppressionActive);

            if (anySuppressionActive) {
                Log.info("MA Trigger/Target Stop Suppressed: ma3Conf="
                        + (useMa3ConfirmationExitSuppression && ma3ConfirmationActive) +
                        ", maSlopeConf=" + maSlopeConfirmationActive +
                        ", ma2SlopeTrendConf=" + (useMa2SlopeTrendConfirmation && ma2SlopeTrendConfirmationActive) +
                        ", visBiasConf="
                        + (useVisibilityTrendBiasExitSuppression && visibilityTrendBiasExitSuppressionActive));
                return; // Hold position, skip Trigger/Target checks
            }

            // C. Trigger Stop Check (Only if Not Exited by Catastrophic and No Suppressions
            // Active)
            double trigStopLevel = this.latestMaAdjustedTrigStop;
            if (!Double.isNaN(trigStopLevel)) {
                if (currentPosition == 1 && currentPrice <= trigStopLevel) {
                    Log.info("MA Trigger Stop Hit (Long): Price=" + currentPrice + " <= Stop=" + trigStopLevel);
                    exitPosition(currentPrice, tradePL, "MA Trigger Triple Stop Exit");
                    return; // Exit triggered
                } else if (currentPosition == -1 && currentPrice >= trigStopLevel) {
                    Log.info("MA Trigger Stop Hit (Short): Price=" + currentPrice + " >= Stop=" + trigStopLevel);
                    exitPosition(currentPrice, tradePL, "MA Trigger Triple Stop Exit");
                    return; // Exit triggered
                }
            }

            // D. Take Profit (Standard Target Price Logic for MA Trades)
            // Check if targetPrice is valid before comparing
            if (!Double.isNaN(this.targetPrice)) {
                if (currentPosition == 1 && currentPrice >= this.targetPrice) {
                    Log.info("MA Target Hit (Long): Price=" + currentPrice + " >= Target=" + this.targetPrice);
                    exitPosition(currentPrice, tradePL, "MA Target Exit");
                    return; // Exit triggered
                } else if (currentPosition == -1 && currentPrice <= this.targetPrice) {
                    Log.info("MA Target Hit (Short): Price=" + currentPrice + " <= Target=" + this.targetPrice);
                    exitPosition(currentPrice, tradePL, "MA Target Exit");
                    return; // Exit triggered
                }
            }
        } // End Normal Exits check block
    }
    // =================== END NEW MA EXIT LOGIC ===================

    /**
     * Plays an alert sound based on the exit reason code using
     * OSCInstanceMain.playtradealert or simple beeps
     */
    private void playTradeSound(int reasonCode) {
        if (!NowRealtime) {
            return; // Do not play sounds if not in realtime
        }

        if (Boolean.TRUE.equals(useSimpleBeepsForExits)) {
            // Use simple system beeps
            java.awt.Toolkit.getDefaultToolkit().beep(); // First beep for all conditions

            if (reasonCode == -2) { // Standard / Trigger Stop
                try {
                    Thread.sleep(200); // Delay for the second beep
                    java.awt.Toolkit.getDefaultToolkit().beep(); // Second beep
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    Log.info("Beep sequence for reason -2 interrupted: " + e.getMessage());
                }
            } else if (reasonCode == -3) { // Catastrophic Stop
                try {
                    Thread.sleep(200); // Delay for the second beep
                    java.awt.Toolkit.getDefaultToolkit().beep(); // Second beep
                    Thread.sleep(200); // Delay for the third beep
                    java.awt.Toolkit.getDefaultToolkit().beep(); // Third beep
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    Log.info("Beep sequence for reason -3 interrupted: " + e.getMessage());
                }
            }
            // For other reasonCodes (e.g. -1, -4, -99, or positive codes if ever passed
            // here),
            // only the initial beep is played.
        } else {
            // Use synthesized voice alerts via OrderSenderControllerV2
            if (OSCInstanceMain == null || this.alias == null)
                return; // Safety check

            // ** Comprehensive Map for Synthesized Speech **
            Map<Integer, String> reasonTexts = new LinkedHashMap<>();
            // Entries
            reasonTexts.put(1, "MA Rej Long");
            reasonTexts.put(2, "MA Rej Short");
            reasonTexts.put(3, "MA Pending Long");
            reasonTexts.put(4, "MA Pending Short");
            reasonTexts.put(5, "MA Long");
            reasonTexts.put(6, "MA Short");
            reasonTexts.put(7, "MA Pullback Long ATR");
            reasonTexts.put(8, "MA Pullback Long Fixed");
            reasonTexts.put(9, "MA Pullback Short ATR");
            reasonTexts.put(10, "MA Pullback Short Fixed");
            reasonTexts.put(11, "MA Re-entry Short");
            reasonTexts.put(13, "MA Pending Long Triggered");
            reasonTexts.put(14, "MA Pending Short Triggered");
            reasonTexts.put(15, "Entry Triple Long");
            reasonTexts.put(16, "Entry Triple Short");

            // Exits
            reasonTexts.put(-1, "Target Exit");
            reasonTexts.put(-2, "Stop Exit");
            reasonTexts.put(-3, "Cat Stop");
            reasonTexts.put(-4, "Rej Exit");
            reasonTexts.put(-5, "MA1 PP Long");
            reasonTexts.put(-6, "MA1 PP Short");
            reasonTexts.put(-99, "Unknown Exit Reason");

            String alertSpeechText = reasonTexts.get(reasonCode);

            if (alertSpeechText != null) {
                // Play the full text directly from the map
                OSCInstanceMain.playtradealert(true, alertSpeechText, this.alias);
            } else {
                // Fallback for any code not in the map
                OSCInstanceMain.playtradealert(true, "Trade event, code " + reasonCode, this.alias);
            }
        }
    }

    // ============== PROFIT TARGET SYSTEM METHODS ====================
    private void updatePotentialTargets(double currentMarketPrice) {
        if (profitTargetGenerator == null || !Boolean.TRUE.equals(enableProfitTargetSystem)) {
            activePotentialTargets.clear();
            return;
        }

        VWAPCalculatorOptimized.VWAPSignalSnapshot vwapSnap = (vwapCalculator != null && vwapCalculator.isReady())
                ? vwapCalculator.getSignalSnapshot(currentMarketPrice)
                : null;
        BucketedAverageWithSTDBands.SignalSnapshot bucketSnap = (stdBands != null && stdBands.isReady())
                ? stdBands.getSignalSnapshot()
                : null;
        VolumeProfileAnalyzer.VPASnapshot vpaSnap = (vpa != null && vpa.isReady())
                ? vpa.getSnapshot(pips)
                : null;

        try {
            activePotentialTargets = profitTargetGenerator.generateTargets(
                    currentMarketPrice,
                    currentTimestampNanos, // Ensure currentTimestampNanos is passed
                    vwapSnap,
                    bucketSnap,
                    vpaSnap);
        } catch (Exception e) {
            Log.error("Error generating profit targets: " + e.getMessage(), e);
            activePotentialTargets.clear();
        }
    }

    private void updateProfitTargetIndicators() {
        if (!Boolean.TRUE.equals(enableProfitTargetSystem)) {
            // Clear all target indicators if system is off
            for (Indicator ind : profitTargetSupportIndicators)
                if (ind != null)
                    ind.addPoint(Double.NaN);
            for (Indicator ind : profitTargetResistanceIndicators)
                if (ind != null)
                    ind.addPoint(Double.NaN);
            return;
        }

        int supportIdx = 0;
        int resistanceIdx = 0;

        if (activePotentialTargets != null) {
            for (PotentialTarget pt : activePotentialTargets) {
                if (pt.getType() == PotentialTarget.TargetType.SUPPORT
                        && supportIdx < profitTargetSupportIndicators.size()) {
                    Indicator ind = profitTargetSupportIndicators.get(supportIdx++);
                    if (ind != null)
                        ind.addPoint(pt.getPriceLevel());
                } else if (pt.getType() == PotentialTarget.TargetType.RESISTANCE
                        && resistanceIdx < profitTargetResistanceIndicators.size()) {
                    Indicator ind = profitTargetResistanceIndicators.get(resistanceIdx++);
                    if (ind != null)
                        ind.addPoint(pt.getPriceLevel());
                }
            }
        }
        // Clear remaining unused indicators
        for (int i = supportIdx; i < profitTargetSupportIndicators.size(); i++) {
            Indicator ind = profitTargetSupportIndicators.get(i);
            if (ind != null)
                ind.addPoint(Double.NaN);
        }
        for (int i = resistanceIdx; i < profitTargetResistanceIndicators.size(); i++) {
            Indicator ind = profitTargetResistanceIndicators.get(i);
            if (ind != null)
                ind.addPoint(Double.NaN);
        }
    }

    private double getLastValidPrice() {
        // Attempt to get the most recent price from various sources
        if (marketPriceIndicator != null) {
            // Assuming marketPriceIndicator holds the latest market price if used
            // This part would need specific logic if marketPriceIndicator is updated with
            // live ticks
            // For now, let's assume it might not be the best source or might not exist.
        }
        if (size > 0) {
            return closePrices[(head - 1 + capacity) % capacity]; // Last stored close price
        }
        if (stdBands != null && stdBands.isReady()) {
            return stdBands.getLastPrice(); // Price from stdBands if available
        }
        // Fallback or more sophisticated logic might be needed
        return Double.NaN;
    }

    // ======== ENTRY TRIPLE STOP ENTRY LOGIC ========
    private void executeEntryTripleStopLogic(double currentPrice) {
        // Check if Entry Triple is enabled AND there are pending entries
        boolean hasPendingEntries = (maPendingEntryDirection != 0) || (pendingEntryDirection != 0);
        
        if (!Boolean.TRUE.equals(entryTriple_Enabled) || currentPosition != 0 || !hasPendingEntries) {
            return; // Only execute when flat, enabled, and pending entries exist
        }

        // Check for Long Entry (when pending long exists and price breaks above L2 trigger)
        if ((maPendingEntryDirection == 1 || pendingEntryDirection == 1) &&
            !Double.isNaN(entryTripleL2Long_CurrentStopLevel) && 
            currentPrice > entryTripleL2Long_CurrentStopLevel) {
            
            // Cancel pending entry since Entry Triple is taking over
            if (maPendingEntryDirection == 1) {
                Log.info("Entry Triple Long overriding MA pending entry at " + currentPrice);
                maPendingEntryDirection = 0;
                maPendingEntryStopLevel = 0.0;
                maPendingEntryConfirmationCounter = 0;
                maPendingEntrySetupReasonCode = 0;
            }
            if (pendingEntryDirection == 1) {
                Log.info("Entry Triple Long overriding general pending entry at " + currentPrice);
                pendingEntryDirection = 0;
                pendingEntryStopLevel = 0.0;
            }
            
            // Execute Long Entry
            if (OSCInstanceMain != null) {
                int tradeReason = 15; // Entry Triple Long Entry
                double sizeMultiplier = (double) positionSize; // Use base position size
                
                Log.info("Entry Triple Long Entry triggered at " + currentPrice + 
                         ", L2 trigger level: " + entryTripleL2Long_CurrentStopLevel +
                         ", Size: " + sizeMultiplier + ", Reason: " + tradeReason);
                
                // Calculate stop and target prices for the triple stop
                double stopPrice = currentPrice - (entryTriple_L1_OffsetPips * pips);
                double targetPrice = Double.NaN; // Let triple stop handle exits
                
                enterLong(currentPrice, stopPrice, targetPrice, sizeMultiplier, false, 0.0, tradeReason);
                
                // Set trade reason indicator
                if (TradeEntryReasonIndicator != null) {
                    TradeEntryReasonIndicator.addPoint(tradeReason);
                }
            }
        }
        
        // Check for Short Entry (when pending short exists and price breaks below L2 trigger)
        else if ((maPendingEntryDirection == -1 || pendingEntryDirection == -1) &&
                 !Double.isNaN(entryTripleL2Short_CurrentStopLevel) && 
                 currentPrice < entryTripleL2Short_CurrentStopLevel) {
            
            // Cancel pending entry since Entry Triple is taking over
            if (maPendingEntryDirection == -1) {
                Log.info("Entry Triple Short overriding MA pending entry at " + currentPrice);
                maPendingEntryDirection = 0;
                maPendingEntryStopLevel = 0.0;
                maPendingEntryConfirmationCounter = 0;
                maPendingEntrySetupReasonCode = 0;
            }
            if (pendingEntryDirection == -1) {
                Log.info("Entry Triple Short overriding general pending entry at " + currentPrice);
                pendingEntryDirection = 0;
                pendingEntryStopLevel = 0.0;
            }
            
            // Execute Short Entry
            if (OSCInstanceMain != null) {
                int tradeReason = 16; // Entry Triple Short Entry
                double sizeMultiplier = (double) positionSize; // Use base position size
                
                Log.info("Entry Triple Short Entry triggered at " + currentPrice + 
                         ", L2 trigger level: " + entryTripleL2Short_CurrentStopLevel +
                         ", Size: " + sizeMultiplier + ", Reason: " + tradeReason);
                
                // Calculate stop and target prices for the triple stop
                double stopPrice = currentPrice + (entryTriple_L1_OffsetPips * pips);
                double targetPrice = Double.NaN; // Let triple stop handle exits
                
                enterShort(currentPrice, stopPrice, targetPrice, sizeMultiplier, false, 0.0, tradeReason);
                
                // Set trade reason indicator
                if (TradeEntryReasonIndicator != null) {
                    TradeEntryReasonIndicator.addPoint(tradeReason);
                }
            }
        }
    }
    // ===============================================================

} // <<< Closing brace for the class
