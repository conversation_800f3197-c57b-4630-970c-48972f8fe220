package com.bookmap.api.simple.demo.indicators;

import javax.swing.SwingUtilities;

import java.awt.*;
import java.awt.event.*;
import java.awt.image.*;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List; // Explicit import for List
import java.util.ArrayList; // Explicit import for ArrayList
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.ArrayDeque;
import java.util.concurrent.*;
import java.util.concurrent.atomic.*;
import java.util.function.*;
import java.util.stream.*;

import javax.swing.*;

import com.bookmap.api.simple.demo.utils.gui.IconFactory;

import velox.api.layer1.Layer1ApiProvider;
import velox.api.layer1.Layer1ApiTradingListener;
import velox.api.layer1.annotations.Layer1StrategyName;
import velox.api.layer1.common.ListenableHelper;
import velox.api.layer1.common.Log;
import velox.api.layer1.common.RunModeHelper;
import velox.api.layer1.data.*;
import velox.api.layer1.layers.utils.SoundSynthHelper;
import velox.api.layer1.messages.Layer1ApiSoundAlertMessage;
import velox.api.layer1.messages.indicators.Layer1ApiUserMessageModifyIndicator.GraphType;
import velox.api.layer1.simplified.Api;
import velox.api.layer1.simplified.BalanceListener;
import velox.api.layer1.simplified.Indicator;
import velox.api.layer1.simplified.PositionListener;
import velox.api.layer1.simplified.TimeListener;
//import com.google.common.util.concurrent.AtomicDouble;
//import java.util.concurrent.atomic.AtomicDouble; 
import java.text.DecimalFormat;
import velox.api.layer1.annotations.Layer1MultiAccountTradingSupported;
import com.bookmap.api.simple.demo.indicators.OrderUIDataListener;

@Layer1MultiAccountTradingSupported

@Layer1StrategyName("OrderSenderControllerV2")
public class OrderSenderControllerV2
        implements PositionListener, BalanceListener,
        TimeListener, Layer1ApiTradingListener,
        OrderCommandInterface, DrawdownGuard.DrawdownGuardLogger {
    /*
     * ──────────────────────────────────────────────────────────────
     * OrderCommandInterface – thin adapters only for the *renamed*
     * entry commands. All data‑getter & cancel/flatten methods
     * already exist, so we don't redeclare them here.
     * ──────────────────────────────────────────────────────────────
     */

    @Override
    public boolean marketBuyViaInterface(int size, OrderDuration d, double sl, double tp) {
        return sendMarketBuyOrder(size, d, sl, tp);
    }

    @Override
    public boolean marketSellViaInterface(int size, OrderDuration d, double sl, double tp) {
        return sendMarketSellOrder(size, d, sl, tp);
    }

    @Override
    public boolean limitBuyViaInterface(int size, double px, OrderDuration d, double sl, double tp) {
        return sendLimitBuyOrder(size, px, d, sl, tp);
    }

    @Override
    public boolean limitSellViaInterface(int size, double px, OrderDuration d, double sl, double tp) {
        return sendLimitSellOrder(size, px, d, sl, tp);
    }

    @Override
    public boolean trailingStopBuyViaInterface(int size, double start, int trail,
            OrderDuration d, double sl, double tp) {
        return sendTrailingStopBuyOrder(size, start, trail, d, sl, tp, 0.0);
    }

    @Override
    public boolean trailingStopSellViaInterface(int size, double start, int trail,
            OrderDuration d, double sl, double tp) {
        return sendTrailingStopSellOrder(size, start, trail, d, sl, tp, 0.0);
    }

    @Override
    public boolean ocoBuyLimitStopViaInterface(int size, double lim, double stop,
            OrderDuration d, double sl, double tp) {
        return sendOcoBuyLimitBuyStopOrder(size, lim, stop, d, sl, tp, 0.0);
    }

    @Override
    public boolean ocoSellLimitStopViaInterface(int size, double lim, double stop,
            OrderDuration d, double sl, double tp) {
        return sendOcoSellLimitSellStopOrder(size, lim, stop, d, sl, tp, 0.0);
    }

    /*
     * All other interface methods (getters, cancels, flatten/reverse,
     * performance dashboard, toggle listener, etc.) were already present
     * in the original class – nothing to add or duplicate.
     */

    @Override
    public int cancelAllOrdersViaInterface() {
        return cancelAllOrders();
    }

    @Override
    public int cancelAllBuyOrdersViaInterface() {
        return cancelAllBuyOrders();
    }

    @Override
    public int cancelAllSellOrdersViaInterface() {
        return cancelAllSellOrders();
    }

    @Override
    public boolean instanceFlattenPositionsViaInterface() {
        return instanceFlattenPositions();
    }

    @Override
    public boolean instanceReversePositionsViaInterface() {
        return instanceReversePositions();
    }

    @Override
    public void setLiveTradingToggleListenerViaInterface(java.util.function.Consumer<Boolean> l) {
        this.liveTradingListener = l;
    }

    @Override
    public java.util.function.Consumer<Boolean> getLiveTradingToggleListenerViaInterface() {
        return this.liveTradingListener;
    }

    @Override
    public void showPerformanceDashboardViaInterface() {
        showPerformanceDashboard();
    }

    // Implementation of DrawdownGuard.DrawdownGuardLogger interface methods
    @Override
    public void log(String message) {
        sendtolog(message);
    }

    @Override
    public boolean isDebugEnabled() {
        return DEBUG_DRAWDOWN_GUARD;
    }

    @Override
    public double getStartingAccountBalance() {
        return StartingAccountBalance;
    }

    // Test UI enable flag.
    private static final boolean ENABLE_TEST_UI = false;

    // Add a performance optimization flag to control verbose logging
    private static final boolean ENABLE_VERBOSE_LOGGING = false;

    // Set to true to use exchange-provided positions, false to use locally tracked
    // positions
    private boolean useExchangePositions = false;

    private final InstrumentInfo instrumentInfo;

    // Add indicator for market price received in updateMarketPrice
    private Indicator marketPriceReceivedIndicator;
    private TradingHoursManager hoursManager; // Declaration of hoursManager

    private Api api;
    // Add missing field for liveTradingListener
    private java.util.function.Consumer<Boolean> liveTradingListener;
    private String alias;
    private String algo;

    // Add getter methods for fields accessed by DrawdownGuard
    public InstrumentInfo getInstrumentInfo() {
        return instrumentInfo;
    }

    public Api getApi() {
        return api;
    }

    // Add placeholder methods for flattenAndDisable and flattenCurrentPosition
    // TODO: Implement the actual logic for these methods
    public void flattenAndDisableStrategy(String reason, boolean useMarketOrders) {
        // Placeholder implementation
        sendtolog("[OrderSenderControllerV2] flattenAndDisableStrategy called with reason: " + reason);
        // Actual implementation would involve cancelling orders, closing positions, and
        // disabling trading
    }

    public void flattenCurrentPosition(String reason, boolean useMarketOrders) {
        // Placeholder implementation
        sendtolog("[OrderSenderControllerV2] flattenCurrentPosition called with reason: " + reason);
        // Actual implementation would involve closing the current position
    }

    private long last;
    private double InstrumentMultiplier;
    private static long currentmilliseconds;
    private static long currentnanoseconds;

    // Executor for global aggregation tasks
    private static final ScheduledExecutorService globalAggregatorService = Executors
            .newSingleThreadScheduledExecutor(r -> new Thread(r, "GlobalAggregatorThread"));

    // ═══ PERFORMANCE OPTIMIZATIONS ═══
    // Batching optimization for global updates to reduce CPU overhead
    // - Batches multiple update requests into single operations every 100ms
    // - Separates UI updates from calculation thread using
    // SwingUtilities.invokeLater
    // - Reduces iteration frequency over position collections
    private static final long GLOBAL_UPDATE_BATCH_INTERVAL_MS = 100; // Update every 100ms
    private static volatile boolean globalUpdateScheduled = false;
    private static final Object globalUpdateLock = new Object();

    private final OrderSender orderSender;
    // Instance-specific risk limits for this controller:
    private double instanceMaxDailyLoss;
    private int instanceMaxPositionSize;

    // Instance-level profit targets
    private double instanceMaxDailyProfitRealized = 0; // 0 or negative means inactive
    private double instanceMaxDailyProfitTotal = 0; // New: Target for Realized + Unrealized PnL. 0 or negative means
                                                    // inactive.
    // Cache for balance and currency
    private volatile double lastAccountBalance = 0.0;
    private volatile String lastAccountCurrency = "";
    private boolean enableProfitTargetChecks = true; // Default to enabled, can be configured
    private boolean autoDisableOnProfitTarget = true; // New flag to enable auto-disable on profit target hit

    // Global risk counters (shared across instances)
    private static double globalCurrentDailyLoss = 0;
    private static volatile double aggregatedGlobalUnrealizedPnL = 0.0; // For periodic aggregation
    private static int globalCurrentPosition = 0;

    // Instance-level risk counters:
    private double instanceCurrentDailyLoss = 0;
    private int instanceCurrentPosition = 0;

    // Minimum interval between orders (in nanoseconds) and thread safety.
    private static final long minOrderIntervalNano = 1_000_000L;
    private static final AtomicLong globalLastOrderTime = new AtomicLong(0);
    private static ScheduledExecutorService globalExecutor = createNewExecutor();
    // Track if the globalExecutor is shut down
    private static volatile boolean isGlobalExecutorShutdown = false;
    public static boolean Debug_Orders = true;

    // Debug flag for this controller.
    public static boolean debug_mode_ordersendercontroller = true;
    public static boolean debug_mode_ordersendercontroller_listner = true;
    public static boolean DEBUG_DRAWDOWN_GUARD = false;

    public static boolean requestStrategyForFlatteningRealisedDisable = false;
    public static boolean requestStrategyForFlatteningUnrealised = false;
    public static boolean requestStrategyForFlatteningInProgress = false;

    // Test UI and debug UI flags and references.
    private static boolean testUIInitialized = false;
    private static JFrame testUIFrame = null;
    private static boolean debugUIInitialized = false;
    // Cache for working orders count
    private volatile int workingBuyOrders = 0;
    private volatile int workingSellOrders = 0;
    private static OrderSenderControllerDebugUI debugUIInstance;
    private static MaindebugUI maindebugUIInstance;
    // ── UI is now accessed through the decoupled listener interface ──
    private OrderUIDataListener ui; // for data callbacks

    // Global market price tracking.
    private static volatile double currentMarketPrice = 0.0;

    // Global P&L tracking.
    private static double globalRealizedPnL = 0.0;
    private static final java.util.concurrent.CopyOnWriteArrayList<Position> globalPositions = new java.util.concurrent.CopyOnWriteArrayList<>();

    // Inner class to represent a position.
    private static class Position {
        String instanceId;
        double entryPrice;
        volatile int quantity; // Added volatile for thread visibility
        boolean isLong;
        volatile double unrealizedPnL; // Added volatile for thread visibility
        volatile int netPosition; // Added volatile for thread visibility
        volatile boolean active = true; // Added volatile for thread visibility
        private double totalRealizedPnL = 0.0; // Track total realized PnL for this position object
        private final double instrumentMultiplier; // Store the instrument multiplier
        private final double pips; // Store the pips value
        private long entryTimeBookmapMillis = 0; // Bookmap time when position was opened
        // In Position class, add fields for MFE/MAE
        private double maxUnrealizedPnL = Double.NEGATIVE_INFINITY; // Maximum Favorable Excursion (MFE)
        private double minUnrealizedPnL = Double.POSITIVE_INFINITY; // Maximum Adverse Excursion (MAE)
        // --- PnL time series for post-profit drawdown ---
        private final List<Double> pnlTimeSeries = new ArrayList<>();
        private final OrderSenderControllerV2 controller; // Reference to parent controller for DrawdownGuard access

        // New fields for drawdown percentages
        private double maxUnrealizedDrawdownPercent = 0.0; // Stores the max positive DD %
        private double minUnrealizedDrawdownPercent = Double.POSITIVE_INFINITY; // Stores the min positive DD %
                                                                                // (non-zero)
        private double maxTotalDrawdownPercent = 0.0;
        private double minTotalDrawdownPercent = Double.POSITIVE_INFINITY;

        private String originalEntryReason = ""; // New field for the initial entry reason

        Position(String instanceId, double entryPrice, int quantity, boolean isLong, double instrumentMultiplier,
                double pips, OrderSenderControllerV2 controller) {
            if (quantity < 0) {
                throw new IllegalArgumentException("Position quantity must be non-negative");
            }
            this.instanceId = instanceId;
            this.entryPrice = entryPrice;
            this.quantity = quantity;
            this.isLong = isLong;
            this.unrealizedPnL = 0.0;
            this.netPosition = isLong ? quantity : -quantity;
            this.active = quantity > 0;
            this.instrumentMultiplier = (instrumentMultiplier > 0 && !Double.isNaN(instrumentMultiplier))
                    ? instrumentMultiplier
                    : 1; // Corrected logic for NaN and non-positive multipliers
            this.pips = (pips > 0 && !Double.isNaN(pips)) ? pips : 1.0; // Added pips initialization with validation
            this.entryTimeBookmapMillis = currentmilliseconds; // Set entry time on creation
            this.controller = controller; // Initialize parent controller reference
            pnlTimeSeries.clear(); // New position, clear PnL series
            resetDrawdownPercentTrackers(); // Initialize drawdown percent trackers
        }

        public int getNetPosition() {
            return netPosition;
        }

        public boolean isActive() {
            return active;
        }

        public double getTotalRealizedPnL() {
            return totalRealizedPnL;
        }

        // Getters for new drawdown percentage fields
        public double getMaxUnrealizedDrawdownPercent() {
            return this.maxUnrealizedDrawdownPercent;
        }

        public double getMinUnrealizedDrawdownPercent() {
            return (this.minUnrealizedDrawdownPercent == Double.POSITIVE_INFINITY) ? 0.0
                    : this.minUnrealizedDrawdownPercent;
        }

        public double getMaxTotalDrawdownPercent() {
            return maxTotalDrawdownPercent;
        }

        public double getMinTotalDrawdownPercent() {
            return (this.minTotalDrawdownPercent == Double.POSITIVE_INFINITY) ? 0.0 : this.minTotalDrawdownPercent;
        } // RE-ADDED

        private void resetDrawdownPercentTrackers() {
            this.maxUnrealizedDrawdownPercent = 0.0;
            this.minUnrealizedDrawdownPercent = Double.POSITIVE_INFINITY;
            this.maxTotalDrawdownPercent = 0.0;
            this.minTotalDrawdownPercent = Double.POSITIVE_INFINITY;
        }

        // Mark mutators as synchronized for thread safety (preserve all method
        // comments)
        synchronized void addToPosition(int additionalQuantity, double price, boolean isBuy) {
            if (additionalQuantity <= 0) {
                throw new IllegalArgumentException("Additional quantity must be positive");
            }
            if (isBuy != isLong) {
                throw new IllegalArgumentException(
                        "Cannot add opposite direction to position. Use reducePosition instead");
            }
            // If position was inactive (zero quantity), use the new price as entry price
            if (quantity == 0) {
                entryPrice = price;
                // Reset MFE/MAE for the new trade leg
                this.maxUnrealizedPnL = Double.NEGATIVE_INFINITY;
                this.minUnrealizedPnL = Double.POSITIVE_INFINITY;
                this.unrealizedPnL = 0.0; // Start with zero PnL for the new leg
                resetDrawdownPercentTrackers(); // Reset for new trade leg

                quantity = additionalQuantity;
                this.entryTimeBookmapMillis = OrderSenderControllerV2.currentmilliseconds; // Use static access
                pnlTimeSeries.clear(); // New position, clear PnL series
            } else {
                // VWAP fix: calculate weighted average price
                entryPrice = ((entryPrice * quantity) + (price * additionalQuantity)) / (quantity + additionalQuantity);
                quantity += additionalQuantity;
            }
            // Update net position and active status
            netPosition = isLong ? quantity : -quantity;
            active = true; // Adding to position always makes it active

            // Update DrawdownGuard's entryNotional if controller and guard exist
            if (this.controller != null && this.controller.drawdownGuard != null
                    && this.controller.instrumentInfo != null) {
                double pointValue = this.controller.instrumentInfo.multiplier; // Use multiplier instead of
                                                                               // sizeMultiplier
                double newNotional = Math.abs(this.entryPrice * this.quantity * pointValue);
                this.controller.drawdownGuard.updateEntryNotional(newNotional);
            }
        }

        synchronized double reducePosition(int reduceBy, double price, boolean isBuy, String exitReason) { // Added
                                                                                                           // exitReason
            if (reduceBy <= 0) {
                throw new IllegalArgumentException("Reduction quantity must be positive");
            }
            if (reduceBy > quantity) {
                throw new IllegalArgumentException(
                        "Cannot reduce by more than current position size");
            }
            if (isBuy == isLong) {
                throw new IllegalArgumentException(
                        "Reduction must be opposite to position direction");
            }
            // Calculate realized PnL for the reduced portion
            double grossPnl = calculateRealizedPnL(price) * (reduceBy / (double) quantity);
            // Commission and slippage adjustment
            double commissionAndSlippage = OrderSenderControllerV2.estimateCommissionAndSlippage(
                    reduceBy, price, instrumentMultiplier, OrderSenderControllerV2.getPipsStatic());
            double pnl = grossPnl - commissionAndSlippage;
            totalRealizedPnL += pnl; // Add to the total realized PnL
            // Record trade for the reduced portion (partial or full close)
            List<Double> tradePnlSeries = new ArrayList<>(pnlTimeSeries);

            // Ensure MFE/MAE are finite before recording
            double mfeToRecord = (this.maxUnrealizedPnL == Double.NEGATIVE_INFINITY) ? Double.NaN
                    : this.maxUnrealizedPnL;
            double maeToRecord = (this.minUnrealizedPnL == Double.POSITIVE_INFINITY) ? Double.NaN
                    : this.minUnrealizedPnL;

            String currentDrawdownReason = this.controller != null ? this.controller.lastDrawdownCloseReason : null;

            TradePerformanceMetrics.getInstance().recordTradeFromPositionUpdate(
                    instanceId, // algoName
                    isLong, // isBuy (direction of original position)
                    reduceBy, // size of closed portion
                    entryPrice, // entry price
                    entryTimeBookmapMillis, // entry time (Bookmap ms)
                    OrderSenderControllerV2.currentmilliseconds, // exit time (Bookmap ms)
                    pnl, // realized PnL for this leg (already net of costs)
                    commissionAndSlippage, // commission+slippage
                    mfeToRecord, // MFE
                    maeToRecord, // MAE
                    tradePnlSeries, // PnL time series
                    getMinUnrealizedDrawdownPercent(), // Pass min (positive or 0)
                    this.maxUnrealizedDrawdownPercent, // Pass max (positive or 0)
                    (this.minTotalDrawdownPercent == Double.POSITIVE_INFINITY ? 0.0 : this.minTotalDrawdownPercent), // RE-ADDED
                                                                                                                     // with
                                                                                                                     // default
                    this.maxTotalDrawdownPercent,
                    currentDrawdownReason, // Pass drawdown reason
                    this.originalEntryReason, // Pass original entry reason for the position
                    exitReason // Pass exit reason for this specific reduction/closure
            );
            // Before recording trade, log entry/exit times
            OrderSenderControllerV2.sendtolog("[Position] Recording trade in reducePosition: entryTime="
                    + entryTimeBookmapMillis + ", exitTime=" + OrderSenderControllerV2.currentmilliseconds + ", size="
                    + reduceBy + ", DDReason=" + currentDrawdownReason);

            // CRITICAL: Reset drawdown reason after it has been used for recording this
            // trade segment.
            if (this.controller != null && currentDrawdownReason != null) {
                this.controller.lastDrawdownCloseReason = null;
            }

            quantity -= reduceBy;
            netPosition = isLong ? quantity : -quantity;
            // Update active state but don't reset entryPrice
            active = quantity > 0;
            if (quantity == 0) {
                pnlTimeSeries.clear(); // Position closed, clear PnL series
                resetDrawdownPercentTrackers(); // Reset when position is fully closed
            }
            // For most trading systems, entryPrice is not updated on reduction, so this is
            // left as is.
            return pnl;
        }

        // A-4: Separate calculation and logging for testability and clarity
        synchronized void updatePnL(double currentBidPrice, double currentAskPrice) { // Takes BBOs as args
            double oldPnL = unrealizedPnL;
            if (quantity == 0) {
                if (unrealizedPnL != 0.0) { // Only update if it actually changes
                    unrealizedPnL = 0.0;
                    logPnLChange(oldPnL, unrealizedPnL); // Log if it changed to zero
                }
                // No need to update drawdown percentages if position is zero
                return;
            }

            // Guard clause for invalid BBO prices (using parameters)
            if ((isLong && currentBidPrice <= 0) || (!isLong && currentAskPrice <= 0)) {
                if (OrderSenderControllerV2.debug_mode_ordersendercontroller) { // Use static outer class debug flag
                    OrderSenderControllerV2.sendtolog("Position.updatePnL", 0f, String.format( // Use static sendtolog
                            "Skipping PnL update for %s due to invalid BBO price. isLong: %b, Bid: %.2f, Ask: %.2f",
                            instanceId, isLong, currentBidPrice, currentAskPrice));
                }
                return; // Do not update PnL if relevant BBO price is invalid
            }

            // Calculate PnL using contract multiplier and BBO prices (using parameters)
            if (isLong) {
                unrealizedPnL = (currentBidPrice - entryPrice) * quantity * instrumentMultiplier;
            } else { // isShort
                unrealizedPnL = (entryPrice - currentAskPrice) * quantity * instrumentMultiplier;
            }

            // Track max/min unrealized PnL for this position
            if (unrealizedPnL > maxUnrealizedPnL)
                maxUnrealizedPnL = unrealizedPnL;
            if (unrealizedPnL < minUnrealizedPnL)
                minUnrealizedPnL = unrealizedPnL;

            // --- Track PnL time series ---
            if (quantity > 0) { // Only add to series if position is active
                // Only add to the time series if the PnL has meaningfully changed or if it's
                // the first valid PnL for an empty series.
                double pnlComparisonThreshold = this.pips * this.quantity * this.instrumentMultiplier;
                if (pnlComparisonThreshold <= 0) {
                    pnlComparisonThreshold = 1e-6; // Default epsilon if threshold is not positive
                }

                if (Math.abs(unrealizedPnL - oldPnL) > pnlComparisonThreshold) {
                    pnlTimeSeries.add(unrealizedPnL);
                } else if (pnlTimeSeries.isEmpty() && unrealizedPnL != 0.0) {
                    // Add the first non-zero PnL to an empty series, even if the change from oldPnL
                    // (likely 0.0) wasn't above threshold.
                    // This ensures the start of PnL tracking for a new leg is captured.
                    pnlTimeSeries.add(unrealizedPnL);
                }
            }

            // A-4: Log PnL change if it actually changed
            logPnLChange(oldPnL, unrealizedPnL);

            // Update drawdown percentages if controller and drawdownGuard are available
            if (this.controller != null && this.controller.drawdownGuard != null) {
                DrawdownGuard dg = this.controller.drawdownGuard;
                double currentUnrDDPct = dg.getCurrentUnrealizedDrawdownPercent(); // This is positive or 0
                double currentTotalDDPct = dg.getCurrentTotalDrawdownPercent();

                // Update Max Unrealized Drawdown %
                if (currentUnrDDPct > this.maxUnrealizedDrawdownPercent) {
                    this.maxUnrealizedDrawdownPercent = currentUnrDDPct;
                }

                // Update Min Unrealized Drawdown %
                if (currentUnrDDPct > 0.0 && currentUnrDDPct < this.minUnrealizedDrawdownPercent) {
                    this.minUnrealizedDrawdownPercent = currentUnrDDPct;
                }

                if (currentTotalDDPct > this.maxTotalDrawdownPercent) {
                    this.maxTotalDrawdownPercent = currentTotalDDPct;
                }
                if (currentTotalDDPct < this.minTotalDrawdownPercent) { // RE-ADDED logic
                    this.minTotalDrawdownPercent = currentTotalDDPct;
                }
            }
        }

        synchronized double calculateRealizedPnL(double exitPrice) {
            // Calculate realized PnL using contract multiplier
            return isLong ? (exitPrice - entryPrice) * quantity * instrumentMultiplier
                    : (entryPrice - exitPrice) * quantity * instrumentMultiplier;
        }

        synchronized void reversePosition(int newQuantity, double newPrice, boolean newDirection,
                String exitReasonForCloseLeg, String entryReasonForNewLeg) { // Added reasons
            // Before reversing, if there is an open position, record a trade for the closed
            // portion
            if (quantity > 0) {
                // Use the provided newPrice (which is the reversal price) for calculating
                // realized PnL of the closing leg
                double grossPnl = calculateRealizedPnL(newPrice);
                double commissionAndSlippage = OrderSenderControllerV2.estimateCommissionAndSlippage(
                        quantity, newPrice, instrumentMultiplier, OrderSenderControllerV2.getPipsStatic());
                double pnl = grossPnl - commissionAndSlippage;
                List<Double> tradePnlSeries = new ArrayList<>(pnlTimeSeries);

                // Ensure MFE/MAE are finite before recording
                double mfeToRecord = (this.maxUnrealizedPnL == Double.NEGATIVE_INFINITY) ? Double.NaN
                        : this.maxUnrealizedPnL;
                double maeToRecord = (this.minUnrealizedPnL == Double.POSITIVE_INFINITY) ? Double.NaN
                        : this.minUnrealizedPnL;
                String currentDrawdownReason = this.controller != null ? this.controller.lastDrawdownCloseReason : null;

                TradePerformanceMetrics.getInstance().recordTradeFromPositionUpdate(
                        instanceId,
                        isLong,
                        quantity,
                        entryPrice,
                        entryTimeBookmapMillis,
                        OrderSenderControllerV2.currentmilliseconds, // Use static access
                        pnl,
                        commissionAndSlippage, // commission+slippage
                        mfeToRecord, // MFE
                        maeToRecord, // MAE
                        tradePnlSeries, // PnL time series
                        getMinUnrealizedDrawdownPercent(), // Pass min (positive or 0)
                        this.maxUnrealizedDrawdownPercent, // Pass max (positive or 0)
                        (this.minTotalDrawdownPercent == Double.POSITIVE_INFINITY ? 0.0 : this.minTotalDrawdownPercent), // RE-ADDED
                                                                                                                         // with
                                                                                                                         // default
                        this.maxTotalDrawdownPercent,
                        currentDrawdownReason, // Pass drawdown reason
                        this.originalEntryReason, // Pass original entry reason for the closing leg
                        exitReasonForCloseLeg // Pass exit reason for the closing leg
                );
                totalRealizedPnL += pnl;
                // CRITICAL: Reset drawdown reason after it has been used for recording this
                // trade segment.
                if (this.controller != null && currentDrawdownReason != null) {
                    this.controller.lastDrawdownCloseReason = null;
                }
            }
            // Store the old info for logging
            boolean oldDirection = this.isLong;

            // Update position properties for the new leg
            this.isLong = newDirection;
            this.quantity = newQuantity;
            this.entryPrice = newPrice; // Entry price for the new leg is the reversal price
            this.netPosition = newDirection ? newQuantity : -newQuantity;
            this.active = newQuantity > 0;
            this.originalEntryReason = (entryReasonForNewLeg == null || entryReasonForNewLeg.isEmpty()) ? ""
                    : entryReasonForNewLeg; // Set entry reason for new leg

            // Reset unrealized PnL for the new leg (will be recalculated by updatePnL
            // later)
            this.unrealizedPnL = 0.0;

            // Reset MFE/MAE for the new trade leg
            this.maxUnrealizedPnL = Double.NEGATIVE_INFINITY;
            this.minUnrealizedPnL = Double.POSITIVE_INFINITY;

            // Set new entry time for the new position
            this.entryTimeBookmapMillis = OrderSenderControllerV2.currentmilliseconds; // Use static access
            pnlTimeSeries.clear(); // New position, clear PnL series
            resetDrawdownPercentTrackers(); // Reset for new trade leg

            // Update DrawdownGuard's entryNotional for the new position
            if (this.controller != null && this.controller.drawdownGuard != null
                    && this.controller.instrumentInfo != null) {
                double pointValue = this.controller.instrumentInfo.multiplier;
                double newNotional = Math.abs(this.entryPrice * this.quantity * pointValue);
                this.controller.drawdownGuard.updateEntryNotional(newNotional);
                this.controller.drawdownGuard.resetUnrealizedTrackingForNewTrade(); // Also reset unrealized DD tracking
                                                                                    // state
            }

            OrderSenderControllerV2.sendtolog("[Position] Reversed: new entryTimeBookmapMillis="
                    + this.entryTimeBookmapMillis + ", instanceId=" + instanceId + ", newPrice=" + newPrice
                    + ", newDirection=" + (newDirection ? "LONG" : "SHORT"));
        }

        // A-4: Optimized logging method for PnL changes
        private void logPnLChange(double oldPnL, double newPnL) {
            // Early return if debugging is disabled
            if (!OrderSenderControllerV2.debug_mode_ordersendercontroller) {
                return;
            }

            // Calculate threshold only once per call
            double pnlChangeThreshold = this.pips * this.quantity * this.instrumentMultiplier;
            if (pnlChangeThreshold <= 0) {
                pnlChangeThreshold = 1e-6;
            }

            // Quick magnitude check before expensive string operations
            double changeMagnitude = Math.abs(oldPnL - newPnL);
            if (changeMagnitude > pnlChangeThreshold) {
                // Use StringBuilder for efficient string construction
                StringBuilder logMessage = new StringBuilder(100); // Pre-allocate capacity
                logMessage.append("PnL changed from ").append(String.format("%.2f", oldPnL))
                        .append(" to ").append(String.format("%.2f", newPnL))
                        .append(" (Δ ").append(String.format("%.2f", newPnL - oldPnL))
                        .append(", Threshold: ").append(String.format("%.2f", pnlChangeThreshold))
                        .append(") for ").append(this.instanceId != null ? this.instanceId : "Position");

                OrderSenderControllerV2.sendtolog("PnL Change", 0f, logMessage.toString());
            }
        }

        // Accessors for MFE/MAE
        public double getMaxUnrealizedPnL() {
            return maxUnrealizedPnL;
        }

        public double getMinUnrealizedPnL() {
            return minUnrealizedPnL;
        }

        public synchronized void resetPnLTrackers() {
            totalRealizedPnL = 0.0;
            unrealizedPnL = 0.0;
            maxUnrealizedPnL = Double.NEGATIVE_INFINITY;
            minUnrealizedPnL = Double.POSITIVE_INFINITY;
            pnlTimeSeries.clear();
            // entryPrice, quantity, isLong, entryTimeBookmapMillis are typically reset when
            // a position is fully closed and re-opened/reversed.
            // This method focuses on resetting PnL accumulation and tracking metrics within
            // an existing position object state.
            if (OrderSenderControllerV2.debug_mode_ordersendercontroller) {
                OrderSenderControllerV2.sendtolog(instanceId != null ? instanceId : "Position", 0f,
                        "Position PnL trackers reset.");
            }
        }
    }

    /*
     * ───────────────────────────────────────────────────────────────────────────
     * NEW Draw‑down Guard (instance‑level realised + unrealised protection)
     * ──────────────────────────────────────────────────────────────────────────
     */
    /**
     * ───────────────────────────────────────────────────────────────
     * DrawdownGuard – per‑trade + per‑session risk sentinel
     * Implements:
     * • unrealised hard stop (cash + % of notional)
     * • profit give‑back stop (% of peak gain)
     * • total‑equity stop (cash + % peak‑to‑trough)
     * Thread‑safe via AtomicDouble; state logs throttled to 1 Hz.
     * } // End of DrawdownGuard class
     * 
     * /** Draw‑down guard for this controller instance
     */
    private /* final */ DrawdownGuard drawdownGuard; // Made non-final
    private Position instancePosition;
    private double instanceRealizedPnL = 0.0;
    private final String instanceId; // Unique identifier for this instance
    private final boolean configTotalDDActionEnabled; // New OSC level config
    private final boolean configUnrealizedDDActionEnabled; // New OSC level config
    private volatile String lastDrawdownCloseReason = null; // New field to store drawdown reason

    // UI references.

    // Global max position size limit.
    private static final int GLOBAL_MAX_POSITION_SIZE = 10;

    // Global max order size constant.
    private static final int GLOBAL_MAX_ORDER_SIZE = GLOBAL_MAX_POSITION_SIZE * 2; // adjust as needed

    // Realtime status flag.
    private static boolean NowRealtime = false;

    // Instance-level last update time for UI throttling.
    private Long lastUpdateTime = null;

    // Timestamps for last orders.
    private long instanceLastBuyOrderTimeMillis = 0;
    private long instanceLastSellOrderTimeMillis = 0;
    private static long globalLastBuyOrderTimeMillis = 0;
    private static long globalLastSellOrderTimeMillis = 0;

    private Indicator lastTradeIndicator;
    private double pips;

    // Collection of all instances.
    private static final Set<OrderSenderControllerV2> instances = Collections.newSetFromMap(new ConcurrentHashMap<>());

    // Order tracking structures
    private final Map<String, OrderInfo> activeOrders = new ConcurrentHashMap<>();
    private final List<OrderInfo> orderHistory = new CopyOnWriteArrayList<>();
    private static final int MAX_ORDER_HISTORY = 10000;

    // Add missing map for tracking client IDs to order IDs
    private final Map<String, String> orderClientIdToOrderId = new ConcurrentHashMap<>();

    // Add reverseInProgress flag to track position reversal operations
    private final AtomicBoolean reverseInProgress = new AtomicBoolean(false);

    // ───────────────────────────────────────────────────────────────────────
    // TIME‑GATING SUPPORT
    // ───────────────────────────────────────────────────────────────────────
    private static TradingHoursManager DEFAULT_HOURS_MANAGER;

    static {
        DEFAULT_HOURS_MANAGER = new TradingHoursManager(
                ZoneId.of("America/New_York"),
                List.of(
                        // Monday
                        new TradingWindow(DayOfWeek.MONDAY, "00:00", "16:00", TimeGateState.ACTIVE, 1.0, 0, 0),
                        // Lunch throttle 11:30–13:00
                        new TradingWindow(DayOfWeek.MONDAY, "11:30", "13:00", TimeGateState.THIN_LIQUIDITY, 0.25, 0, 0),
                        // 3 PM micro-throttle 15:00–15:15
                        new TradingWindow(DayOfWeek.MONDAY, "15:00", "15:15", TimeGateState.THIN_LIQUIDITY, 0.30, 0, 0),
                        // Maintenance break 16:00–17:00
                        new TradingWindow(DayOfWeek.MONDAY, "16:00", "17:00", TimeGateState.BLOCKED, 1.0, 0, 0),
                        // Evening session 17:00–24:00
                        new TradingWindow(DayOfWeek.MONDAY, "17:00", "23:59:59.999999999", TimeGateState.ACTIVE, 1.0, 0,
                                0),

                        // Tuesday
                        new TradingWindow(DayOfWeek.TUESDAY, "00:00", "11:30", TimeGateState.ACTIVE, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.TUESDAY, "11:30", "13:00", TimeGateState.THIN_LIQUIDITY, 0.25, 0,
                                0),
                        new TradingWindow(DayOfWeek.TUESDAY, "13:00", "15:00", TimeGateState.ACTIVE, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.TUESDAY, "15:00", "15:15", TimeGateState.THIN_LIQUIDITY, 0.30, 0,
                                0),
                        new TradingWindow(DayOfWeek.TUESDAY, "15:15", "16:00", TimeGateState.ACTIVE, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.TUESDAY, "16:00", "17:00", TimeGateState.BLOCKED, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.TUESDAY, "17:00", "23:59:59.999999999", TimeGateState.ACTIVE, 1.0,
                                0, 0),

                        // Wednesday
                        new TradingWindow(DayOfWeek.WEDNESDAY, "00:00", "11:30", TimeGateState.ACTIVE, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.WEDNESDAY, "11:30", "13:00", TimeGateState.THIN_LIQUIDITY, 0.25, 0,
                                0),
                        new TradingWindow(DayOfWeek.WEDNESDAY, "13:00", "15:00", TimeGateState.ACTIVE, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.WEDNESDAY, "15:00", "15:15", TimeGateState.THIN_LIQUIDITY, 0.30, 0,
                                0),
                        new TradingWindow(DayOfWeek.WEDNESDAY, "15:15", "16:00", TimeGateState.ACTIVE, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.WEDNESDAY, "16:00", "17:00", TimeGateState.BLOCKED, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.WEDNESDAY, "17:00", "23:59:59.999999999", TimeGateState.ACTIVE, 1.0,
                                0, 0),

                        // Thursday
                        new TradingWindow(DayOfWeek.THURSDAY, "00:00", "11:30", TimeGateState.ACTIVE, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.THURSDAY, "11:30", "13:00", TimeGateState.THIN_LIQUIDITY, 0.25, 0,
                                0),
                        new TradingWindow(DayOfWeek.THURSDAY, "13:00", "15:00", TimeGateState.ACTIVE, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.THURSDAY, "15:00", "15:15", TimeGateState.THIN_LIQUIDITY, 0.30, 0,
                                0),
                        new TradingWindow(DayOfWeek.THURSDAY, "15:15", "16:00", TimeGateState.ACTIVE, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.THURSDAY, "16:00", "17:00", TimeGateState.BLOCKED, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.THURSDAY, "17:00", "23:59:59.999999999", TimeGateState.ACTIVE, 1.0,
                                0, 0),

                        // Friday
                        new TradingWindow(DayOfWeek.FRIDAY, "00:00", "11:30", TimeGateState.ACTIVE, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.FRIDAY, "11:30", "13:00", TimeGateState.THIN_LIQUIDITY, 0.25, 0, 0),
                        new TradingWindow(DayOfWeek.FRIDAY, "13:00", "15:00", TimeGateState.ACTIVE, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.FRIDAY, "15:00", "15:15", TimeGateState.THIN_LIQUIDITY, 0.30, 0, 0),
                        new TradingWindow(DayOfWeek.FRIDAY, "15:15", "16:00", TimeGateState.ACTIVE, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.FRIDAY, "16:00", "17:00", TimeGateState.BLOCKED, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.FRIDAY, "17:00", "23:59:59.999999999", TimeGateState.ACTIVE, 1.0, 0,
                                0),

                        // Weekend closure
                        new TradingWindow(DayOfWeek.SATURDAY, "00:00", "23:59:59.999999999", TimeGateState.BLOCKED, 1.0,
                                0, 0),
                        new TradingWindow(DayOfWeek.SUNDAY, "00:00", "17:00", TimeGateState.BLOCKED, 1.0, 0, 0),
                        new TradingWindow(DayOfWeek.SUNDAY, "17:00", "23:59:59.999999999", TimeGateState.ACTIVE, 1.0, 0,
                                0)));

        // Schedule the global aggregation task
        globalAggregatorService.scheduleAtFixedRate(OrderSenderControllerV2::updateGlobalAggregates, 5, 1,
                TimeUnit.SECONDS); // Initial delay 5s, repeat every 1s
    }

    public enum TimeGateState {
        ACTIVE, THIN_LIQUIDITY, CLOSE_ONLY, BLOCKED
    }

    /** Snapshot object consumed by OrdersTestUIV3 for display */
    public static final class TimeGateSnapshot {
        public final TimeGateState state;
        public final int positionCap;
        public final int orderCap;
        public final long secondsToNext; // 0 if unknown

        public TimeGateSnapshot(TimeGateState s, int pos, int ord, long sec) {
            this.state = s;
            this.positionCap = pos;
            this.orderCap = ord;
            this.secondsToNext = sec;
        }
    }

    /** Simple holder for a weekly recurring window */
    private static final class TradingWindow {
        final DayOfWeek day;
        final LocalTime start;
        final LocalTime end;
        final TimeGateState state;
        final double sizeMultiplier; // 1.0 = no change
        final int maxPositionCap; // 0 = use global/instance
        final int maxOrderCap; // 0 = use global

        TradingWindow(DayOfWeek d, String s, String e,
                TimeGateState st, double mult, int posCap, int ordCap) {
            this.day = d;
            this.start = LocalTime.parse(s);
            this.end = LocalTime.parse(e);
            this.state = st;
            this.sizeMultiplier = mult;
            this.maxPositionCap = posCap;
            this.maxOrderCap = ordCap;
        }

        boolean matches(ZonedDateTime zdt) {
            return zdt.getDayOfWeek() == day &&
                    !zdt.toLocalTime().isBefore(start) &&
                    zdt.toLocalTime().isBefore(end);
        }
    }

    /** Very light‑weight manager sufficient for our needs */
    private static final class TradingHoursManager {
        private final ZoneId zone;
        private final List<TradingWindow> windows;

        TradingHoursManager(ZoneId zone, List<TradingWindow> windows) {
            this.zone = zone;
            this.windows = windows;
        }

        TimeGateState state(Instant ts) {
            ZonedDateTime zdt = ts.atZone(zone);
            for (TradingWindow w : windows)
                if (w.matches(zdt))
                    return w.state;
            return TimeGateState.BLOCKED; // fail‑closed
        }

        double sizeMultiplier(Instant ts) {
            ZonedDateTime zdt = ts.atZone(zone);
            for (TradingWindow w : windows)
                if (w.matches(zdt))
                    return w.sizeMultiplier;
            return 1.0;
        }

        int capPosition(Instant ts, int base) {
            ZonedDateTime zdt = ts.atZone(zone);
            for (TradingWindow w : windows)
                if (w.matches(zdt) && w.maxPositionCap > 0)
                    return w.maxPositionCap;
            return base;
        }

        int capOrder(Instant ts, int base) {
            ZonedDateTime zdt = ts.atZone(zone);
            for (TradingWindow w : windows)
                if (w.matches(zdt) && w.maxOrderCap > 0)
                    return w.maxOrderCap;
            return base;
        }

        boolean orderAllowed(boolean reducing, Instant ts) {
            TimeGateState s = state(ts);
            if (s == TimeGateState.ACTIVE) {
                return true;
            } else if (s == TimeGateState.THIN_LIQUIDITY) {
                return true; // allowed but maybe scaled
            } else if (s == TimeGateState.CLOSE_ONLY) {
                return reducing;
            } else if (s == TimeGateState.BLOCKED) {
                return false;
            }
            return false; // Default case
        }

        Duration untilNextChange(Instant ts) {
            ZonedDateTime zdt = ts.atZone(zone);
            Duration best = Duration.ZERO;
            for (TradingWindow w : windows) {
                ZonedDateTime end = zdt.with(w.day).with(w.end);
                if (!end.isBefore(zdt)) { // end is after now
                    Duration d = Duration.between(zdt, end);
                    if (best.isZero() || d.compareTo(best) < 0)
                        best = d;
                }
            }
            return best;
        }
    }

    /** Provides UI with a consolidated view of the current time‑gate and caps */
    public TimeGateSnapshot getTimeGateSnapshot() {
        // Ensure we have a valid timestamp
        if (currentmilliseconds <= 0) {
            // If no valid timestamp, return a default snapshot with ACTIVE state
            return new TimeGateSnapshot(TimeGateState.ACTIVE, instanceMaxPositionSize, GLOBAL_MAX_ORDER_SIZE, 0);
        }

        Instant ts = Instant.ofEpochMilli(currentmilliseconds);
        TimeGateState s = hoursManager.state(ts);
        int posCap = hoursManager.capPosition(ts, instanceMaxPositionSize);
        int ordCap = hoursManager.capOrder(ts, GLOBAL_MAX_ORDER_SIZE);
        long secs = hoursManager.untilNextChange(ts).toSeconds();

        // Log the values being returned
        if (debug_mode_ordersendercontroller) {
            System.out.println("getTimeGateSnapshot: time=" + currentmilliseconds +
                    ", state=" + s + ", posCap=" + posCap + ", ordCap=" + ordCap);
        }

        return new TimeGateSnapshot(s, posCap, ordCap, secs);
    }

    // Order types for filtering
    public enum OrderCategory {
        ALL, MARKET, LIMIT, STOP, OCO, BUY, SELL
    }

    // Enum for order types
    public enum OrderType {
        MARKET,
        LIMIT,
        STOP,
        STOP_LIMIT,
        TRAIL_STOP,
        OCO
    }

    // Order status enum
    public enum OrderStatus {
        PENDING_NEW, NEW, WORKING, PARTIALLY_FILLED, FILLED, CANCELED, REJECTED
    }

    // Order information class
    public static class OrderInfo {
        private final String orderId;
        private final String clientId;
        private final boolean isBuy;
        private final OrderType type;
        private final int size;
        private double price;
        private OrderStatus status;
        private final long creationTime;
        private long lastUpdateTime;
        private final String notes;
        private final boolean isOco;
        private String linkedOrderId; // For OCO orders
        private final List<OrderEvent> events = new ArrayList<>(); // Track order events
        private final String orderReason; // New field for entry/exit reason

        public OrderInfo(String orderId, String clientId, boolean isBuy, OrderType type,
                int size, double price, String notes, boolean isOco, String orderReason) { // Added orderReason
            this.orderId = orderId;
            this.clientId = clientId;
            this.isBuy = isBuy;
            this.type = type;
            this.size = size;
            this.price = price;
            this.status = OrderStatus.PENDING_NEW;
            this.creationTime = currentmilliseconds;
            this.lastUpdateTime = this.creationTime;
            this.notes = notes;
            this.isOco = isOco;
            this.orderReason = (orderReason == null || orderReason.isEmpty()) ? "" : orderReason; // Initialize with
                                                                                                  // default

            // Add initial event
            addEvent("CREATED", "Order created with status " + status
                    + (this.orderReason.isEmpty() ? "" : " Reason: " + this.orderReason));
        }

        public void setStatus(OrderStatus status) {
            OrderStatus oldStatus = this.status;
            this.status = status;
            this.lastUpdateTime = currentmilliseconds;

            // Log status change as an event
            addEvent("STATUS_CHANGE", "Status changed from " + oldStatus + " to " + status);
        }

        public void setPrice(double price) {
            double oldPrice = this.price;
            this.price = price;
            this.lastUpdateTime = currentmilliseconds;

            // Log price change as an event
            addEvent("PRICE_CHANGE", "Price changed from " + oldPrice + " to " + price);
        }

        public void addEvent(String eventType, String details) {
            events.add(new OrderEvent(eventType, details, currentmilliseconds));
        }

        public List<OrderEvent> getEvents() {
            return new ArrayList<>(events);
        }

        public void setLinkedOrderId(String linkedOrderId) {
            this.linkedOrderId = linkedOrderId;
        }

        public String getOrderId() {
            return orderId;
        }

        public String getClientId() {
            return clientId;
        }

        public boolean isBuy() {
            return isBuy;
        }

        public OrderType getType() {
            return type;
        }

        public int getSize() {
            return size;
        }

        public double getPrice() {
            return price;
        }

        public OrderStatus getStatus() {
            return status;
        }

        public long getCreationTime() {
            return creationTime;
        }

        public long getLastUpdateTime() {
            return lastUpdateTime;
        }

        public String getNotes() {
            return notes;
        }

        public boolean isOco() {
            return isOco;
        }

        public String getLinkedOrderId() {
            return linkedOrderId;
        }

        public String getOrderReason() {
            return orderReason;
        } // New getter

        @Override
        public String toString() {
            return String.format(
                    "%s %s %s order: ID=%s, Size=%d, Price=%.2f, Status=%s, Created=%d, LastUpdate=%d, Notes=%s%s",
                    isBuy ? "BUY" : "SELL",
                    isOco ? "OCO" : "",
                    type,
                    orderId,
                    size,
                    price,
                    status,
                    creationTime,
                    lastUpdateTime,
                    notes,
                    linkedOrderId != null ? ", Linked=" + linkedOrderId : "");
        }

        public String getLifecycleReport() {
            StringBuilder report = new StringBuilder();
            report.append("=== ORDER LIFECYCLE REPORT ===\n");
            report.append(String.format("Order ID: %s (Client ID: %s)\n", orderId, clientId));
            report.append(String.format("Type: %s %s %s\n", isBuy ? "BUY" : "SELL", isOco ? "OCO" : "", type));
            report.append(String.format("Size: %d, Current Price: %.2f\n", size, price));
            report.append(String.format("Current Status: %s\n", status));
            report.append(String.format("Created: %s\n", new Date(creationTime)));
            report.append(String.format("Last Updated: %s\n", new Date(lastUpdateTime)));

            if (status == OrderStatus.FILLED) {
                long executionTime = lastUpdateTime - creationTime;
                report.append(String.format("Execution Time: %d ms\n", executionTime));
            }

            report.append("\nEvent History:\n");
            for (OrderEvent event : events) {
                report.append(String.format("- [%s] %s: %s\n",
                        new Date(event.getTimestamp()), event.getType(), event.getDetails()));
            }

            return report.toString();
        }
    }

    // Order event class to track order lifecycle
    public static class OrderEvent {
        private final String type;
        private final String details;
        private final long timestamp;

        public OrderEvent(String type, String details, long timestamp) {
            this.type = type;
            this.details = details;
            this.timestamp = timestamp;
        }

        public String getType() {
            return type;
        }

        public String getDetails() {
            return details;
        }

        public long getTimestamp() {
            return timestamp;
        }
    }

    // Constructor.
    public OrderSenderControllerV2(Api api, String alias, String algo, double maxDailyLoss, int maxPositionSize,
            Indicator lastTradeIndicator, InstrumentInfo instrumentInfo,
            double ddCash, double ddPct, double ddUnrCash, double ddUnrPct, double ddGivePct, double profitEpsilonTicks,
            boolean totalDDActionEnabled, boolean unrealizedDDActionEnabled) {
        // Chain to the primary constructor, providing default values for profit targets
        this(api, alias, algo, maxDailyLoss, maxPositionSize,
                0.0, // default for instanceMaxDailyProfitRealized
                0.0, // default for instanceMaxDailyProfitTotal
                lastTradeIndicator, instrumentInfo,
                ddCash, ddPct, ddUnrCash, ddUnrPct, ddGivePct, profitEpsilonTicks,
                totalDDActionEnabled, unrealizedDDActionEnabled);
    }

    // NEW Overloaded Constructor (Now the PRIMARY constructor)
    public OrderSenderControllerV2(Api api, String alias, String algo,
            double maxDailyLoss, int maxPositionSize,
            double maxDailyProfitRealized, double maxDailyProfitTotal, // Updated for Total PnL target
            Indicator lastTradeIndicator, InstrumentInfo instrumentInfo,
            double ddCash, double ddPct, double ddUnrCash, double ddUnrPct, double ddGivePct, double profitEpsilonTicks,
            boolean totalDDActionEnabled, boolean unrealizedDDActionEnabled) { // Added DD action enable params

        // Always restart the global executor on new instance creation (e.g., after
        // reload)
        restartGlobalExecutor();


        // Add null checks to prevent NullPointerException
        if (alias == null) {
            alias = "UnknownAlias";
            Log.info("Constructor Warning: Alias parameter was null, using default: " + alias);
        }
        if (algo == null) {
            algo = "UnknownAlgo";
            Log.info("Constructor Warning: Algo parameter was null, using default: " + algo);
        }

        this.instanceId = alias + "_" + algo; // Initialize instanceId early as it's used by DrawdownGuard

        this.api = api;
        this.alias = alias;
        this.algo = algo;
        this.instanceMaxDailyLoss = maxDailyLoss;
        this.instanceMaxPositionSize = maxPositionSize;

        // Set profit targets from constructor arguments
        this.instanceMaxDailyProfitRealized = maxDailyProfitRealized;
        this.instanceMaxDailyProfitTotal = maxDailyProfitTotal; // Updated
        this.enableProfitTargetChecks = true; // Default to enabled

        this.instrumentInfo = instrumentInfo;
        // Use robust InstrumentMultiplier initialization
        this.InstrumentMultiplier = (instrumentInfo.multiplier > 0 && !Double.isNaN(instrumentInfo.multiplier))
                ? instrumentInfo.multiplier
                : 1;

        this.pips = instrumentInfo.pips;
        this.last = System.nanoTime();

        // ─── Time‑gate initialisation (US equities template) ───
        this.hoursManager = DEFAULT_HOURS_MANAGER; // Assign the statically initialized manager

        // Initialize DrawdownGuard
        this.drawdownGuard = new DrawdownGuard(ddCash, ddPct, ddUnrCash, ddUnrPct, ddGivePct, profitEpsilonTicks,
                totalDDActionEnabled, unrealizedDDActionEnabled,
                this.instanceId, this.algo, this); // Use passed DD params

        if (!debugUIInitialized && debug_mode_ordersendercontroller) {
            debugUIInstance = OrderSenderControllerDebugUI.getInstance();
            maindebugUIInstance = MaindebugUI.getInstance();
            debugUIInitialized = true;
            // These global initializations might be better placed in a static block or a
            // dedicated global reset method
            // if they are truly global and not per-instance related, but keeping as per
            // original logic for now.
            // globalCurrentPosition = 0;
            // globalCurrentDailyLoss = 0;
        }
        this.orderSender = new OrderSender(api, alias, pips, debugUIInstance, algo);

        this.instanceCurrentPosition = 0;
        this.instancePosition = null; // Initialize instancePosition

        // Register for order status updates
        registerOrderCallbacks();

        this.lastTradeIndicator = lastTradeIndicator;

        // Log instrument info on creation.
        if (debug_mode_ordersendercontroller) {
            String instrumentInfoStr = new InstrumentInfoLogger().logInstrumentInfo(instrumentInfo,
                    getCurrentMarketPrice());
            sendtolog("Initialization", 0f, instrumentInfoStr);
            // Log profit targets too
            sendtolog("Initialization", 0f, String.format("Instance %s Profit Targets: Realized=%.2f, Total=%.2f",
                    instanceId, this.instanceMaxDailyProfitRealized, this.instanceMaxDailyProfitTotal));
        }

        // --- Singleton UI registration ---
        OrdersTestUIV3 obtainedUi = OrdersTestUIV3.getInstance(instrumentInfo, this, null);
        if (obtainedUi != null) {
            final OrdersTestUIV3 finalUi = obtainedUi; // Use effectively final variable in lambda
            SwingUtilities.invokeLater(() -> {
                finalUi.registerController(this, this.algo);
            });
            obtainedUi.show();
            this.ui = obtainedUi; // Assign the interface reference

            // Initialize UI with data
            this.ui.updateAlias(this.alias);
            this.ui.updateGlobalPnL(getGlobalUnrealizedPnL(), getGlobalRealizedPnL(), getGlobalTotalPnL());
            this.ui.updateAccountAndOrders(lastAccountBalance, workingSellOrders, workingBuyOrders,
                    instanceCurrentPosition);
            this.ui.setRealtimeStatus(NowRealtime);
        } else {
            Log.info("OrderSenderControllerV2: UI instance is null, possibly due to shutdown. UI will not be shown for "
                    + this.algo);
        }

        // Instantiate V2 tracker
        this.inputDataHealthTracker = new InputDataHealthTrackerV2("OSC-" + algo + "-Tracker");

        // marketPriceReceivedIndicator = registerIndicator("OSC Market Price Received",
        // GraphType.BOTTOM, Color.red, 1);
        updateSimulationModeFromRunMode();

        // Store the action enabled flags
        this.configTotalDDActionEnabled = totalDDActionEnabled;
        this.configUnrealizedDDActionEnabled = unrealizedDDActionEnabled;

        // Add to global instances - ONCE, at the end of the primary constructor
        instances.add(this);
    }

    private Indicator registerIndicator(String name, GraphType graphType, Color color, int width) {
        Indicator indicator = api.registerIndicator(name, graphType);
        indicator.setColor(color);
        indicator.setWidth(width);
        return indicator;
    }

    // Register for order callbacks
    private void registerOrderCallbacks() {
        if (api != null) {
            try {
                // Instead of trying to use the simplified api.addOrdersListener
                // We need to get the underlying provider and register with it directly
                Layer1ApiProvider provider = api.getProvider();
                if (provider != null) {
                    ListenableHelper.addListeners(provider, this);
                    sendtolog("Order Callbacks", 0f,
                            "Successfully registered with Layer1ApiProvider for " + instanceId);
                } else {
                    sendtolog("Order Callbacks", 0f, "Failed to get provider from API for " + instanceId);
                }
            } catch (Exception e) {
                sendtolog("Order Callbacks", 0f, "Error registering callbacks: " + e.getMessage());
            }
        } else {
            sendtolog("Order Callbacks", 0f, "Cannot register callbacks - API is null for " + instanceId);
        }
    }

    // Risk limit setter.
    public void setRiskLimits(double newMaxDailyLoss, int newMaxPositionSize) {
        this.instanceMaxDailyLoss = newMaxDailyLoss;
        this.instanceMaxPositionSize = newMaxPositionSize;
    }

    public double getMaxDailyLoss() {
        return instanceMaxDailyLoss;
    }

    public int getMaxPositionSize() {
        return instanceMaxPositionSize;
    }

    public void setInstanceMaxPositionSize(int newMaxPositionSize) {
        this.instanceMaxPositionSize = newMaxPositionSize;
    }

    public void setProfitTargets(double realizedTarget, double totalTarget) { // Updated signature
        this.instanceMaxDailyProfitRealized = realizedTarget;
        this.instanceMaxDailyProfitTotal = totalTarget; // Updated
        if (debug_mode_ordersendercontroller) {
            sendtolog("Profit Targets Set", 0f, String.format("Instance %s: Realized=%.2f, Total=%.2f",
                    instanceId, realizedTarget, totalTarget)); // Updated logging
        }
    }

    public void setEnableProfitTargetChecks(boolean enable) {
        this.enableProfitTargetChecks = enable;
        if (debug_mode_ordersendercontroller) {
            sendtolog("Profit Target Checks " + (enable ? "Enabled" : "Disabled"), 0f, "Instance " + instanceId);
        }
    }

    public double getInstanceMaxDailyProfitRealized() {
        return instanceMaxDailyProfitRealized;
    }

    /**
     * Gets the instance-level maximum daily total profit target (Realized +
     * Unrealized PnL).
     * 
     * @return The total profit target. Returns 0 or negative if not active.
     */
    public double getInstanceMaxDailyProfitTotal() { // New getter for Total PnL Target
        return instanceMaxDailyProfitTotal;
    }

    public boolean isProfitTargetChecksEnabled() {
        return enableProfitTargetChecks;
    }

    private boolean canSendOrder(double estimatedRisk, int size, boolean isBuy) {
        // ─── Time gate pre‑check ───────────────────────────────
        Instant ts = Instant.ofEpochMilli(currentmilliseconds);
        boolean reducing = (instanceCurrentPosition > 0 && !isBuy) ||
                (instanceCurrentPosition < 0 && isBuy);
        if (!hoursManager.orderAllowed(reducing, ts)) {
            sendtolog("Order Blocked (Time Gate)", 0f, "Trading window not active");
            displayRejectedOrderIcon(getCurrentMarketPrice());
            return false;
        }
        // Apply dynamic caps & size multiplier
        int dynPosCap = hoursManager.capPosition(ts, instanceMaxPositionSize);
        int dynOrdCap = hoursManager.capOrder(ts, GLOBAL_MAX_ORDER_SIZE);
        double mult = hoursManager.sizeMultiplier(ts);
        size = (int) Math.max(1, Math.floor(size * mult));
        TradingState state = tradingState.get();
        // --- Permanent disable: allow only reducing orders ---
        if (state == TradingState.DISABLED_PERMANENT) {
            sendtolog("Order Blocked", 0f,
                    "Trading is permanently disabled for this instance due to risk/profit limits. Only reducing/closing orders allowed.");
            displayRejectedOrderIcon(getCurrentMarketPrice());
            int orderEffectOnPosition = isBuy ? size : -size;
            int projectedNewPosition = instanceCurrentPosition + orderEffectOnPosition;
            if ((instanceCurrentPosition > 0 && !isBuy && size <= instanceCurrentPosition) ||
                    (instanceCurrentPosition < 0 && isBuy && size <= Math.abs(instanceCurrentPosition))) {
                // Allow strictly reducing/closing orders
                return true;
            }
            return false;
        }
        // --- Transient reduce‑only mode ---
        if (state == TradingState.REDUCE_ONLY) {
            int orderEffectOnPosition = isBuy ? size : -size;
            int projectedNewPosition = instanceCurrentPosition + orderEffectOnPosition;
            int orderIsStrictlyClosingOrReducing = 0;
            if (instanceCurrentPosition > 0 && !isBuy && size <= instanceCurrentPosition) {
                orderIsStrictlyClosingOrReducing = 1;
            } else if (instanceCurrentPosition < 0 && isBuy && size <= Math.abs(instanceCurrentPosition)) {
                orderIsStrictlyClosingOrReducing = 1;
            }
            if (orderIsStrictlyClosingOrReducing == 0) {
                sendtolog("Order Blocked (Reduce Only Mode)", 0f,
                        "Order rejected: Reduce-only mode is enabled. Only closing/reducing orders are allowed. Position: "
                                + instanceCurrentPosition + ", Order (buy=" + isBuy + ", size=" + size + ")");
                displayRejectedOrderIcon(getCurrentMarketPrice());
                return false; // Block the current order
            }
            // If order IS strictly closing/reducing, continue to further checks
        }
        if (!globalEnableLiveTrading) {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Order Blocked", 0f, "Order rejected: Global live trading is disabled.");
            }
            displayRejectedOrderIcon(getCurrentMarketPrice());
            return false;
        }
        if (!enableLiveTrading) {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Order Blocked", 0f, "Order rejected: Live trading is disabled for this instance.");
            }
            displayRejectedOrderIcon(getCurrentMarketPrice());
            return false;
        }
        if (!NowRealtime && !simulationMode) {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Order Blocked", 0f, "Order rejected: Trading not in realtime mode is disabled.");
            }
            displayRejectedOrderIcon(getCurrentMarketPrice());
            return false;
        }

        int orderEffectOnPosition = isBuy ? size : -size;
        int projectedNewPosition = instanceCurrentPosition + orderEffectOnPosition;

        // --- Explicit Reduce-Only Mode Check ---
        if (state == TradingState.REDUCE_ONLY) {
            int orderIsStrictlyClosingOrReducing = 0;
            if (instanceCurrentPosition > 0 && !isBuy && size <= instanceCurrentPosition) {
                orderIsStrictlyClosingOrReducing = 1;
            } else if (instanceCurrentPosition < 0 && isBuy && size <= Math.abs(instanceCurrentPosition)) {
                orderIsStrictlyClosingOrReducing = 1;
            }
            if (orderIsStrictlyClosingOrReducing == 0) {
                sendtolog("Order Blocked (Reduce Only Mode)", 0f,
                        "Order rejected: Reduce-only mode is enabled. Only closing/reducing orders are allowed. Position: "
                                + instanceCurrentPosition + ", Order (buy=" + isBuy + ", size=" + size + ")");
                displayRejectedOrderIcon(getCurrentMarketPrice());
                return false; // Block the current order
            }
            // If order IS strictly closing/reducing, continue to further checks
        }

        // Profit Target Checks
        if (enableProfitTargetChecks) {
            boolean realizedProfitTargetHit = (instanceMaxDailyProfitRealized > 0
                    && instanceRealizedPnL >= instanceMaxDailyProfitRealized);
            double currentUnrealizedPnL = getInstanceUnrealizedPnL();
            double currentTotalPnL = instanceRealizedPnL + currentUnrealizedPnL;
            boolean totalProfitTargetHit = (instanceMaxDailyProfitTotal > 0
                    && currentTotalPnL >= instanceMaxDailyProfitTotal);

            // Auto-disable if Realized OR Total profit target is hit and auto-disable is
            // enabled
            if (autoDisableOnProfitTarget && (realizedProfitTargetHit || totalProfitTargetHit)) {
                String disableReason = "Unknown";
                if (totalProfitTargetHit && instanceMaxDailyProfitTotal > 0) { // Prioritize Total if active and hit
                    disableReason = "Total";
                } else if (realizedProfitTargetHit && instanceMaxDailyProfitRealized > 0) {
                    disableReason = "Realized";
                }
                // --- Sticky disable: change state machine ---
                tradingState.set(TradingState.DISABLED_PERMANENT);
                setReduceOnlyMode(true); // keeps UI in sync
                sendtolog(disableReason + " profit target hit (R: " + instanceRealizedPnL + ", U: "
                        + currentUnrealizedPnL + ", T: " + currentTotalPnL
                        + ") and auto-disable is enabled. Initiating position closure and disabling trading. (Sticky disable active)");
                closePositionsAndDisableTrading();
                displayRejectedOrderIcon(getCurrentMarketPrice()); // Also display rejected icon here
                return false; // Block the current order
            }

            // Logic for "close/reduce only" if any target is hit (Realized OR Total)
            if (realizedProfitTargetHit || totalProfitTargetHit) {
                setReduceOnlyMode(true); // Set explicit reduce-only mode
                String targetTypeLog = "";
                double targetValueLog = 0;
                double pnlValueLog = 0;

                if (totalProfitTargetHit && instanceMaxDailyProfitTotal > 0) { // Prioritize logging total if that's hit
                                                                               // and active
                    targetTypeLog = "TOTAL";
                    targetValueLog = instanceMaxDailyProfitTotal;
                    pnlValueLog = currentTotalPnL;
                } else if (realizedProfitTargetHit && instanceMaxDailyProfitRealized > 0) { // Fallback to realized if
                                                                                            // only that was hit and
                                                                                            // active
                    targetTypeLog = "REALIZED";
                    targetValueLog = instanceMaxDailyProfitRealized;
                    pnlValueLog = instanceRealizedPnL;
                } else {
                    targetTypeLog = "NONE_ACTIVE_BUT_HIT_FLAG";
                }

                boolean orderIsStrictlyClosingOrReducing = false;
                if (instanceCurrentPosition > 0 && !isBuy && size <= instanceCurrentPosition) {
                    orderIsStrictlyClosingOrReducing = true;
                } else if (instanceCurrentPosition < 0 && isBuy && size <= Math.abs(instanceCurrentPosition)) {
                    orderIsStrictlyClosingOrReducing = true;
                }
                if (!orderIsStrictlyClosingOrReducing) {
                    if (debug_mode_ordersendercontroller) {
                        sendtolog("Order Blocked (Profit Target)", 0f, String.format(
                                "Order rejected: Instance %s profit target of %.2f reached (Current PnL R:%.2f U:%.2f T:%.2f). Position: %d. Order (buy=%b, size=%d) is not strictly closing/reducing. Instance %s.",
                                targetTypeLog, targetValueLog, instanceRealizedPnL, currentUnrealizedPnL,
                                currentTotalPnL, instanceCurrentPosition, isBuy, size, instanceId));
                    }
                    displayRejectedOrderIcon(getCurrentMarketPrice());
                    return false; // Disallow orders that are not strictly closing/reducing
                }
                // If order IS strictly closing/reducing, it continues to further checks.

            }
        }

        // Allow orders that reduce or flatten position even if daily loss limit is hit
        if (instanceMaxDailyLoss > 0 && instanceRealizedPnL <= -instanceMaxDailyLoss) { // Check if loss limit is active
            // --- Sticky disable: change state machine ---
            tradingState.set(TradingState.DISABLED_PERMANENT);
            setReduceOnlyMode(true); // Set explicit reduce-only mode
            // If the order reduces the absolute position (closer to zero), allow it
            if (Math.abs(projectedNewPosition) < Math.abs(instanceCurrentPosition)) {
                return true; // This allows the order, bypassing subsequent size limit checks.
            } else {
                sendtolog("Order Blocked", 0f, String.format(
                        "Order rejected: Instance daily loss limit of %.2f exceeded (PnL=%.2f). Position: %d. Order (buy=%b, size=%d) is not reducing magnitude. Only closing/reducing orders are allowed. (Sticky disable active)",
                        instanceMaxDailyLoss, instanceRealizedPnL, instanceCurrentPosition, isBuy, size));
                return false;
            }

        }
        // Allow orders that reduce position (General rule, may bypass size limits if
        // this return true is kept)
        // This condition means any order that reduces current exposure is allowed to
        // bypass size limits.
        if ((instanceCurrentPosition > 0 && orderEffectOnPosition < 0) ||
                (instanceCurrentPosition < 0 && orderEffectOnPosition > 0)) {
            // This is a reducing order. The original code would `return true;` here.
            // If we want reducing orders (even if no limit is hit) to always be allowed
            // past size checks:
            // return true;
            // For now, let's assume reducing orders should still respect size limits unless
            // a loss limit forced an exit.
            // So, this specific block might be redundant or its `return true` needs careful
            // consideration.
            // Let's proceed without an early return here, meaning reducing orders still go
            // through size checks unless loss limit forced an exit.
        }
        // Check instance position limit
        if (Math.abs(projectedNewPosition) > dynPosCap) {
            sendtolog("Order Blocked", 0f, String.format(
                    "Order rejected: Instance position limit of %d would be exceeded. Current: %d, Order: %d %s. New: %d. Instance %s",
                    instanceMaxPositionSize, instanceCurrentPosition, size, (isBuy ? "BUY" : "SELL"),
                    projectedNewPosition, instanceId));
            // Display rejected icon
            displayRejectedOrderIcon(getCurrentMarketPrice());
            return false;
        }
        // Check global position limit using projected value
        int projectedGlobalPosition;
        synchronized (PNL_LOCK) { // Assuming PNL_LOCK also covers globalCurrentPosition or use a dedicated lock
            projectedGlobalPosition = globalCurrentPosition + orderEffectOnPosition;
        }
        if (Math.abs(projectedGlobalPosition) > dynPosCap) {
            sendtolog("Order Blocked", 0f, String.format(
                    "Order rejected: Global position limit of %d would be exceeded. Current Global: %d, Order for instance %s: %d %s. Projected Global: %d",
                    GLOBAL_MAX_POSITION_SIZE, globalCurrentPosition, instanceId, size, (isBuy ? "BUY" : "SELL"),
                    projectedGlobalPosition));
            // Display rejected icon
            displayRejectedOrderIcon(getCurrentMarketPrice());
            return false;
        }
        return true;
    }

    // For backward compatibility, keep the old method but have it call the new one
    private boolean canSendOrder(double estimatedRisk, int size) {
        // Assume it's a buy order for backward compatibility
        return canSendOrder(estimatedRisk, size, true);
    }

    // Example: send a market buy order.
    public boolean sendMarketBuyOrder(final int size, final OrderDuration duration, final double stopLoss,
            final double takeProfit, final double estimatedRisk) {
        if (debug_mode_ordersendercontroller) {
            sendtolog("sendMarketBuyOrder called", (float) size,
                    String.format("Params: duration=%s, stopLoss=%.2f, takeProfit=%.2f, estimatedRisk=%.2f", duration,
                            stopLoss, takeProfit, estimatedRisk));
        }
        if (!canSendOrder(estimatedRisk, size, true)) {
            sendtolog("Market Buy", 0f, "Risk limits exceeded. Order not sent.");
            return false;
        }
        int dynPosCap = getDynamicPositionCap();
        int allowedSizeRaw = clipOrderSize(instanceCurrentPosition, size, /* isBuy */ true, dynPosCap);
        int allowedSize = Math.max(allowedSizeRaw == 0 ? 0 : 1, allowedSizeRaw); // honour min 1 when gate not BLOCKED
        if (allowedSize < size) {
            // Changed: reject instead of clip
            sendtolog("Order Rejected", 0f,
                    "Requested buy size " + size + " would exceed position limit. Order rejected.");
            return false;
        }
        if (allowedSize == 0) {
            sendtolog("Order Blocked", 0f, "Order not sent: would exceed position limit.");
            return false;
        }
        scheduleOrder(() -> {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Market Buy Order", (float) allowedSize,
                        "Sending order from instanceId=" + instanceId + ", algo=" + algo);
            }

            // Generate a client ID for tracking
            String clientId = algo + currentmilliseconds + "Buy";

            // Register which instance created this client ID
            clientIdToInstanceId.put(clientId, instanceId);
            boolean orderSuccess = orderSender.sendMarketBuyOrder(allowedSize, duration, stopLoss, takeProfit,
                    clientId);
            if (!orderSuccess) {
                sendtolog("Market Buy Order", (float) allowedSize, "Order rejected by exchange");
                OrderInfo rejectedOrder = new OrderInfo(
                        "REJECTED_" + currentmilliseconds,
                        clientId,
                        true,
                        OrderType.MARKET,
                        allowedSize,
                        currentMarketPrice,
                        "Market Buy Order (Rejected)",
                        false,
                        null);
                rejectedOrder.setStatus(OrderStatus.REJECTED);
                return;
            }
            String orderId = clientId;
            registerOrder(orderId, clientId, true, OrderType.MARKET, allowedSize, currentMarketPrice,
                    "Market Buy Order", false, null);
            instanceLastBuyOrderTimeMillis = currentmilliseconds;
            globalLastBuyOrderTimeMillis = currentmilliseconds;
        });
        return true;
    }

    // Similarly fixed methods for market sell orders.
    public boolean sendMarketSellOrder(final int size, final OrderDuration duration, final double stopLoss,
            final double takeProfit, final double estimatedRisk) {
        if (debug_mode_ordersendercontroller) {
            sendtolog("sendMarketSellOrder called", (float) size,
                    String.format("Params: duration=%s, stopLoss=%.2f, takeProfit=%.2f, estimatedRisk=%.2f", duration,
                            stopLoss, takeProfit, estimatedRisk));
        }
        if (!canSendOrder(estimatedRisk, size, false)) {
            sendtolog("Market Sell", 0f, "Risk limits exceeded. Order not sent.");
            return false;
        }
        int dynPosCap = getDynamicPositionCap();
        int allowedSizeRaw = clipOrderSize(instanceCurrentPosition, size, /* isBuy */ false, dynPosCap);
        int allowedSize = Math.max(allowedSizeRaw == 0 ? 0 : 1, allowedSizeRaw); // honour min 1 when gate not BLOCKED
        if (allowedSize < size) {
            // Changed: reject instead of clip
            sendtolog("Order Rejected", 0f,
                    "Requested sell size " + size + " would exceed position limit. Order rejected.");
            return false;
        }
        if (allowedSize == 0) {
            sendtolog("Order Blocked", 0f, "Order not sent: would exceed position limit.");
            return false;
        }
        scheduleOrder(() -> {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Market Sell Order", (float) allowedSize,
                        "Sending order from instanceId=" + instanceId + ", algo=" + algo);
            }

            // Generate a client ID for tracking
            String clientId = algo + currentmilliseconds + "Sell";

            // Register which instance created this client ID
            clientIdToInstanceId.put(clientId, instanceId);
            boolean orderSuccess = orderSender.sendMarketSellOrder(allowedSize, duration, stopLoss, takeProfit,
                    clientId);
            if (!orderSuccess) {
                sendtolog("Market Sell Order", (float) allowedSize, "Order rejected by exchange");
                return;
            }
            String orderId = clientId;
            registerOrder(orderId, clientId, false, OrderType.MARKET, allowedSize, currentMarketPrice,
                    "Market Sell Order", false, null);
            instanceLastSellOrderTimeMillis = currentmilliseconds;
            globalLastSellOrderTimeMillis = currentmilliseconds;

            // Mark the clientId as processed to avoid duplicate position updates
            String executionKey = clientId + "_" + size + "_" + currentMarketPrice;
        });
        return true;
    }

    // --- START: Emergency Market Orders (Bypass ALL risk checks, for emergency use
    // only) ---
    /**
     * EMERGENCY: Sends a market buy order bypassing canSendOrder (risk, profit
     * target, and other checks).
     * Use ONLY for emergency flatten/close situations when standard orders fail or
     * checks are problematic.
     * Still respects basic position clipping (clipOrderSize) to prevent overly
     * large orders.
     * Not wired to UI or general logic by default. Intended for programmatic
     * emergency use.
     * 
     * @param size       Order size (must be positive)
     * @param duration   Order duration
     * @param stopLoss   Stop loss price (optional, broker-dependent)
     * @param takeProfit Take profit price (optional, broker-dependent)
     * @return true if order was scheduled, false otherwise
     */
    public boolean sendEmergencyMarketBuyOrder(final int size, final OrderDuration duration, final double stopLoss,
            final double takeProfit) {
        if (debug_mode_ordersendercontroller) {
            sendtolog("sendEmergencyMarketBuyOrder called", (float) size,
                    String.format("Params: duration=%s, stopLoss=%.2f, takeProfit=%.2f. BYPASSING canSendOrder.",
                            duration, stopLoss, takeProfit));
        }
        if (size <= 0) { // Emergency orders must have a positive size
            sendtolog("Emergency Market Buy BLOCKED", (float) size,
                    "Requested size is " + size + ". Emergency orders must have a positive size.");
            return false;
        }
        // Basic clipping still applies to prevent truly extreme sizes relative to max
        // position.
        int clippedSize = clipOrderSize(instanceCurrentPosition, size, true, instanceMaxPositionSize);

        final int finalSizeToSend;
        if (clippedSize == 0 && size > 0) { // If clipping results in zero for a positive requested size
            sendtolog("Emergency Market Buy BLOCKED by clipOrderSize", (float) size,
                    "Position limit prevents sending order of size " + size + ". Clipped size is 0.");
            return false;
        } else if (clippedSize < size && clippedSize > 0) { // If clipped to a smaller, but still positive size
            sendtolog("Emergency Market Buy Warning", 0f, "Requested buy size " + size + " clipped to " + clippedSize
                    + " due to position limit. Sending clipped size " + clippedSize + ".");
            finalSizeToSend = clippedSize;
        } else { // No clipping or clipOrderSize returned original size
            finalSizeToSend = size;
        }

        scheduleOrder(() -> {
            if (debug_mode_ordersendercontroller) {
                sendtolog("EMERGENCY Market Buy Order", (float) finalSizeToSend,
                        "Sending order from instanceId=" + instanceId + ", algo=" + algo);
            }
            String clientId = algo + currentmilliseconds + "EmergencyMarketBuy";
            clientIdToInstanceId.put(clientId, instanceId);
            boolean orderSuccess = orderSender.sendMarketBuyOrder(finalSizeToSend, duration, stopLoss, takeProfit,
                    clientId);
            if (!orderSuccess) {
                sendtolog("EMERGENCY Market Buy Order FAILED", (float) finalSizeToSend,
                        "Order rejected by exchange/sender");
                return;
            }
            String orderId = clientId;
            registerOrder(orderId, clientId, true, OrderType.MARKET, finalSizeToSend, currentMarketPrice,
                    "EMERGENCY Market Buy Order", false, null);
            instanceLastBuyOrderTimeMillis = currentmilliseconds;
            globalLastBuyOrderTimeMillis = currentmilliseconds;
        });
        return true;
    }

    /**
     * EMERGENCY: Sends a market sell order bypassing canSendOrder (risk, profit
     * target, and other checks).
     * Use ONLY for emergency flatten/close situations when standard orders fail or
     * checks are problematic.
     * Still respects basic position clipping (clipOrderSize) to prevent overly
     * large orders.
     * Not wired to UI or general logic by default. Intended for programmatic
     * emergency use.
     * 
     * @param size       Order size (must be positive)
     * @param duration   Order duration
     * @param stopLoss   Stop loss price (optional, broker-dependent)
     * @param takeProfit Take profit price (optional, broker-dependent)
     * @return true if order was scheduled, false otherwise
     */
    public boolean sendEmergencyMarketSellOrder(final int size, final OrderDuration duration, final double stopLoss,
            final double takeProfit) {
        if (debug_mode_ordersendercontroller) {
            sendtolog("sendEmergencyMarketSellOrder called", (float) size,
                    String.format("Params: duration=%s, stopLoss=%.2f, takeProfit=%.2f. BYPASSING canSendOrder.",
                            duration, stopLoss, takeProfit));
        }
        if (size <= 0) { // Emergency orders must have a positive size
            sendtolog("Emergency Market Sell BLOCKED", (float) size,
                    "Requested size is " + size + ". Emergency orders must have a positive size.");
            return false;
        }
        // Basic clipping still applies.
        int clippedSize = clipOrderSize(instanceCurrentPosition, size, false, instanceMaxPositionSize);

        final int finalSizeToSend;
        if (clippedSize == 0 && size > 0) {
            sendtolog("Emergency Market Sell BLOCKED by clipOrderSize", (float) size,
                    "Position limit prevents sending order of size " + size + ". Clipped size is 0.");
            return false;
        } else if (clippedSize < size && clippedSize > 0) {
            sendtolog("Emergency Market Sell Warning", 0f, "Requested sell size " + size + " clipped to " + clippedSize
                    + " due to position limit. Sending clipped size " + clippedSize + ".");
            finalSizeToSend = clippedSize;
        } else {
            finalSizeToSend = size;
        }

        scheduleOrder(() -> {
            if (debug_mode_ordersendercontroller) {
                sendtolog("EMERGENCY Market Sell Order", (float) finalSizeToSend,
                        "Sending order from instanceId=" + instanceId + ", algo=" + algo);
            }
            String clientId = algo + currentmilliseconds + "EmergencyMarketSell";
            clientIdToInstanceId.put(clientId, instanceId);
            boolean orderSuccess = orderSender.sendMarketSellOrder(finalSizeToSend, duration, stopLoss, takeProfit,
                    clientId);
            if (!orderSuccess) {
                sendtolog("EMERGENCY Market Sell Order FAILED", (float) finalSizeToSend,
                        "Order rejected by exchange/sender");
                return;
            }
            String orderId = clientId;
            registerOrder(orderId, clientId, false, OrderType.MARKET, finalSizeToSend, currentMarketPrice,
                    "EMERGENCY Market Sell Order", false, null);
            instanceLastSellOrderTimeMillis = currentmilliseconds;
            globalLastSellOrderTimeMillis = currentmilliseconds;
        });
        return true;
    }
    // --- END: Emergency Market Orders ---

    // Limit order methods.
    public boolean sendLimitBuyOrder(final int size, final double limitPrice, final OrderDuration duration,
            final double stopLoss, final double takeProfit, final double estimatedRisk) {
        int allowedSize = clipOrderSize(instanceCurrentPosition, size, true, instanceMaxPositionSize);
        if (allowedSize < size) {
            // Changed: reject instead of clip
            sendtolog("Order Rejected", 0f,
                    "Requested limit buy size " + size + " would exceed position limit. Order rejected.");
            return false;
        }
        if (allowedSize == 0) {
            sendtolog("Order Blocked", 0f, "Order not sent: would exceed position limit.");
            return false;
        }
        if (!canSendOrder(estimatedRisk, allowedSize, true)) {
            sendtolog("Limit Buy", 0f, "Risk limits exceeded. Order not sent.");
            return false;
        }
        if (limitPrice <= 0) {
            sendtolog("Limit Buy", 0f, "Invalid price. Order not sent.");
            return false;
        }
        double roundedLimitPrice = Math.round(limitPrice / pips) * pips;
        scheduleOrder(() -> {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Limit Buy Order", (float) allowedSize, "Sending order at " + roundedLimitPrice);
            }

            // Generate a client ID for tracking
            String clientId = algo + currentmilliseconds + "LimitBuy";
            boolean orderSuccess = orderSender.sendLimitBuyOrder(allowedSize, roundedLimitPrice, duration, stopLoss,
                    takeProfit, clientId);
            if (!orderSuccess) {
                sendtolog("Limit Buy Order", (float) allowedSize, "Order rejected by exchange");
                return;
            }

            // Register the order in our tracking system
            registerOrder(clientId, clientId, true, OrderType.LIMIT, size, roundedLimitPrice,
                    "Limit Buy Order", true, null);

            // Position management is now handled in onOrderExecuted

            instanceLastBuyOrderTimeMillis = currentmilliseconds;
            globalLastBuyOrderTimeMillis = currentmilliseconds;
        });
        return true;
    }

    public boolean sendLimitSellOrder(final int size, final double limitPrice, final OrderDuration duration,
            final double stopLoss, final double takeProfit, final double estimatedRisk) {
        int allowedSize = clipOrderSize(instanceCurrentPosition, size, false, instanceMaxPositionSize);
        if (allowedSize < size) {
            // Changed: reject instead of clip
            sendtolog("Order Rejected", 0f,
                    "Requested limit sell size " + size + " would exceed position limit. Order rejected.");
            return false;
        }
        if (allowedSize == 0) {
            sendtolog("Order Blocked", 0f, "Order not sent: would exceed position limit.");
            return false;
        }
        if (!canSendOrder(estimatedRisk, allowedSize, false)) {
            sendtolog("Limit Sell", 0f, "Risk limits exceeded. Order not sent.");
            return false;
        }
        if (limitPrice <= 0) {
            sendtolog("Limit Sell", 0f, "Invalid price. Order not sent.");
            return false;
        }
        double roundedLimitPrice = Math.round(limitPrice / pips) * pips;
        scheduleOrder(() -> {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Limit Sell Order", (float) allowedSize, "Sending order at " + roundedLimitPrice);
            }
            String clientId = algo + currentmilliseconds + "LimitSell";
            boolean orderSuccess = orderSender.sendLimitSellOrder(allowedSize, roundedLimitPrice, duration, stopLoss,
                    takeProfit, clientId);
            if (!orderSuccess) {
                sendtolog("Limit Sell Order", (float) allowedSize, "Order rejected by exchange");
                return;
            }
            registerOrder(clientId, clientId, false, OrderType.LIMIT, allowedSize, roundedLimitPrice,
                    "Limit Sell Order", true, null);
            instanceLastSellOrderTimeMillis = currentmilliseconds;
            globalLastSellOrderTimeMillis = currentmilliseconds;
        });
        return true;
    }

    // Overloaded methods without estimatedRisk parameter.
    public boolean sendMarketBuyOrder(final int size, final OrderDuration duration, final double stopLoss,
            final double takeProfit) {
        return sendMarketBuyOrder(size, duration, stopLoss, takeProfit, 0.0);
    }

    public boolean sendMarketSellOrder(final int size, final OrderDuration duration, final double stopLoss,
            final double takeProfit) {
        return sendMarketSellOrder(size, duration, stopLoss, takeProfit, 0.0);
    }

    public boolean sendLimitBuyOrder(final int size, final double limitPrice, final OrderDuration duration,
            final double stopLoss, final double takeProfit) {
        return sendLimitBuyOrder(size, limitPrice, duration, stopLoss, takeProfit, 0.0);
    }

    public boolean sendLimitSellOrder(final int size, final double limitPrice, final OrderDuration duration,
            final double stopLoss, final double takeProfit) {
        return sendLimitSellOrder(size, limitPrice, duration, stopLoss, takeProfit, 0.0);
    }

    // Trailing stop order methods.
    public boolean sendTrailingStopBuyOrder(final int size, final double initialStopPrice, final int trailingStep,
            final OrderDuration duration, final double stopLoss, final double takeProfit, final double estimatedRisk) {
        int allowedSize = clipOrderSize(instanceCurrentPosition, size, true, instanceMaxPositionSize);
        if (allowedSize < size) {
            // Changed: reject instead of clip
            sendtolog("Order Rejected", 0f,
                    "Requested trailing stop buy size " + size + " would exceed position limit. Order rejected.");
            return false;
        }
        if (allowedSize == 0) {
            sendtolog("Order Blocked", 0f, "Order not sent: would exceed position limit.");
            return false;
        }
        if (!canSendOrder(estimatedRisk, allowedSize, true)) {
            sendtolog("Trailing Stop Buy", 0f, "Risk limits exceeded. Order not sent.");
            return false;
        }
        scheduleOrder(() -> {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Trailing Stop Buy Order", (float) allowedSize,
                        "Sending order with initial stop " + initialStopPrice);
            }
            String clientId = algo + currentmilliseconds + "TrailStopBuy";
            boolean orderSuccess = orderSender.sendTrailingStopBuyOrder(allowedSize, initialStopPrice, trailingStep,
                    duration, stopLoss, takeProfit, clientId);
            if (!orderSuccess) {
                sendtolog("Trailing Stop Buy Order", (float) allowedSize, "Order rejected by exchange");
                return;
            }
            registerOrder(clientId, clientId, true, OrderType.TRAIL_STOP, allowedSize, initialStopPrice,
                    "Trailing Stop Buy Order", false, null);
            instanceLastBuyOrderTimeMillis = currentmilliseconds;
            globalLastBuyOrderTimeMillis = currentmilliseconds;
        });
        return true;
    }

    public boolean sendTrailingStopSellOrder(final int size, final double initialStopPrice, final int trailingStep,
            final OrderDuration duration, final double stopLoss, final double takeProfit, final double estimatedRisk) {
        int allowedSize = clipOrderSize(instanceCurrentPosition, size, false, instanceMaxPositionSize);
        if (allowedSize < size) {
            // Changed: reject instead of clip
            sendtolog("Order Rejected", 0f,
                    "Requested trailing stop sell size " + size + " would exceed position limit. Order rejected.");
            return false;
        }
        if (allowedSize == 0) {
            sendtolog("Order Blocked", 0f, "Order not sent: would exceed position limit.");
            return false;
        }
        if (!canSendOrder(estimatedRisk, allowedSize, false)) {
            sendtolog("Trailing Stop Sell", 0f, "Risk limits exceeded. Order not sent.");
            return false;
        }
        scheduleOrder(() -> {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Trailing Stop Sell Order", (float) allowedSize,
                        "Sending order with initial stop " + initialStopPrice);
            }
            String clientId = algo + currentmilliseconds + "TrailStopSell";
            boolean orderSuccess = orderSender.sendTrailingStopSellOrder(allowedSize, initialStopPrice, trailingStep,
                    duration, stopLoss, takeProfit, clientId);
            if (!orderSuccess) {
                sendtolog("Trailing Stop Sell Order", (float) allowedSize, "Order rejected by exchange");
                return;
            }
            registerOrder(clientId, clientId, false, OrderType.TRAIL_STOP, allowedSize, initialStopPrice,
                    "Trailing Stop Sell Order", false, null);
            instanceLastSellOrderTimeMillis = currentmilliseconds;
            globalLastSellOrderTimeMillis = currentmilliseconds;
        });
        return true;
    }

    // OCO order methods.
    public boolean sendOcoBuyLimitBuyStopOrder(final int size, final double limitPrice, final double stopPrice,
            final OrderDuration duration, final double stopLoss, final double takeProfit, final double estimatedRisk) {
        int allowedSize = clipOrderSize(instanceCurrentPosition, size, true, instanceMaxPositionSize);
        if (allowedSize < size) {
            // Changed: reject instead of clip
            sendtolog("Order Rejected", 0f,
                    "Requested OCO buy size " + size + " would exceed position limit. Order rejected.");
            return false;
        }
        if (allowedSize == 0) {
            sendtolog("Order Blocked", 0f, "Order not sent: would exceed position limit.");
            return false;
        }
        if (!canSendOrder(estimatedRisk, allowedSize, true)) {
            sendtolog("OCO Buy Limit/Stop", 0f, "Risk limits exceeded. Order not sent.");
            return false;
        }
        if (limitPrice <= 0 || stopPrice <= 0) {
            sendtolog("OCO Buy Limit/Stop", 0f, "Invalid price. Order not sent.");
            return false;
        }
        scheduleOrder(() -> {
            if (debug_mode_ordersendercontroller) {
                sendtolog("OCO Buy Limit/Stop Order", (float) allowedSize,
                        "Sending order at limit " + limitPrice + " / stop " + stopPrice);
            }
            String clientId = algo + currentmilliseconds + "OcoBuyLimitStop";
            boolean orderSuccess = orderSender.sendOcoBuyLimitBuyStopOrder(allowedSize, limitPrice, stopPrice, duration,
                    stopLoss, takeProfit, clientId);
            if (!orderSuccess) {
                sendtolog("OCO Buy Limit/Stop Order", (float) allowedSize, "Order rejected by exchange");
                return;
            }
            registerOrder(clientId, clientId, true, OrderType.OCO, allowedSize, limitPrice,
                    "OCO Buy Limit/Stop Order", true, null);
            instanceLastBuyOrderTimeMillis = currentmilliseconds;
            globalLastBuyOrderTimeMillis = currentmilliseconds;
        });
        return true;
    }

    public boolean sendOcoSellLimitSellStopOrder(final int size, final double limitPrice, final double stopPrice,
            final OrderDuration duration, final double stopLoss, final double takeProfit, final double estimatedRisk) {
        int allowedSize = clipOrderSize(instanceCurrentPosition, size, false, instanceMaxPositionSize);
        if (allowedSize < size) {
            // Changed: reject instead of clip
            sendtolog("Order Rejected", 0f,
                    "Requested OCO sell size " + size + " would exceed position limit. Order rejected.");
            return false;
        }
        if (allowedSize == 0) {
            sendtolog("Order Blocked", 0f, "Order not sent: would exceed position limit.");
            return false;
        }
        if (!canSendOrder(estimatedRisk, allowedSize, false)) {
            sendtolog("OCO Sell Limit/Stop", 0f, "Risk limits exceeded. Order not sent.");
            return false;
        }
        if (limitPrice <= 0 || stopPrice <= 0) {
            sendtolog("OCO Sell Limit/Stop", 0f, "Invalid price. Order not sent.");
            return false;
        }
        scheduleOrder(() -> {
            if (debug_mode_ordersendercontroller) {
                sendtolog("OCO Sell Limit/Stop Order", (float) allowedSize,
                        "Sending order at limit " + limitPrice + " / stop " + stopPrice);
            }
            String clientId = algo + currentmilliseconds + "OcoSellLimitStop";
            boolean orderSuccess = orderSender.sendOcoSellLimitSellStopOrder(allowedSize, limitPrice, stopPrice,
                    duration, stopLoss, takeProfit, clientId);
            if (!orderSuccess) {
                sendtolog("OCO Sell Limit/Stop Order", (float) allowedSize, "Order rejected by exchange");
                return;
            }
            registerOrder(clientId, clientId, false, OrderType.OCO, allowedSize, limitPrice,
                    "OCO Sell Limit/Stop Order", true, null);
            instanceLastSellOrderTimeMillis = currentmilliseconds;
            globalLastSellOrderTimeMillis = currentmilliseconds;
        });
        return true;
    }

    // Overloaded OCO methods without estimatedRisk.
    public boolean sendOcoBuyLimitBuyStopOrder(final int size, final double limitPrice, final double stopPrice,
            final OrderDuration duration, final double stopLoss, final double takeProfit) {
        return sendOcoBuyLimitBuyStopOrder(size, limitPrice, stopPrice, duration, stopLoss, takeProfit, 0.0);
    }

    public boolean sendOcoBuyLimitBuyTrailStopOrder(final int size, final double limitPrice,
            final double initialStopPrice, final int trailingStep, final OrderDuration duration, final double stopLoss,
            final double takeProfit, final double estimatedRisk) {
        int allowedSize = clipOrderSize(instanceCurrentPosition, size, true, instanceMaxPositionSize);
        if (allowedSize < size) {
            // Changed: reject instead of clip
            sendtolog("Order Rejected", 0f,
                    "Requested OCO buy trail stop size " + size + " would exceed position limit. Order rejected.");
            return false;
        }
        if (allowedSize == 0) {
            sendtolog("Order Blocked", 0f, "Order not sent: would exceed position limit.");
            return false;
        }
        if (!canSendOrder(estimatedRisk, allowedSize, true)) {
            sendtolog("OCO Buy Limit/Trail Stop", 0f, "Risk limits exceeded. Order not sent.");
            return false;
        }
        if (limitPrice <= 0 || initialStopPrice <= 0 || trailingStep <= 0) {
            sendtolog("OCO Buy Limit/Trail Stop", 0f, "Invalid price or trailing step. Order not sent.");
            return false;
        }
        scheduleOrder(() -> {
            if (debug_mode_ordersendercontroller) {
                sendtolog("OCO Buy Limit/Trail Stop Order", (float) allowedSize, "Sending order at limit " + limitPrice
                        + " / initial stop " + initialStopPrice + " / trail " + trailingStep);
            }
            String clientId = algo + currentmilliseconds + "OcoBuyLimitTrailStop";
            boolean orderSuccess = orderSender.sendOcoBuyLimitBuyTrailStopOrder(allowedSize, limitPrice,
                    initialStopPrice, trailingStep, duration, stopLoss, takeProfit, clientId);
            if (!orderSuccess) {
                sendtolog("OCO Buy Limit/Trail Stop Order", (float) allowedSize, "Order rejected by exchange");
                return;
            }
            registerOrder(clientId, clientId, true, OrderType.OCO, allowedSize, limitPrice,
                    "OCO Buy Limit/Trail Stop Order", true, null);
            instanceLastBuyOrderTimeMillis = currentmilliseconds;
            globalLastBuyOrderTimeMillis = currentmilliseconds;
        });
        return true;
    }

    public boolean sendOcoSellLimitSellTrailStopOrder(final int size, final double limitPrice,
            final double initialStopPrice, final int trailingStep, final OrderDuration duration, final double stopLoss,
            final double takeProfit, final double estimatedRisk) {
        int allowedSize = clipOrderSize(instanceCurrentPosition, size, false, instanceMaxPositionSize);
        if (allowedSize < size) {
            // Changed: reject instead of clip
            sendtolog("Order Rejected", 0f,
                    "Requested OCO sell trail stop size " + size + " would exceed position limit. Order rejected.");
            return false;
        }
        if (allowedSize == 0) {
            sendtolog("Order Blocked", 0f, "Order not sent: would exceed position limit.");
            return false;
        }
        if (!canSendOrder(estimatedRisk, allowedSize, false)) {
            sendtolog("OCO Sell Limit/Trail Stop", 0f, "Risk limits exceeded. Order not sent.");
            return false;
        }
        if (limitPrice <= 0 || initialStopPrice <= 0 || trailingStep <= 0) {
            sendtolog("OCO Sell Limit/Trail Stop", 0f, "Invalid price or trailing step. Order not sent.");
            return false;
        }
        scheduleOrder(() -> {
            if (debug_mode_ordersendercontroller) {
                sendtolog("OCO Sell Limit/Trail Stop Order", (float) allowedSize, "Sending order at limit " + limitPrice
                        + " / initial stop " + initialStopPrice + " / trail " + trailingStep);
            }
            String clientId = algo + currentmilliseconds + "OcoSellLimitTrailStop";
            boolean orderSuccess = orderSender.sendOcoSellLimitSellTrailStopOrder(allowedSize, limitPrice,
                    initialStopPrice, trailingStep, duration, stopLoss, takeProfit, clientId);
            if (!orderSuccess) {
                sendtolog("OCO Sell Limit/Trail Stop Order", (float) allowedSize, "Order rejected by exchange");
                return;
            }
            registerOrder(clientId, clientId, false, OrderType.OCO, allowedSize, limitPrice,
                    "OCO Sell Limit/Trail Stop Order", true, null);
            instanceLastSellOrderTimeMillis = currentmilliseconds;
            globalLastSellOrderTimeMillis = currentmilliseconds;
        });
        return true;
    }

    private boolean flattenInProgress = false;

    // Instance flatten method with enhanced debugging and forced check.
    public boolean instanceFlattenPositions() {
        sendtolog("instanceFlattenPositions", 0f, "Starting flatten for instance " + instanceId);
        synchronized (this) {
            if (flattenInProgress) {
                sendtolog("instanceFlattenPositions", 0f, "Flatten already in progress for " + instanceId);
                return false;
            }
            flattenInProgress = true;
        }
        try {
            int currentPos;
            synchronized (globalPositions) {
                currentPos = instanceCurrentPosition;
                sendtolog("instanceFlattenPositions", 0f, "Current instance position = " + currentPos);
            }
            if (currentPos == 0) {
                sendtolog("instanceFlattenPositions", 0f, "No position to flatten for " + instanceId);
                return true;
            }

            if (currentPos > 0) {
                sendtolog("instanceFlattenPositions", 0f, "Attempting to send market SELL for " + currentPos);

                // Generate a client ID
                String clientId = algo + currentmilliseconds + "FlattenSell";
                clientIdToInstanceId.put(clientId, instanceId);

                boolean orderSuccess = orderSender.sendMarketSellOrder(currentPos, OrderDuration.GTC, 0, 0, clientId);

                if (orderSuccess) {
                    // Register the order
                    String orderId = clientId;
                    registerOrder(orderId, clientId, false, OrderType.MARKET, currentPos, currentMarketPrice,
                            "Market Sell Flatten Order", false, null);

                    sendtolog("instanceFlattenPositions", 0f, "Flattened long position of " + currentPos + " units");
                    return true;
                } else {
                    sendtolog("instanceFlattenPositions", 0f, "Failed to send flatten order for " + instanceId);
                    return false;
                }
            } else {
                int posToBuy = Math.abs(currentPos);
                sendtolog("instanceFlattenPositions", 0f, "Attempting to send market BUY for " + posToBuy);

                // Generate a client ID
                String clientId = algo + currentmilliseconds + "FlattenBuy";
                clientIdToInstanceId.put(clientId, instanceId);

                boolean orderSuccess = orderSender.sendMarketBuyOrder(posToBuy, OrderDuration.GTC, 0, 0, clientId);

                if (orderSuccess) {
                    // Register the order
                    String orderId = clientId;
                    registerOrder(orderId, clientId, true, OrderType.MARKET, posToBuy, currentMarketPrice,
                            "Market Buy Flatten Order", false, null);

                    sendtolog("instanceFlattenPositions", 0f, "Flattened short position of " + posToBuy + " units");
                    return true;
                } else {
                    sendtolog("instanceFlattenPositions", 0f, "Failed to send flatten order for " + instanceId);
                    return false;
                }
            }
        } finally {
            synchronized (this) {
                flattenInProgress = false;
            }
            sendtolog("instanceFlattenPositions", 0f, "Exiting flatten for instance " + instanceId);
        }
    }

    // Updated global flatten method with enhanced debugging.
    public static boolean globalFlattenPositions() {
        sendtolog("globalFlattenPositions", 0f, "Global flatten initiated.");
        List<OrderSenderControllerV2> instanceList;
        synchronized (globalPositions) {
            if (globalPositions.isEmpty() || globalCurrentPosition == 0) {
                sendtolog("globalFlattenPositions", 0f, "No positions to flatten globally.");
                return true;
            }
            sendtolog("globalFlattenPositions", 0f, "Global positions count: " + globalPositions.size() +
                    ", globalCurrentPosition=" + globalCurrentPosition);
            instanceList = new ArrayList<>(instances);
        }
        boolean overallSuccess = true;
        for (OrderSenderControllerV2 instance : instanceList) {
            sendtolog("globalFlattenPositions", 0f, "Flattening instance " + instance.instanceId);
            boolean success = instance.instanceFlattenPositions();
            if (!success) {
                overallSuccess = false;
                sendtolog("globalFlattenPositions", 0f, "Instance " + instance.instanceId + " failed to flatten.");
            }
        }
        synchronized (globalPositions) {
            globalCurrentPosition = globalPositions.stream().mapToInt(p -> p.quantity).sum();
            sendtolog("globalFlattenPositions", 0f, "After flatten, globalCurrentPosition=" + globalCurrentPosition);
        }
        if (overallSuccess) {
            sendtolog("globalFlattenPositions", 0f, "Successfully flattened positions across all instances.");
        } else {
            sendtolog("globalFlattenPositions", 0f, "Some instances failed to flatten positions.");
        }
        return overallSuccess;
    }

    // Optimize the scheduleOrder method to reduce logging and improve performance
    private void scheduleOrder(Runnable orderTask) {
        if (isGlobalExecutorShutdown || globalExecutor.isShutdown() || globalExecutor.isTerminated()) {
            sendtolog("scheduleOrder", 0f, "Order scheduling skipped: globalExecutor is shut down.");
            Log.warn("OrderSenderControllerV2: Attempted to schedule order after executor shutdown. Skipping.");
            return;
        }
        long now = System.nanoTime();
        long elapsed = now - globalLastOrderTime.get();

        if (ENABLE_VERBOSE_LOGGING) {
            sendtolog("scheduleOrder", 0f, "Elapsed since last order: " + elapsed + " ns");
        }

        if (elapsed >= minOrderIntervalNano) {
            globalLastOrderTime.set(now);
            globalExecutor.execute(() -> {
                if (ENABLE_VERBOSE_LOGGING) {
                    sendtolog("scheduleOrder", 0f, "Executing orderTask immediately.");
                }
                synchronized (this) {
                    orderTask.run();
                }
            });
        } else {
            long delay = minOrderIntervalNano - elapsed;
            if (ENABLE_VERBOSE_LOGGING) {
                sendtolog("scheduleOrder", 0f, "Scheduling orderTask with delay: " + delay + " ns");
            }
            globalExecutor.schedule(() -> {
                globalLastOrderTime.set(System.nanoTime());
                if (ENABLE_VERBOSE_LOGGING) {
                    sendtolog("scheduleOrder", 0f, "Executing delayed orderTask.");
                }
                synchronized (this) {
                    orderTask.run();
                }
            }, delay, TimeUnit.NANOSECONDS);
        }
        last = System.nanoTime();
    }

    // Optimize the updatePositionDisplays method to reduce UI updates and logging
    private void updatePositionDisplays(int oldGlobalPos, int newGlobalPos) {
        if (flattenInProgress) {
            if (ENABLE_VERBOSE_LOGGING) {
                sendtolog("updatePositionDisplays", 0f,
                        "Flatten in progress for instance " + instanceId + "; skipping UI update.");
            }
            return;
        }

        // Only update UI if it's visible to avoid unnecessary Swing operations
        if (testUIFrame != null && testUIFrame.isVisible()) {
            long currentTime = System.currentTimeMillis();
            if (lastUpdateTime == null || (currentTime - lastUpdateTime) > 250) { // Update at most 4 times per second
                lastUpdateTime = currentTime;
                // C-4: Copy fields under lock, then update UI
                int instancePosSnapshot;
                int globalPosSnapshot;
                synchronized (this) {
                    instancePosSnapshot = instanceCurrentPosition;
                }
                synchronized (OrderSenderControllerV2.class) {
                    globalPosSnapshot = globalCurrentPosition;
                }
                SwingUtilities.invokeLater(() -> {
                    try {
                        Component[] components = ((Container) testUIFrame.getContentPane().getComponent(0))
                                .getComponents();
                        for (Component c : components) {
                            if (c instanceof JTextField) {
                                JTextField field = (JTextField) c;
                                if ("instancePosition".equals(field.getName())) {
                                    field.setText(String.valueOf(instancePosSnapshot));
                                } else if ("globalPosition".equals(field.getName())) {
                                    field.setText(String.valueOf(globalPosSnapshot));
                                }
                            }
                        }
                    } catch (Exception e) {
                        if (ENABLE_VERBOSE_LOGGING) {
                            sendtolog("updatePositionDisplays", 0f, "Error updating UI fields: " + e.getMessage());
                        }
                    }
                });
            }
        }

        if (ENABLE_VERBOSE_LOGGING) {
            sendtolog("updatePositionDisplays", 0f, "Position Update: Global: " + oldGlobalPos + " -> " + newGlobalPos +
                    ", Instance: " + instanceId + " = " + instanceCurrentPosition);
        }

        // First check if we already have a UI instance
        if (this.ui != null && (oldGlobalPos != newGlobalPos || lastInstancePosition != instanceCurrentPosition)) {
            lastInstancePosition = instanceCurrentPosition;

            // Update the OrdersTestUIV3 with the latest position information
            this.ui.updatePositions(instanceCurrentPosition, globalCurrentPosition);
            this.ui.updateGlobalPnL(getGlobalUnrealizedPnL(), getGlobalRealizedPnL(), getGlobalTotalPnL());
            this.ui.updateAccountAndOrders(LastAccountBalance, workingSellOrders, workingBuyOrders,
                    instanceCurrentPosition);
            this.ui.logPosition(instanceCurrentPosition, workingBuyOrders, workingSellOrders);
        }
    }

    // Add a field to track the last instance position to avoid redundant updates
    private int lastInstancePosition = 0;

    public double getCurrentDailyLoss() {
        return globalCurrentDailyLoss;
    }

    public int getCurrentPosition() {
        return globalCurrentPosition;
    }

    public double getInstanceDailyLoss() {
        return instanceRealizedPnL;
    }

    public int getInstancePosition() {
        return instanceCurrentPosition;
    }

    // Optimize the sendtolog method to reduce overhead
    // Cache for time formatting to reduce object allocations
    private static volatile long cachedNanoseconds = -1;
    private static volatile String cachedTimePrefix = "";
    private static final String LOG_PREFIX = "OrderSenderControllerV2: ";
    
    // Optimized time prefix generation with caching
    private static String getTimePrefixOptimized(long nanoseconds) {
        // Use cached result if timestamp hasn't changed (reduces formatNanosecondsToTime calls)
        if (cachedNanoseconds != nanoseconds) {
            cachedNanoseconds = nanoseconds;
            cachedTimePrefix = "[" + formatNanosecondsToTime(nanoseconds) + "] ";
        }
        return cachedTimePrefix;
    }

    public static void sendtolog(String S, float F, String S1) {
        if (Debug_Orders || debug_mode_ordersendercontroller_listner || debug_mode_ordersendercontroller) {
            // Use StringBuilder to avoid multiple string concatenations and intermediate objects
            StringBuilder sb = new StringBuilder(128); // Pre-allocate reasonable capacity
            sb.append(getTimePrefixOptimized(currentnanoseconds))
              .append(LOG_PREFIX)
              .append(S)
              .append(' ')
              .append(F)
              .append(' ')
              .append(S1);
            OrderSenderControllerDebugUI.appendToLog(sb.toString());
        }
    }

    public static void sendtolog(String S) {
        if (Debug_Orders || debug_mode_ordersendercontroller_listner || debug_mode_ordersendercontroller) {
            // Use StringBuilder to avoid string concatenations
            StringBuilder sb = new StringBuilder(96); // Pre-allocate reasonable capacity
            sb.append(getTimePrefixOptimized(currentnanoseconds))
              .append(LOG_PREFIX)
              .append(S);
            OrderSenderControllerDebugUI.appendToLog(sb.toString());
        }
    }

    public static void sendtoOrderStatusLog(String S, float F, String S1) {
        if (Debug_Orders || debug_mode_ordersendercontroller_listner || debug_mode_ordersendercontroller) {
            // Use StringBuilder to avoid multiple string concatenations and intermediate objects
            StringBuilder sb = new StringBuilder(128); // Pre-allocate reasonable capacity
            sb.append(getTimePrefixOptimized(currentnanoseconds))
              .append(LOG_PREFIX)
              .append(S)
              .append(' ')
              .append(F)
              .append(' ')
              .append(S1);
            OrderSenderControllerDebugUI.appendToOrderStatusLog(sb.toString());
        }
    }

    public static void sendtoOrderStatusLog(String S) {
        if (Debug_Orders || debug_mode_ordersendercontroller_listner || debug_mode_ordersendercontroller) {
            // Use StringBuilder to avoid string concatenations
            StringBuilder sb = new StringBuilder(96); // Pre-allocate reasonable capacity
            sb.append(getTimePrefixOptimized(currentnanoseconds))
              .append(LOG_PREFIX)
              .append(S);
            OrderSenderControllerDebugUI.appendToOrderStatusLog(sb.toString());
        }
    }

    public static String reportOpenPositions() {
        StringBuilder sb = new StringBuilder("Open Positions:\n");
        List<OrderSenderControllerV2> snap;
        synchronized (instances) {
            snap = new ArrayList<>(instances);
        }
        for (OrderSenderControllerV2 osc : snap) {
            if (osc.instanceCurrentPosition != 0) {
                sb.append(osc.alias).append(": ").append(osc.instanceCurrentPosition).append("\n");
            }
        }
        return sb.toString();
    }

    public boolean hasTimePassedSinceLastOrder(long seconds, String orderType) {
        long lastOrderTimeMillis;
        switch (orderType.toLowerCase()) {
            case "buy":
                lastOrderTimeMillis = instanceLastBuyOrderTimeMillis;
                break;
            case "sell":
                lastOrderTimeMillis = instanceLastSellOrderTimeMillis;
                break;
            default:
                return false;
        }
        long thresholdMillis = currentmilliseconds - (seconds * 1000);
        return lastOrderTimeMillis < thresholdMillis;
    }

    public static boolean hasTimePassedSinceLastGlobalOrder(long seconds, String orderType) {
        long lastOrderTimeMillis;
        switch (orderType.toLowerCase()) {
            case "buy":
                lastOrderTimeMillis = globalLastBuyOrderTimeMillis;
                break;
            case "sell":
                lastOrderTimeMillis = globalLastSellOrderTimeMillis;
                break;
            default:
                return false;
        }
        long thresholdMillis = currentmilliseconds - (seconds * 1000);
        return lastOrderTimeMillis < thresholdMillis;
    }

    public void showTestUI() {
        if (!ENABLE_TEST_UI) {
            return;
        }
        // A-5: Guard against headless mode
        if (GraphicsEnvironment.isHeadless()) {
            Log.info("OrderSenderControllerV2: Headless mode detected, skipping Test UI creation.");
            return;
        }
        if (testUIInitialized) {
            return; // Test UI already initialized
        }
        testUIInitialized = true;
        SwingUtilities.invokeLater(() -> {
            try {
                JFrame frame = new JFrame("Order Sender Test UI");
                testUIFrame = frame;
                frame.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
                frame.addWindowListener(new WindowAdapter() {
                    @Override
                    public void windowClosed(WindowEvent e) {
                        testUIFrame = null;
                        testUIInitialized = false;
                    }
                });

                JPanel inputPanel = new JPanel(new FlowLayout());
                inputPanel.add(new JLabel("Alias:"));
                JTextField aliasField = new JTextField(getTrimmedAlias(alias), 10);
                inputPanel.add(aliasField);

                inputPanel.add(new JLabel("Price:"));
                JTextField priceField = new JTextField(String.valueOf(getCurrentMarketPrice()), 10);
                priceField.setName("price");
                inputPanel.add(priceField);

                inputPanel.add(new JLabel("Display Price:"));
                JTextField displayPriceField = new JTextField(String.valueOf(getDisplayPrice()), 10);
                displayPriceField.setName("displayPrice");
                displayPriceField.setEditable(false);
                inputPanel.add(displayPriceField);

                inputPanel.add(new JLabel("Pips:"));
                JTextField pipsField = new JTextField(String.valueOf(pips), 5);
                inputPanel.add(pipsField);

                JPanel buttonPanel = new JPanel(new GridLayout(0, 2, 5, 5));
                // (Button initialization code omitted for brevity; assume same as original)
                JTextArea statusTextArea = new JTextArea(6, 40);
                statusTextArea.setEditable(false);
                JScrollPane statusScrollPane = new JScrollPane(statusTextArea);

                frame.add(inputPanel, BorderLayout.NORTH);
                frame.add(buttonPanel, BorderLayout.CENTER);
                frame.add(statusScrollPane, BorderLayout.SOUTH);

                frame.pack();
                frame.setLocationRelativeTo(null);
                frame.setVisible(true);
            } catch (Exception ex) {
                Log.info("OrderSenderControllerV2: Error creating Test UI: " + ex.getMessage());
                ex.printStackTrace();
            }
        });
    }

    public void closeTestUI() {
        if (testUIFrame != null) {
            Runnable disposeAction = () -> {
                if (testUIFrame != null) {
                    testUIFrame.dispose();
                    testUIFrame = null;
                    testUIInitialized = false;
                }
            };
            if (SwingUtilities.isEventDispatchThread()) {
                disposeAction.run();
            } else {
                try {
                    SwingUtilities.invokeAndWait(disposeAction);
                } catch (Exception e) {
                    // Fallback: dispose directly if invokeAndWait fails
                    if (testUIFrame != null) {
                        testUIFrame.dispose();
                        testUIFrame = null;
                        testUIInitialized = false;
                    }
                }
            }
        }
        // C-3: Shutdown executor to prevent tasks from running after UI is closed
        if (globalExecutor != null && !globalExecutor.isShutdown()) {
            globalExecutor.shutdownNow();
        }
    }

    public static void setRealtimeStatus(boolean isRealtime) {
        NowRealtime = isRealtime;
    }

    private volatile double BBO_bidContractPrice = 0; // Made volatile
    private volatile double BBO_askContractPrice = 0; // Made volatile
    private volatile double BBO_contractPrice = 0;

    public void onBbo(int bidPrice, int bidSize, int askPrice, int askSize) {

        if (bidPrice <= 0 || askPrice <= 0) {
            return;
        }
        BBO_bidContractPrice = bidPrice * pips;
        BBO_askContractPrice = askPrice * pips;
        BBO_contractPrice = (BBO_bidContractPrice + BBO_askContractPrice) / 2;
    }

    // Add this field to track the max observed priceInPips for this instance
    private double maxObservedPriceInPips = 0.0;

    public synchronized void updateMarketPrice(double priceInPips) {

        double priceToUse = priceInPips;

        double contractPrice = priceToUse * pips;

        currentMarketPrice = contractPrice;
        if (marketPriceReceivedIndicator != null) {
            marketPriceReceivedIndicator.addPoint(contractPrice);
        }
        orderSender.updateMarketPrice(contractPrice);
        // --- Rolling window update (only here) ---
        synchronized (recentMarketPrices) {
            if (recentMarketPrices.size() == ROLLING_WINDOW_SIZE) {
                recentMarketPrices.removeFirst();
            }
            recentMarketPrices.addLast(contractPrice);
        }

        // Only update UI if NowRealtime or simulationMode
        if (NowRealtime || simulationMode) {
            if (testUIFrame != null && testUIFrame.isVisible()) {
                // Ensure all UI updates are performed on the EDT for thread safety
                SwingUtilities.invokeLater(() -> {
                    try {
                        Component[] components = ((Container) testUIFrame.getContentPane().getComponent(0))
                                .getComponents();
                        for (Component c : components) {
                            if (c instanceof JTextField) {
                                JTextField field = (JTextField) c;
                                if ("price".equals(field.getName())) {
                                    field.setText(String.valueOf(priceInPips * pips));
                                } else if ("displayPrice".equals(field.getName())) {
                                    field.setText(String.valueOf(getDisplayPrice()));
                                } else if ("instancePosition".equals(field.getName())) {
                                    field.setText(String.valueOf(instanceCurrentPosition));
                                } else if ("globalPosition".equals(field.getName())) {
                                    field.setText(String.valueOf(globalCurrentPosition));
                                }
                            }
                        }
                    } catch (Exception e) {
                        Log.info("OrderSenderControllerV2: Error updating UI fields: " + e.getMessage());
                    }
                });
            }

            // ═══ CENTRALIZED UI ACQUISITION - ONLY PLACE TO GET UI ═══
            // Only acquire UI if we don't have it yet
            if (this.ui == null) {
                OrdersTestUIV3 uiInstance = OrdersTestUIV3.getInstance(instrumentInfo, this, null);
                if (uiInstance != null) {
                    this.ui = uiInstance; // Assign to interface field

                }
            }

            // Use this.ui for all UI updates (no SwingUtilities.invokeLater needed for
            // interface calls)
            if (this.ui != null) {
                this.ui.updatePrice(contractPrice);
                this.ui.refreshPositionsTable();
                this.ui.setRealtimeStatus(NowRealtime);

                // ═══ UPDATE TIME GATE DATA WITH MARKET PRICE ═══
                var snap = getTimeGateSnapshot();
                this.ui.updateTimeGate(snap.state, snap.positionCap, snap.orderCap, snap.secondsToNext);
            }
        }
        if (instancePosition != null) {
            instancePosition.updatePnL(this.BBO_bidContractPrice, this.BBO_askContractPrice); // Pass BBOs
        }
        synchronized (globalPositions) {
            for (Position pos : globalPositions) {
                pos.updatePnL(this.BBO_bidContractPrice, this.BBO_askContractPrice); // Pass BBOs
            }
        }
        // ─── Update instance‑level draw‑down guard ───
        drawdownGuard.update(instanceRealizedPnL, (instancePosition != null ? instancePosition.unrealizedPnL : 0.0),
                instrumentInfo.multiplier);
        checkDrawdownAndLimits();

        boolean valid = Double.isFinite(priceInPips) && priceInPips > 0;
        long bookmapTimeNanos = currentnanoseconds; // Always use Bookmap time
        inputDataHealthTracker.update(priceInPips, bookmapTimeNanos, valid);
        ready = inputDataHealthTracker.isHealthy();
    }

    // Helper to compute rolling average of recent market prices
    private double getRollingAverageMarketPrice() {
        synchronized (recentMarketPrices) {
            if (recentMarketPrices.isEmpty()) {
                return currentMarketPrice;
            }
            double sum = 0.0;
            for (double v : recentMarketPrices)
                sum += v;
            return sum / recentMarketPrices.size();
        }
    }

    public static double getCurrentMarketPrice() {
        return currentMarketPrice;
    }

    public static double getGlobalUnrealizedPnL() {
        // Return the value updated periodically by the globalAggregatorService
        return aggregatedGlobalUnrealizedPnL;
    }

    // This method will periodically update global aggregates.
    // Batched version of global aggregates update
    private static void updateGlobalAggregates() {
        scheduleGlobalUpdate();
    }

    // Force immediate global update (for critical operations)
    private static void updateGlobalAggregatesImmediate() {
        performGlobalAggregatesUpdate();
    }

    // Schedule a batched global update to reduce frequency
    private static void scheduleGlobalUpdate() {
        synchronized (globalUpdateLock) {
            if (!globalUpdateScheduled) {
                globalUpdateScheduled = true;
                globalAggregatorService.schedule(() -> {
                    try {
                        performGlobalAggregatesUpdate();
                    } finally {
                        synchronized (globalUpdateLock) {
                            globalUpdateScheduled = false;
                        }
                    }
                }, GLOBAL_UPDATE_BATCH_INTERVAL_MS, java.util.concurrent.TimeUnit.MILLISECONDS);
            }
        }
    }

    // Actual implementation of global aggregates calculation
    private static void performGlobalAggregatesUpdate() {
        double currentGlobalUnrealizedSum = 0.0;
        int currentGlobalPositionSum = 0;

        // Single iteration through positions with local variables for efficiency
        for (Position pos : globalPositions) { // globalPositions is CopyOnWriteArrayList, iteration is safe
            if (pos != null && pos.isActive()) {
                currentGlobalUnrealizedSum += pos.unrealizedPnL; // pos.unrealizedPnL is volatile
                currentGlobalPositionSum += pos.getNetPosition();
            }
        }

        // Update aggregated values
        aggregatedGlobalUnrealizedPnL = currentGlobalUnrealizedSum;

        synchronized (PNL_LOCK) { // Synchronize to update globalCurrentPosition consistently
            globalCurrentPosition = currentGlobalPositionSum;
        }
        // globalRealizedPnL is already accumulated atomically via
        // addToGlobalRealizedPnL
        // globalCurrentDailyLoss is also accumulated, not summed from positions here.

        // ═══ NOTIFY ALL UI INSTANCES OF GLOBAL P&L UPDATES ═══
        for (OrderSenderControllerV2 instance : instances) {
            if (instance.ui != null) {
                instance.ui.updateGlobalPnL(currentGlobalUnrealizedSum, globalRealizedPnL,
                        currentGlobalUnrealizedSum + globalRealizedPnL);
                                        // Update global position limit
                instance.ui.updateGlobalPositionLimit(getGlobalPositionLimit());
            }
        }
    }

    public static double getGlobalRealizedPnL() {
        if (globalRealizedPnL != 0.0 && globalPositions.isEmpty()) {
            sendtolog("getGlobalRealizedPnL: Nonzero realized PnL with no positions! globalRealizedPnL="
                    + globalRealizedPnL);
        }
        return globalRealizedPnL;
    }

    public static double getGlobalTotalPnL() {
        return globalRealizedPnL + getGlobalUnrealizedPnL();
    }

    public double getInstanceUnrealizedPnL() {
        if (instancePosition == null || !instancePosition.isActive()) {
            return 0.0;
        }
        double unrealized = instancePosition.unrealizedPnL; // Already uses multiplier

        return unrealized;
    }

    public double getInstanceRealizedPnL() {
        return instanceRealizedPnL;
    }

    public double getInstanceTotalPnL() {
        return instanceRealizedPnL + getInstanceUnrealizedPnL();
    }

    public static String getTrimmedAlias(String alias) {
        if (alias == null) {
            return null;
        }
        int colonIndex = alias.indexOf(':');
        int dotIndex = alias.indexOf('.');
        int endIndex = alias.length();
        if (colonIndex != -1 && colonIndex < endIndex) {
            endIndex = colonIndex;
        }
        if (dotIndex != -1 && dotIndex < endIndex) {
            endIndex = dotIndex;
        }
        return alias.substring(0, endIndex);
    }

    public double getDisplayPrice() {
        return Math.round(currentMarketPrice / pips) * pips;
    }

    public void onPositionUpdate(int newPosition) {
        if (Double.isFinite(newPosition)) {

            int exchangeGlobalPos = newPosition;
            sendtolog("OrderSenderControllerV2: onPositionUpdate: Current Global Position: " + globalCurrentPosition,
                    0f, "Exchange Global Position: " + exchangeGlobalPos);
        }

        // updatePositionDisplays(globalCurrentPosition, globalCurrentPosition);

    }

    public void closeDebugUI() {
        if (maindebugUIInstance != null) {
            maindebugUIInstance.dispose();
            maindebugUIInstance = null;
        }
    }

    // Methods to draw icons
    public void drawLastTrade(double barclose, boolean isBuy) {
        String extraArg = (algo != null) ? algo : "";
        BufferedImage arrow = IconFactory.makeRandomArrow(isBuy, 30, 25, extraArg);
        if (lastTradeIndicator != null) {
            lastTradeIndicator.addIcon(barclose, arrow, 2, 2);
        }
    }

    public void drawLastTrade2(double barclose, boolean isBuy) {
        String extraArg = (algo != null) ? algo : "";
        BufferedImage arrow = IconFactory.makeRandomArrow2(false, 30, 25, extraArg);
        if (lastTradeIndicator != null) {
            lastTradeIndicator.addIcon(barclose, arrow, 2, 2);
        }
    }

    public void drawLastTrade3(double barclose, boolean isBuy) {
        String extraArg = (algo != null) ? algo : "";
        BufferedImage arrow = IconFactory.makeRandomArrow3(false, 30, 25, Color.white, Color.white, extraArg);
        if (lastTradeIndicator != null) {
            lastTradeIndicator.addIcon(barclose, arrow, 2, 2);
        }
    }

    public void playtradealert(boolean condition, String alerttext, String instrumentName) {
        if (condition) {
            byte[] alertSoundShort = SoundSynthHelper.synthesize(instrumentName + alerttext);
            Layer1ApiSoundAlertMessage alertMessage = new Layer1ApiSoundAlertMessage(
                    alertSoundShort, "alert for " + alias, 1, null, (a, s) -> {
                    }, this.getClass(), alias);
            api.sendUserMessage(alertMessage);
        }
    }

    public static boolean isNowRealtime() {
        // In simulation mode, always return true for logic that checks this
        return NowRealtime || simulationMode;
    }

    // Accessor for alias
    public String getAlias() {
        return alias != null ? alias : "Main";
    }

    // Order update handler
    private void handleOrderUpdate(OrderUpdateParameters params) {
        // Cache toString() to avoid multiple calls
        String paramsStr = params.toString();
        String orderId = extractFieldValue(paramsStr, "orderId=");
        String statusStr = extractFieldValue(paramsStr, "status=");

        if (orderId == null || statusStr == null) {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Order Update", 0f, "Failed to parse order update: " + paramsStr);
            }
            return;
        }

        OrderStatus status = parseOrderStatus(statusStr);

        if (debug_mode_ordersendercontroller) {
            sendtolog("Order Update", 0f, "Order " + orderId + " status: " + status);
        }

        OrderInfo orderInfo = activeOrders.get(orderId);
        if (orderInfo != null) {
            // Add event before changing status
            orderInfo.addEvent("EXCHANGE_UPDATE", "Received status update: " + statusStr);

            // Update status (this will also log the status change event)
            orderInfo.setStatus(status);

            // Handle different order statuses
            if (status == OrderStatus.REJECTED) {
                orderInfo.addEvent("REJECTED", "Order was rejected by the exchange");
                moveToHistory(orderId);
            } else if (status == OrderStatus.CANCELED) {
                orderInfo.addEvent("CANCELED", "Order was canceled");
                moveToHistory(orderId);
            } else if (status == OrderStatus.FILLED) {
                orderInfo.addEvent("FILLED", "Order was completely filled");
                moveToHistory(orderId);
            }
        }
    }

    // ═══ STRING PROCESSING OPTIMIZATIONS ═══
    // Efficient field extraction helper method - replaces multiple split()
    // operations
    // with single-pass parsing to reduce object allocation and improve performance
    private String extractFieldValue(String source, String fieldName) {
        int startIndex = source.indexOf(fieldName);
        if (startIndex == -1) {
            return null;
        }
        startIndex += fieldName.length();

        int endIndex = source.indexOf(',', startIndex);
        if (endIndex == -1) {
            endIndex = source.length();
        }

        return source.substring(startIndex, endIndex).trim();
    }

    // Parse order status from string
    private OrderStatus parseOrderStatus(String statusStr) {
        switch (statusStr.toUpperCase()) {
            case "PENDING_NEW":
                return OrderStatus.PENDING_NEW;
            case "NEW":
                return OrderStatus.NEW;
            case "WORKING":
                return OrderStatus.WORKING;
            case "PARTIALLY_FILLED":
                return OrderStatus.PARTIALLY_FILLED;
            case "FILLED":
                return OrderStatus.FILLED;
            case "CANCELED":
                return OrderStatus.CANCELED;
            case "REJECTED":
                return OrderStatus.REJECTED;
            default:
                return OrderStatus.PENDING_NEW;
        }
    }

    // Trade update handler
    private void handleTradeUpdate(TradeInfo tradeInfo) {
        // Cache toString() to avoid multiple calls
        String tradeInfoStr = tradeInfo.toString();
        String orderId = extractFieldValue(tradeInfoStr, "orderId=");

        if (orderId == null) {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Trade Update", 0f, "Failed to parse orderId from: " + tradeInfoStr);
            }
            return;
        }

        OrderInfo orderInfo = activeOrders.get(orderId);

        sendtolog("handleTradeUpdate called for orderId: " + orderId);
        Log.info("TradeUpdate: Processing trade update for orderId: " + orderId);

        if (orderInfo != null) {
            // Extract size and price from cached string
            String sizeStr = extractFieldValue(tradeInfoStr, "size=");
            String priceStr = extractFieldValue(tradeInfoStr, "price=");

            if (sizeStr == null || priceStr == null) {
                if (debug_mode_ordersendercontroller) {
                    sendtolog("Trade Update", 0f, "Failed to parse size/price from: " + tradeInfoStr);
                }
                return;
            }

            int lastFillSize;
            double lastFillPrice;
            try {
                lastFillSize = Integer.parseInt(sizeStr);
                lastFillPrice = Double.parseDouble(priceStr);
            } catch (NumberFormatException e) {
                if (debug_mode_ordersendercontroller) {
                    sendtolog("Trade Update", 0f, "Failed to parse numbers: size=" + sizeStr + ", price=" + priceStr);
                }
                return;
            }

            int remainingSize = 0; // Assume fully filled for simplicity

            // Log the trade execution
            orderInfo.addEvent("EXECUTION",
                    String.format("Executed %d units at price %.2f", lastFillSize, lastFillPrice));

            sendtolog("Trade executed: " + lastFillSize + " units at " + lastFillPrice + " for orderId: " + orderId);
            Log.info("TradeUpdate: Executed " + lastFillSize + " units at " + lastFillPrice + " for orderId: "
                    + orderId);

            // Update the order status if it's fully filled
            if (remainingSize == 0) {
                orderInfo.setStatus(OrderStatus.FILLED);
                moveToHistory(orderId);

                // Record the completed trade in the performance metrics system
                sendtolog("Calling recordCompletedTrade for fully filled orderId: " + orderId);
                Log.info("TradeUpdate: Calling recordCompletedTrade for fully filled orderId: " + orderId);
                recordCompletedTrade(orderInfo, lastFillSize, lastFillPrice);
            } else {
                orderInfo.setStatus(OrderStatus.PARTIALLY_FILLED);
                orderInfo.addEvent("PARTIAL_FILL",
                        String.format("Remaining size: %d", remainingSize));
                sendtolog("Partial fill for orderId: " + orderId + ", remaining size: " + remainingSize);
                Log.info("TradeUpdate: Partial fill for orderId: " + orderId + ", remaining size: " + remainingSize);
            }
        } else {
            sendtolog("OrderInfo not found for orderId: " + orderId);
            Log.warn("TradeUpdate: OrderInfo not found for orderId: " + orderId);
        }
    }

    /**
     * Records a completed trade in the performance metrics system
     * 
     * @param orderInfo     The order information
     * @param lastFillSize  The size of the last fill
     * @param lastFillPrice The price of the last fill
     */
    private void recordCompletedTrade(OrderInfo orderInfo, int lastFillSize, double lastFillPrice) {
        // Extract the algo name from the client ID
        String clientId = orderInfo.getClientId();
        String algoName = extractAlgoNameFromClientId(clientId);

        // Get entry and exit prices
        double entryPrice = orderInfo.getPrice();
        double exitPrice = lastFillPrice;

        // For market orders, use the fill price as both entry and exit
        if (orderInfo.getType() == OrderType.MARKET) {
            entryPrice = exitPrice;
        }

        // Get trade direction
        boolean isLong = orderInfo.isBuy();

        // Get trade size
        int quantity = orderInfo.getSize();

        // Get timestamps - explicitly use Bookmap time
        long entryTime = orderInfo.getCreationTime();
        long exitTime = currentmilliseconds; // This is Bookmap time from onTimestamp

        // Commission and slippage are now included in Position realized PnL, so do not
        // double count here
        // Just log the trade for dashboard, no deduction here
        String debugMessage = String.format(
                "Recorded trade for algo: %s, EntryPrice: %.2f, ExitPrice: %.2f, Quantity: %d, IsLong: %b, EntryTime: %d, ExitTime: %d",
                algoName, entryPrice, exitPrice, quantity, isLong, entryTime, exitTime);
        sendtolog(debugMessage);
        Log.info("TradeRecording: " + debugMessage);

        // Force dashboard update after recording a trade
        if (performanceDashboardUI != null) {
            performanceDashboardUI.updateDashboard();
            performanceDashboardUI.show();
            sendtolog("Dashboard updated and shown after trade recording");
            Log.info("PerformanceDashboard: Forced update and show after trade recording");
        } else {
            sendtolog("Dashboard is null, cannot update");
            Log.warn("PerformanceDashboard: Dashboard instance is null during trade recording");
        }
    }

    /**
     * Extracts the algorithm name from a client ID (optimized version)
     * 
     * @param clientId The client ID
     * @return The algorithm name
     */
    private String extractAlgoNameFromClientId(String clientId) {
        if (clientId == null || clientId.isEmpty()) {
            return "Unknown";
        }

        // Extract the algo name (everything before the timestamp)
        // Assuming format: algoNameYYMMDDHHMMSS...
        // Optimized: scan for 12 consecutive digits more efficiently
        final int minLength = clientId.length();
        for (int i = 0; i <= minLength - 12; i++) {
            if (Character.isDigit(clientId.charAt(i))) {
                // Quick check: if we found 12 consecutive digits
                boolean foundTimestamp = true;
                for (int j = i + 1; j < i + 12; j++) {
                    if (!Character.isDigit(clientId.charAt(j))) {
                        foundTimestamp = false;
                        break;
                    }
                }
                if (foundTimestamp && i > 0) {
                    return clientId.substring(0, i);
                }
            }
        }

        // Fallback: return first part if no timestamp pattern found
        return minLength > 10 ? clientId.substring(0, 10) : clientId;
    }

    /**
     * Estimates the total cost (commission + slippage) for a trade
     * 
     * @param quantity             The quantity traded
     * @param price                The price at which the trade was executed (not
     *                             used for slippage)
     * @param instrumentMultiplier The contract multiplier for the instrument
     * @param pips                 The pip value for the instrument
     * @return The estimated total cost (commission + slippage)
     */
    public static double estimateCommissionAndSlippage(int quantity, double price, double instrumentMultiplier,
            double pips) {
        // Commission
        double perContractFee = 0.85; // Example: $0.85 per contract
        double minCommission = 1.0; // Example: $1.00 minimum commission
        double commission = quantity * perContractFee;
        commission = Math.max(commission, minCommission);
        // Slippage: 0.5 pips per contract, converted to price by instrumentMultiplier
        double slippagePerContract = 1.1 * pips * instrumentMultiplier;
        double totalSlippage = slippagePerContract * quantity;
        return commission + totalSlippage;
    }

    // Move order from active to history
    private void moveToHistory(String orderId) {
        OrderInfo orderInfo = activeOrders.remove(orderId);
        if (orderInfo != null) {
            orderHistory.add(orderInfo);

            // If linked order exists, update it
            if (orderInfo.isOco() && orderInfo.getLinkedOrderId() != null) {
                OrderInfo linkedOrder = activeOrders.get(orderInfo.getLinkedOrderId());
                if (linkedOrder != null) {
                    moveToHistory(linkedOrder.getOrderId());
                }
            }

            // Log the move to history
            if (debug_mode_ordersendercontroller) {
                sendtolog("Order History", 0f,
                        "Order " + orderId + " moved to history with status: " + orderInfo.getStatus());
            }

            // Trim history if needed
            while (orderHistory.size() > MAX_ORDER_HISTORY) {
                orderHistory.remove(0);
            }
        }
    }

    // Register order in tracking system
    private void registerOrder(String orderId, String clientId, boolean isBuy, OrderType type,
            int size, double price, String notes, boolean isOco, String orderReason) { // Added orderReason
        // Validate and sanitize price value
        double validatedPrice = price;
        if (Double.isNaN(price) || price <= 0) {
            // Only apply price validation for non-market orders
            if (type == OrderType.LIMIT || type == OrderType.STOP ||
                    type == OrderType.STOP_LIMIT || type == OrderType.TRAIL_STOP) {
                // For limit/stop orders, log warning and use currentMarketPrice as fallback
                sendtolog("Order Registration", 0f, "Warning: " + type + " order registered with invalid price: " +
                        price + ", using current market price as fallback.");
                validatedPrice = currentMarketPrice > 0 ? currentMarketPrice : 0.0;
            } else {
                // For market orders, use the original price (can be NaN or zero)
                validatedPrice = price;
            }
        }

        // Create and register the order
        OrderInfo orderInfo = new OrderInfo(orderId, clientId, isBuy, type, size, validatedPrice, notes, isOco,
                orderReason); // Pass orderReason

        // If this is a numeric order ID, map it to the instance
        if (orderId.matches("\\d+")) {
            synchronized (orderIdToInstanceMap) {
                orderIdToInstanceMap.put(orderId, instanceId);
            }
        }

        // Store metadata about order side for handling executions
        orderSideMap.put(orderId, isBuy);

        // Map client ID to this instance for future reference
        if (clientId != null && !clientId.isEmpty()) {
            clientIdToInstanceId.put(clientId, instanceId);
        }

        // Register the order in our tracking system
        activeOrders.put(orderId, orderInfo);
        orderClientIdToOrderId.put(clientId, orderId);

        // Update the display with the new order

        sendtolog("Order Registration", 0f, "Registered " + type + " order: " + orderId +
                " (" + (isBuy ? "BUY" : "SELL") + " " + size + " @ " + validatedPrice + ")");
    }

    // Get active orders
    public List<OrderInfo> getActiveOrders() {
        return new ArrayList<>(activeOrders.values());
    }

    // Get order history
    public List<OrderInfo> getOrderHistory() {
        return new ArrayList<>(orderHistory);
    }

    // Get active orders by category
    public List<OrderInfo> getActiveOrdersByCategory(OrderCategory category) {
        return filterOrders(new ArrayList<>(activeOrders.values()), category);
    }

    // Get order history by category
    public List<OrderInfo> getOrderHistoryByCategory(OrderCategory category) {
        return filterOrders(new ArrayList<>(orderHistory), category);
    }

    // Filter orders by category
    private List<OrderInfo> filterOrders(List<OrderInfo> orders, OrderCategory category) {
        Predicate<OrderInfo> filter;

        switch (category) {
            case MARKET:
                filter = order -> order.getType() == OrderType.MARKET;
                break;
            case LIMIT:
                filter = order -> order.getType() == OrderType.LIMIT;
                break;
            case STOP:
                filter = order -> order.getType() == OrderType.STOP || order.getType() == OrderType.STOP_LIMIT;
                break;
            case OCO:
                filter = OrderInfo::isOco;
                break;
            case BUY:
                filter = OrderInfo::isBuy;
                break;
            case SELL:
                filter = order -> !order.isBuy();
                break;
            case ALL:
            default:
                filter = order -> true;
                break;
        }

        return orders.stream()
                .filter(filter)
                .collect(Collectors.toList());
    }

    /**
     * Filter orders by algo name
     * 
     * @param orders   List of orders to filter
     * @param algoName The algo name to filter by
     * @return Filtered list of orders belonging to the specified algo
     */
    private List<OrderInfo> filterOrdersByAlgo(List<OrderInfo> orders, String algoName) {
        if (algoName == null || algoName.isEmpty()) {
            return new ArrayList<>();
        }

        return orders.stream()
                .filter(order -> {
                    String clientId = order.getClientId();
                    // Check if the clientId starts with the algo name
                    return clientId != null && clientId.startsWith(algoName);
                })
                .collect(Collectors.toList());
    }

    /**
     * Get all active orders for a specific algo
     * 
     * @param algoName The algo name to filter by
     * @return List of active orders for the specified algo
     */
    public List<OrderInfo> getActiveOrdersByAlgo(String algoName) {
        if (debug_mode_ordersendercontroller) {
            sendtolog("Get Active Orders", 0f, "Retrieving active orders for algo: " + algoName);
        }
        return filterOrdersByAlgo(new ArrayList<>(activeOrders.values()), algoName);
    }

    /**
     * Get active orders for a specific algo filtered by category
     * 
     * @param algoName The algo name to filter by
     * @param category The order category to filter by
     * @return List of active orders for the specified algo and category
     */
    public List<OrderInfo> getActiveOrdersByAlgoAndCategory(String algoName, OrderCategory category) {
        if (debug_mode_ordersendercontroller) {
            sendtolog("Get Active Orders", 0f, "Retrieving " + category + " orders for algo: " + algoName);
        }
        List<OrderInfo> algoOrders = getActiveOrdersByAlgo(algoName);
        return filterOrders(algoOrders, category);
    }

    /**
     * Get order history for a specific algo
     * 
     * @param algoName The algo name to filter by
     * @return List of historical orders for the specified algo
     */
    public List<OrderInfo> getOrderHistoryByAlgo(String algoName) {
        if (debug_mode_ordersendercontroller) {
            sendtolog("Get Order History", 0f, "Retrieving order history for algo: " + algoName);
        }
        return filterOrdersByAlgo(new ArrayList<>(orderHistory), algoName);
    }

    /**
     * Get order history for a specific algo filtered by category
     * 
     * @param algoName The algo name to filter by
     * @param category The order category to filter by
     * @return List of historical orders for the specified algo and category
     */
    public List<OrderInfo> getOrderHistoryByAlgoAndCategory(String algoName, OrderCategory category) {
        if (debug_mode_ordersendercontroller) {
            sendtolog("Get Order History", 0f, "Retrieving " + category + " order history for algo: " + algoName);
        }
        List<OrderInfo> algoOrders = getOrderHistoryByAlgo(algoName);
        return filterOrders(algoOrders, category);
    }

    /**
     * Cancel all active orders for a specific algo
     * 
     * @param algoName The algo name to filter by
     * @return Number of orders cancelled
     */
    public int cancelAllOrdersByAlgo(String algoName) {
        List<OrderInfo> ordersToCancel = getActiveOrdersByAlgo(algoName);
        int cancelCount = 0;

        if (debug_mode_ordersendercontroller) {
            sendtolog("Cancel Orders", 0f,
                    "Cancelling all orders for algo: " + algoName + " (count: " + ordersToCancel.size() + ")");
        }

        for (OrderInfo orderInfo : ordersToCancel) {
            if (cancelOrder(orderInfo.getOrderId())) {
                cancelCount++;
            }
        }

        if (debug_mode_ordersendercontroller) {
            sendtolog("Cancel Orders", 0f, "Cancelled " + cancelCount + " orders for algo: " + algoName);
        }
        return cancelCount;
    }

    /**
     * Cancel active orders for a specific algo filtered by category
     * 
     * @param algoName The algo name to filter by
     * @param category The order category to filter by
     * @return Number of orders cancelled
     */
    public int cancelOrdersByAlgoAndCategory(String algoName, OrderCategory category) {
        List<OrderInfo> ordersToCancel = getActiveOrdersByAlgoAndCategory(algoName, category);
        int cancelCount = 0;

        if (debug_mode_ordersendercontroller) {
            sendtolog("Cancel Orders", 0f, "Cancelling " + category + " orders for algo: " + algoName + " (count: "
                    + ordersToCancel.size() + ")");
        }

        for (OrderInfo orderInfo : ordersToCancel) {
            if (cancelOrder(orderInfo.getOrderId())) {
                cancelCount++;
            }
        }

        if (debug_mode_ordersendercontroller) {
            sendtolog("Cancel Orders", 0f,
                    "Cancelled " + cancelCount + " " + category + " orders for algo: " + algoName);
        }
        return cancelCount;
    }

    /**
     * Get a summary report of active orders for a specific algo
     * 
     * @param algoName The algo name to filter by
     * @return String containing a summary of active orders
     */
    public String getActiveOrdersSummaryByAlgo(String algoName) {
        List<OrderInfo> orders = getActiveOrdersByAlgo(algoName);
        StringBuilder report = new StringBuilder();
        report.append("=== ACTIVE ORDERS FOR ALGO: ").append(algoName).append(" ===\n");

        if (orders.isEmpty()) {
            report.append("No active orders\n");
        } else {
            // Count by type
            Map<OrderType, Integer> typeCount = new HashMap<>();
            Map<Boolean, Integer> directionCount = new HashMap<>();

            for (OrderInfo order : orders) {
                typeCount.put(order.getType(), typeCount.getOrDefault(order.getType(), 0) + 1);
                directionCount.put(order.isBuy(), directionCount.getOrDefault(order.isBuy(), 0) + 1);
            }

            report.append("Total orders: ").append(orders.size()).append("\n");
            report.append("By type: ");
            for (Map.Entry<OrderType, Integer> entry : typeCount.entrySet()) {
                report.append(entry.getKey()).append(": ").append(entry.getValue()).append(", ");
            }
            report.append("\n");

            report.append("By direction: ");
            report.append("BUY: ").append(directionCount.getOrDefault(true, 0)).append(", ");
            report.append("SELL: ").append(directionCount.getOrDefault(false, 0)).append("\n\n");

            // List all orders
            for (OrderInfo order : orders) {
                report.append(order.toString()).append("\n");
            }
        }

        return report.toString();
    }

    // Cancel a specific order
    public boolean cancelOrder(String orderId) {
        OrderInfo orderInfo = activeOrders.get(orderId);
        if (orderInfo == null) {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Cancel Order", 0f, "Order " + orderId + " not found");
            }
            return false;
        }

        try {
            OrderCancelParameters cancelParams = new OrderCancelParameters(orderId);
            api.updateOrder(cancelParams);

            if (debug_mode_ordersendercontroller) {
                sendtolog("Cancel Order", 0f, "Cancellation request sent for order " + orderId);
            }
            return true;
        } catch (Exception e) {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Cancel Order Error", 0f, "Failed to cancel order " + orderId + ": " + e.getMessage());
            }
            return false;
        }
    }

    // Cancel all orders
    public int cancelAllOrders() {
        int cancelCount = 0;
        for (String orderId : new ArrayList<>(activeOrders.keySet())) {
            if (cancelOrder(orderId)) {
                cancelCount++;
            }
        }

        if (debug_mode_ordersendercontroller) {
            sendtolog("Cancel All Orders", 0f, "Cancelled " + cancelCount + " orders");
        }
        return cancelCount;
    }

    // Cancel orders by category
    public int cancelOrdersByCategory(OrderCategory category) {
        List<OrderInfo> ordersToCancel = getActiveOrdersByCategory(category);
        int cancelCount = 0;

        for (OrderInfo orderInfo : ordersToCancel) {
            if (cancelOrder(orderInfo.getOrderId())) {
                cancelCount++;
            }
        }

        if (debug_mode_ordersendercontroller) {
            sendtolog("Cancel Orders by Category", 0f, "Cancelled " + cancelCount + " " + category + " orders");
        }
        return cancelCount;
    }

    // Modify order price
    public boolean modifyOrderPrice(String orderId, double newPrice) {
        OrderInfo orderInfo = activeOrders.get(orderId);
        if (orderInfo == null) {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Modify Order Price", 0f, "Order " + orderId + " not found");
            }
            return false;
        }

        try {
            // Determine which price to modify based on order type
            double stopPrice = Double.NaN;
            double limitPrice = Double.NaN;

            switch (orderInfo.getType()) {
                case LIMIT:
                    limitPrice = newPrice;
                    break;
                case STOP:
                    stopPrice = newPrice;
                    break;
                case STOP_LIMIT:
                    // For stop-limit orders, we need to know which price to modify
                    // This is a simplification - in a real implementation, you might want to
                    // specify which price (stop or limit) to modify
                    limitPrice = newPrice;
                    break;
                default:
                    if (debug_mode_ordersendercontroller) {
                        sendtolog("Modify Order Price", 0f,
                                "Cannot modify price for order type " + orderInfo.getType());
                    }
                    return false;
            }

            OrderMoveParameters moveParams = new OrderMoveParameters(orderId, stopPrice, limitPrice);
            api.updateOrder(moveParams);

            // Update local order info
            orderInfo.setPrice(newPrice);

            if (debug_mode_ordersendercontroller) {
                sendtolog("Modify Order Price", 0f,
                        "Price modification request sent for order " + orderId + ", new price: " + newPrice);
            }
            return true;
        } catch (Exception e) {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Modify Order Price Error", 0f,
                        "Failed to modify price for order " + orderId + ": " + e.getMessage());
            }
            return false;
        }
    }

    // Modify order size
    public boolean modifyOrderSize(String orderId, int newSize) {
        OrderInfo orderInfo = activeOrders.get(orderId);
        if (orderInfo == null) {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Modify Order Size", 0f, "Order " + orderId + " not found");
            }
            return false;
        }

        try {
            OrderResizeParameters resizeParams = new OrderResizeParameters(orderId, newSize);
            api.updateOrder(resizeParams);

            if (debug_mode_ordersendercontroller) {
                sendtolog("Modify Order Size", 0f,
                        "Size modification request sent for order " + orderId + ", new size: " + newSize);
            }
            return true;
        } catch (Exception e) {
            if (debug_mode_ordersendercontroller) {
                sendtolog("Modify Order Size Error", 0f,
                        "Failed to modify size for order " + orderId + ": " + e.getMessage());
            }
            return false;
        }
    }

    // Get order by ID
    public OrderInfo getOrderById(String orderId) {
        OrderInfo orderInfo = activeOrders.get(orderId);
        if (orderInfo == null) {
            // Check in history
            for (OrderInfo historyOrder : orderHistory) {
                if (historyOrder.getOrderId().equals(orderId)) {
                    return historyOrder;
                }
            }
        }
        return orderInfo;
    }

    // Get order by client ID
    public OrderInfo getOrderByClientId(String clientId) {
        // Check active orders
        for (OrderInfo orderInfo : activeOrders.values()) {
            if (orderInfo.getClientId().equals(clientId)) {
                return orderInfo;
            }
        }

        // Check history
        for (OrderInfo orderInfo : orderHistory) {
            if (orderInfo.getClientId().equals(clientId)) {
                return orderInfo;
            }
        }

        return null;
    }

    // Get order status report
    public String getOrderStatusReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== ACTIVE ORDERS ===\n");

        if (activeOrders.isEmpty()) {
            report.append("No active orders\n");
        } else {
            for (OrderInfo orderInfo : activeOrders.values()) {
                report.append(orderInfo.toString()).append("\n");
            }
        }

        report.append("\n=== RECENT ORDER HISTORY ===\n");

        if (orderHistory.isEmpty()) {
            report.append("No order history\n");
        } else {
            // Show last 10 orders from history
            int startIndex = Math.max(0, orderHistory.size() - 10);
            for (int i = startIndex; i < orderHistory.size(); i++) {
                report.append(orderHistory.get(i).toString()).append("\n");
            }
        }

        return report.toString();
    }

    private static PerformanceDashboardUI performanceDashboardUI;

    /**
     * Shows the performance dashboard UI
     */
    public void showPerformanceDashboard() {
        // Ensure this runs on the EDT
        if (!SwingUtilities.isEventDispatchThread()) {
            SwingUtilities.invokeLater(this::showPerformanceDashboard);
            return;
        }
        // A-5: Guard against headless mode
        if (GraphicsEnvironment.isHeadless()) {
            Log.info("OrderSenderControllerV2: Headless mode detected, skipping Performance Dashboard UI creation.");
            return;
        }
        synchronized (OrderSenderControllerV2.class) {
            if (performanceDashboardUI == null) {
                performanceDashboardUI = new PerformanceDashboardUI();
            }
            performanceDashboardUI.updateAlgorithmList();
            performanceDashboardUI.show();
        }
    }

    /**
     * Closes the performance dashboard UI
     */
    public void closePerformanceDashboard() {
        // Ensure this runs on the EDT
        if (!SwingUtilities.isEventDispatchThread()) {
            SwingUtilities.invokeLater(this::closePerformanceDashboard);
            return;
        }

        synchronized (OrderSenderControllerV2.class) {
            if (performanceDashboardUI != null) {
                performanceDashboardUI.cleanup();
                performanceDashboardUI = null;
            }
        }
    }

    /**
     * Track an order by its order ID
     * 
     * @param orderId The order ID to track
     * @return The OrderInfo object if found, null otherwise
     */
    public OrderInfo trackOrder(String orderId) {
        // First check active orders
        OrderInfo order = activeOrders.get(orderId);
        if (order != null) {
            return order;
        }

        // Then check order history
        for (OrderInfo historyOrder : orderHistory) {
            if (historyOrder.getOrderId().equals(orderId)) {
                return historyOrder;
            }
        }

        return null; // Order not found
    }

    /**
     * Track an order by its client ID
     * 
     * @param clientId The client ID to track
     * @return The OrderInfo object if found, null otherwise
     */
    public OrderInfo trackOrderByClientId(String clientId) {
        // Check active orders
        for (OrderInfo order : activeOrders.values()) {
            if (order.getClientId().equals(clientId)) {
                return order;
            }
        }

        // Check order history
        for (OrderInfo historyOrder : orderHistory) {
            if (historyOrder.getClientId().equals(clientId)) {
                return historyOrder;
            }
        }

        return null; // Order not found
    }

    /**
     * Get a detailed lifecycle report for an order
     * 
     * @param orderId The order ID to get the report for
     * @return A string containing the detailed lifecycle report
     */
    public String getOrderLifecycleReport(String orderId) {
        OrderInfo order = trackOrder(orderId);
        if (order != null) {
            return order.getLifecycleReport();
        } else {
            return "Order not found: " + orderId;
        }
    }

    /**
     * Get a detailed lifecycle report for an order by client ID
     * 
     * @param clientId The client ID to get the report for
     * @return A string containing the detailed lifecycle report
     */
    public String getOrderLifecycleReportByClientId(String clientId) {
        OrderInfo order = trackOrderByClientId(clientId);
        if (order != null) {
            return order.getLifecycleReport();
        } else {
            return "Order not found with client ID: " + clientId;
        }
    }

    // Implementation of PositionListener interface
    public void onPositionUpdate(StatusInfo statusInfo) {
        if (debug_mode_ordersendercontroller_listner) {
            sendtolog("Position Update", 0f, "Position: " + statusInfo.position +
                    ", Working Buys: " + statusInfo.workingBuys +
                    ", Working Sells: " + statusInfo.workingSells);
        }
        // updateMarketPrice(currentMarketPrice);

    }

    // Implementation of OrdersListener interface
    public void onOrderUpdated(OrderInfoUpdate orderInfoUpdate) {
        if (orderInfoUpdate.clientId != null && !orderInfoUpdate.clientId.startsWith(algo)
                || orderInfoUpdate.filled < 1) { // Initial algo and basic fill check
            return;
        }

        boolean runenabled = true;
        if (runenabled) {
            OrderStatus currentStatus = parseOrderStatus(orderInfoUpdate.status.toString());
            OrderInfo orderInfo = activeOrders.get(orderInfoUpdate.orderId); // Try to get early

            boolean isFullyFilled = orderInfoUpdate.filled > 0 && orderInfoUpdate.unfilled == 0
                    && currentStatus == OrderStatus.FILLED;

            if (isFullyFilled) {
                double orderprice = currentMarketPrice; // Default
                OrderType orderType1 = determineOrderType(orderInfoUpdate);
                if (orderType1 == OrderType.LIMIT) {
                    orderprice = Double.isFinite(orderInfoUpdate.limitPrice) ? orderInfoUpdate.limitPrice
                            : currentMarketPrice;
                }
                if (orderType1 == OrderType.STOP) {
                    orderprice = Double.isFinite(orderInfoUpdate.stopPrice) ? orderInfoUpdate.stopPrice
                            : currentMarketPrice;
                }
                if (orderType1 == OrderType.STOP_LIMIT) {
                    orderprice = Double.isFinite(orderInfoUpdate.limitPrice) ? orderInfoUpdate.limitPrice
                            : currentMarketPrice;
                    // double
                    // orderstopprice=Double.isFinite(orderInfoUpdate.stopPrice)?orderInfoUpdate.stopPrice:currentMarketPrice;
                    // // Not directly used for orderprice
                }
                if (orderType1 == OrderType.MARKET) {
                    orderprice = currentMarketPrice;
                }

                boolean orderside = orderInfoUpdate.isBuy;
                int orderfilled = orderInfoUpdate.filled;

                // Price validation
                if (orderprice < 0.95 * currentMarketPrice || orderprice > 1.05 * currentMarketPrice) {
                    sendtolog("Setting Order Price to Market Price on FILL", 0f, "Order price " + orderprice
                            + " vs market " + currentMarketPrice + " for OrderID " + orderInfoUpdate.orderId);
                    OrderSenderControllerDebugUI.appendToLog(
                            "[OrderPriceDerivationException] Order price out of bounds on FILL. OrderInfoUpdate: " +
                                    "orderId=" + orderInfoUpdate.orderId +
                                    ", clientId=" + orderInfoUpdate.clientId +
                                    ", limitPrice=" + orderInfoUpdate.limitPrice +
                                    ", stopPrice=" + orderInfoUpdate.stopPrice +
                                    ", status=" + orderInfoUpdate.status +
                                    ", filled=" + orderInfoUpdate.filled +
                                    ", unfilled=" + orderInfoUpdate.unfilled +
                                    ", isBuy=" + orderInfoUpdate.isBuy);
                    orderprice = currentMarketPrice;
                }

                sendtoOrderStatusLog(" Algo-- " + algo + " OrderID " + orderInfoUpdate.orderId + " Processed as FILLED",
                        0f, "Filled: " + orderfilled + " Price: " + orderprice);

                updatePositionForExecution(
                        orderside,
                        orderfilled,
                        orderprice,
                        null // entryReason is null for this path
                );

                // Check if we're in simulation mode (not real trading) - check directly from RunModeHelper
        Boolean isRealTrading = RunModeHelper.isRealTrading();
        boolean isSimulation = !Boolean.TRUE.equals(isRealTrading);
        
        if (debug_mode_ordersendercontroller_listner) {
            MaindebugUI.log("onOrderUpdated called - isRealTrading: " + isRealTrading + ", isSimulation: " + isSimulation);
        }

        if (isSimulation) {
            // Override balance data for simulation mode
            if (StartingAccountBalance == 0.0) {
                StartingAccountBalance = 5000.0; // Set simulation starting balance to 5000
            }
            LastAccountBalance = StartingAccountBalance; // Use the simulation starting balance

            // Calculate available liquidity as starting balance + total global PnL
            double totalGlobalPnL = getGlobalTotalPnL();
            LastLiquidityValue = StartingAccountBalance + totalGlobalPnL;
        
            if (debug_mode_ordersendercontroller_listner) {
                MaindebugUI.log("Simulation Mode Balance Override in onOrderUpdated" + 0f +
                        "Starting: " + StartingAccountBalance +
                        ", Current: " + LastAccountBalance +
                        ", Global PnL: " + totalGlobalPnL +
                        ", Liquidity: " + LastLiquidityValue);
            }
        } 

                if (orderInfo == null) { // Order not known, register it
                    sendtolog("Order Update", 0f, "[NEW ORDER ON FILL] Registering orderId: " + orderInfoUpdate.orderId
                            + " for algo: " + algo);
                    registerOrder(
                            orderInfoUpdate.orderId,
                            orderInfoUpdate.clientId,
                            orderside,
                            orderType1, // Determined above
                            orderfilled, // Total size for a new, already filled order
                            orderprice, // Use the execution price for registration if new
                            "Order registered on fill detection",
                            false, // isOco - cannot determine reliably
                            null // orderReason - not available
                    );
                    orderInfo = activeOrders.get(orderInfoUpdate.orderId); // Retrieve after registration
                }

                if (orderInfo != null) {
                    orderInfo.setStatus(OrderStatus.FILLED);
                    orderInfo.addEvent("FILLED",
                            "Executed at " + String.format("%.2f", orderprice) + ", size " + orderfilled);

                    // Notify UI of order update
                    if (ui != null) {
                        ui.logOrderUpdate(orderInfoUpdate.orderId, orderInfoUpdate.clientId,
                                "FILLED", orderside, orderprice);
                    }

                    moveToHistory(orderInfoUpdate.orderId);
                } else {
                    sendtoOrderStatusLog("Order Update ERROR", 0f,
                            "Failed to get/register OrderInfo for FILLED order: " + orderInfoUpdate.orderId);
                }

            } else if (currentStatus == OrderStatus.CANCELED || currentStatus == OrderStatus.REJECTED) {
                sendtoOrderStatusLog(
                        " Algo-- " + algo + " OrderID " + orderInfoUpdate.orderId + " Processed as " + currentStatus,
                        0f, "Filled: " + orderInfoUpdate.filled + " Unfilled: " + orderInfoUpdate.unfilled);
                if (orderInfo == null && orderInfoUpdate.clientId != null
                        && orderInfoUpdate.clientId.startsWith(algo)) {
                    sendtolog("Order Update", 0f, "[TERMINAL ORDER] Registering orderId: " + orderInfoUpdate.orderId
                            + " Status: " + currentStatus);
                    registerOrder(
                            orderInfoUpdate.orderId,
                            orderInfoUpdate.clientId,
                            orderInfoUpdate.isBuy,
                            determineOrderType(orderInfoUpdate),
                            orderInfoUpdate.filled, // Can be 0
                            (orderInfoUpdate.limitPrice > 0 && Double.isFinite(orderInfoUpdate.limitPrice))
                                    ? orderInfoUpdate.limitPrice
                                    : ((orderInfoUpdate.stopPrice > 0 && Double.isFinite(orderInfoUpdate.stopPrice))
                                            ? orderInfoUpdate.stopPrice
                                            : 0.0), // Original requested price
                            "Order registered on " + currentStatus + " detection",
                            false, // isOco
                            null // orderReason
                    );
                    orderInfo = activeOrders.get(orderInfoUpdate.orderId);
                }

                if (orderInfo != null) {
                    orderInfo.setStatus(currentStatus);
                    orderInfo.addEvent(currentStatus.toString(), "Order terminal state received");

                    // Notify UI of order update
                    if (ui != null) {
                        double price = (orderInfoUpdate.limitPrice > 0 && Double.isFinite(orderInfoUpdate.limitPrice))
                                ? orderInfoUpdate.limitPrice
                                : ((orderInfoUpdate.stopPrice > 0 && Double.isFinite(orderInfoUpdate.stopPrice))
                                        ? orderInfoUpdate.stopPrice
                                        : currentMarketPrice);
                        ui.logOrderUpdate(orderInfoUpdate.orderId, orderInfoUpdate.clientId,
                                currentStatus.toString(), orderInfoUpdate.isBuy, price);
                    }

                    moveToHistory(orderInfoUpdate.orderId);
                } else {
                    sendtoOrderStatusLog("Order Update ERROR", 0f, "Failed to get/register OrderInfo for "
                            + currentStatus + " order: " + orderInfoUpdate.orderId);
                }
            } else if (orderInfo != null) { // Existing order, non-FILLED, non-CANCELED, non-REJECTED update (e.g.
                                            // WORKING, PARTIALLY_FILLED)
                OrderStatus oldStatus = orderInfo.getStatus();
                if (currentStatus != oldStatus) {
                    orderInfo.setStatus(currentStatus);
                    String eventDetails = "Status updated to " + currentStatus;
                    if ((currentStatus == OrderStatus.WORKING || currentStatus == OrderStatus.PARTIALLY_FILLED) &&
                            orderInfoUpdate.limitPrice > 0 && Double.isFinite(orderInfoUpdate.limitPrice) &&
                            orderInfo.getPrice() != orderInfoUpdate.limitPrice) {
                        orderInfo.setPrice(orderInfoUpdate.limitPrice); // Update price if it's a modification
                        eventDetails += ", price set to " + String.format("%.2f", orderInfoUpdate.limitPrice);
                    }
                    orderInfo.addEvent("UPDATE", eventDetails);
                    sendtoOrderStatusLog(" Algo-- " + algo + " OrderID " + orderInfoUpdate.orderId
                            + " status updated to " + currentStatus, 0f, eventDetails);
                }
            }
            // else: Order not in activeOrders and not a terminal state we're registering on
            // the fly.
            // Or, clientId didn't match 'algo' (already filtered at the start).

            return; // This is the existing early return, ending the "fast path"
        }

        // The rest of this method (from old line 3663 onwards) is currently bypassed
        // due to the 'return' above, as per user's confirmation of the fast-path
        // design.
        // This section contains logic for globalProcessedOrderIds,
        // orderIdToInstanceMap, etc.,
        // which the user indicated are to be simplified/removed.

        if (debug_mode_ordersendercontroller_listner) {
            sendtolog("Order Update", 0f, "Order " + orderInfoUpdate.orderId + " status: " + orderInfoUpdate.status);
        }

        // IMPROVED: Check if this is a numeric order ID from the exchange that matches
        // a market order we sent
        if (orderInfoUpdate.orderId.matches("\\d+")) {
            if (debug_mode_ordersendercontroller_listner) {
                sendtolog("Order Update", 0f,
                        "Ignoring exchange update for numeric market order ID: " + orderInfoUpdate.orderId);
            }
            return;
        }

        // Determine order type
        OrderType orderType = determineOrderType(orderInfoUpdate);

        // Improve handling of NaN/zero prices for different order types
        if (orderType == OrderType.LIMIT
                && (Double.isNaN(orderInfoUpdate.limitPrice) || orderInfoUpdate.limitPrice <= 0)) {
            sendtolog("Order Update", 0f, "Warning: Limit order with invalid limit price detected for order: " +
                    orderInfoUpdate.orderId + ", price: " + orderInfoUpdate.limitPrice);
            // We don't return here, as we still want to process the update, but log the
            // warning
        }

        if (orderType == OrderType.STOP
                && (Double.isNaN(orderInfoUpdate.stopPrice) || orderInfoUpdate.stopPrice <= 0)) {
            sendtolog("Order Update", 0f, "Warning: Stop order with invalid stop price detected for order: " +
                    orderInfoUpdate.orderId + ", price: " + orderInfoUpdate.stopPrice);
            // We don't return here, as we still want to process the update, but log the
            // warning
        }

        if (orderType == OrderType.STOP_LIMIT) {
            boolean invalidLimit = Double.isNaN(orderInfoUpdate.limitPrice) || orderInfoUpdate.limitPrice <= 0;
            boolean invalidStop = Double.isNaN(orderInfoUpdate.stopPrice) || orderInfoUpdate.stopPrice <= 0;

            if (invalidLimit || invalidStop) {
                sendtolog("Order Update", 0f, "Warning: Stop-Limit order with invalid prices detected for order: " +
                        orderInfoUpdate.orderId + ", limit: " + orderInfoUpdate.limitPrice +
                        ", stop: " + orderInfoUpdate.stopPrice);
                // We don't return here, as we still want to process the update, but log the
                // warning
            }
        }

        // Skip NaN priced orders for updates only if they are LIMIT or STOP type orders
        // Market orders can legitimately have zero or NaN limit prices
        if (Double.isNaN(orderInfoUpdate.limitPrice) &&
                (orderType == OrderType.LIMIT || orderType == OrderType.STOP_LIMIT)) {
            sendtolog("Order Update", 0f, "Skipping update with NaN limit price for order: " + orderInfoUpdate.orderId);
            return;
        }

        // Skip updates for stop orders with NaN stop prices
        if (Double.isNaN(orderInfoUpdate.stopPrice) &&
                (orderType == OrderType.STOP || orderType == OrderType.STOP_LIMIT
                        || orderType == OrderType.TRAIL_STOP)) {
            sendtolog("Order Update", 0f, "Skipping update with NaN stop price for order: " + orderInfoUpdate.orderId);
            return;
        }

        // Skip if this is an already processed order based on global tracking
        String updateKey = orderInfoUpdate.orderId + "_" + orderInfoUpdate.status;
        if (globalProcessedOrderIds.contains(updateKey)) {
            if (debug_mode_ordersendercontroller_listner) {
                sendtolog("Order Update", 0f, "Skipping already processed update: " + updateKey);
            }
            return;
        }

        // Check if we're the owner of this order before processing
        boolean isOwner = false;
        String owningInstanceId = null;

        synchronized (orderIdToInstanceMap) {
            owningInstanceId = orderIdToInstanceMap.get(orderInfoUpdate.orderId);
            isOwner = instanceId.equals(owningInstanceId);

            // For numeric order IDs that don't have a registered owner yet
            if (!isOwner && orderInfoUpdate.orderId.matches("\\d+")
                    && !orderIdToInstanceMap.containsKey(orderInfoUpdate.orderId)) {
                // Try to find the client ID
                String clientId = orderInfoUpdate.clientId;
                if (clientId != null && !clientId.isEmpty()) {
                    // Register mapping between exchange order ID and client ID
                    exchangeOrderIdToClientId.put(orderInfoUpdate.orderId, clientId);

                    // Claim ownership
                    orderIdToInstanceMap.put(orderInfoUpdate.orderId, instanceId);
                    isOwner = true;
                    owningInstanceId = instanceId;
                    sendtolog("Order Update", 0f, "No instance mapping - instance " + instanceId +
                            " claiming ownership of numeric order ID: " + orderInfoUpdate.orderId);
                } else {
                    // No client ID available - claim ownership
                    orderIdToInstanceMap.put(orderInfoUpdate.orderId, instanceId);
                    isOwner = true;
                    owningInstanceId = instanceId;
                    sendtolog("Order Update", 0f, "Instance " + instanceId +
                            " claiming ownership of numeric order ID: " + orderInfoUpdate.orderId);
                }
            }
        }

        // Skip processing if we're not the owner
        if (!isOwner) {
            // This instance doesn't own this order, skip processing
            return;
        }

        // Add to processed updates to prevent duplicate processing
        globalProcessedOrderIds.add(updateKey);

        // Process the update if we're the owner
        OrderInfo orderInfo = activeOrders.get(orderInfoUpdate.orderId);

        if (orderInfo == null) {
            // This is a new order we haven't seen before
            // Log the first time we see a new exchange order ID
            sendtolog("Order Update", 0f,
                    "[NEW EXCHANGE ORDER ID] First status update for exchange orderId=" + orderInfoUpdate.orderId +
                            ", clientId=" + orderInfoUpdate.clientId +
                            ", status=" + orderInfoUpdate.status +
                            ", filled=" + orderInfoUpdate.filled +
                            ", unfilled=" + orderInfoUpdate.unfilled +
                            ", isBuy=" + orderInfoUpdate.isBuy);
            // Also log mapping if available
            if (orderInfoUpdate.clientId != null && !orderInfoUpdate.clientId.isEmpty()) {
                sendtolog("Order Update", 0f,
                        "[ORDER ID MAPPING] Mapping exchange orderId=" + orderInfoUpdate.orderId +
                                " to clientId=" + orderInfoUpdate.clientId +
                                " for instanceId=" + instanceId);
            }
            // Try to determine the order type from the update
            if (orderType == null) {
                orderType = OrderType.MARKET; // Default to MARKET if we can't determine type
            }

            // Register the new order
            registerOrder(
                    orderInfoUpdate.orderId,
                    orderInfoUpdate.clientId != null ? orderInfoUpdate.clientId : "",
                    orderInfoUpdate.isBuy,
                    orderType,
                    orderInfoUpdate.filled > 0 ? (int) orderInfoUpdate.filled : 1, // Use filled size if available,
                                                                                   // otherwise default to 1
                    !Double.isNaN(orderInfoUpdate.limitPrice) ? orderInfoUpdate.limitPrice : currentMarketPrice, // Use
                                                                                                                 // current
                                                                                                                 // market
                                                                                                                 // price
                                                                                                                 // for
                                                                                                                 // NaN
                                                                                                                 // limit
                                                                                                                 // prices
                    "Order from exchange with status " + orderInfoUpdate.status,
                    false,
                    null);

            orderInfo = activeOrders.get(orderInfoUpdate.orderId);
        }

        // Now update the order status
        if (orderInfo != null) {
            // Update order data
            OrderStatus oldStatus = orderInfo.getStatus();

            // Convert external status to our enum
            OrderStatus newStatus = parseOrderStatus(orderInfoUpdate.status.toString());

            // Skip if the status hasn't changed
            if (oldStatus == newStatus) {
                if (debug_mode_ordersendercontroller_listner) {
                    sendtolog("Order Update", 0f,
                            "Skipping update - status unchanged: " + orderInfoUpdate.orderId + " status: " + newStatus);
                }
                return;
            }

            orderInfo.setStatus(newStatus);

            if (orderInfoUpdate.limitPrice > 0 && !Double.isNaN(orderInfoUpdate.limitPrice)) {
                orderInfo.setPrice(orderInfoUpdate.limitPrice);
            }

            // For filled orders, move to history and update position if needed
            if (newStatus == OrderStatus.FILLED ||
                    newStatus == OrderStatus.CANCELED ||
                    newStatus == OrderStatus.REJECTED) {

                // Update position on fill and move to history
                if (newStatus == OrderStatus.FILLED) {
                    // Make sure we store the side information
                    orderSideMap.put(orderInfoUpdate.orderId, orderInfo.isBuy());

                    // If we're not using exchange positions, update position based on order
                    /*
                     * if (!useExchangePositions) {
                     * updatePositionForExecution(
                     * orderInfo.isBuy(),
                     * orderInfo.getSize(),
                     * orderInfo.getPrice()
                     * );
                     * }
                     */
                }

                // Move to history
                moveToHistory(orderInfoUpdate.orderId);
            }
        }
    }

    public void onOrderExecuted(ExecutionInfo executionInfo) {

        // DO NOT UNCOMMENT this block, this is not needed we handle execution in
        // onStatus method.!!!
        /*
         * if (debug_mode_ordersendercontroller_listner) {
         * sendtolog("Order Executed", 0f, "Order " + executionInfo.orderId +
         * " executed " +
         * executionInfo.size + " @ " + executionInfo.price + " for instance=" +
         * instanceId + ", algo=" + algo);
         * }
         * 
         * // Notify UI of execution
         * if (ui != null) {
         * ui.logExecution(executionInfo.orderId, executionInfo.size,
         * executionInfo.price);
         * ui.appendStatus("Execution: " + executionInfo.orderId + " " +
         * executionInfo.size +
         * " @ " + String.format("%.2f", executionInfo.price));
         * }
         * 
         * // Enhanced price validation for all order types
         * if (Double.isNaN(executionInfo.price) || executionInfo.price <= 0) {
         * sendtolog("Order Executed", 0f,
         * "Execution with invalid price detected, skipping: " +
         * executionInfo.orderId + ", price: " + executionInfo.price);
         * return;
         * }
         * 
         * // Process the execution (updating position logic should be here)
         * Boolean isBuy = orderSideMap.get(executionInfo.orderId);
         * if (isBuy == null) {
         * OrderInfo orderInfo = getOrderById(executionInfo.orderId);
         * if (orderInfo != null) {
         * isBuy = orderInfo.isBuy();
         * orderSideMap.put(executionInfo.orderId, isBuy);
         * }
         * }
         * 
         * if (isBuy != null) {
         * updatePositionForExecution(isBuy, executionInfo.size, executionInfo.price,
         * null);
         * } else {
         * sendtolog("Order Executed Warning", 0f,
         * "Execution received but order direction unknown: " +
         * executionInfo.orderId + ", unable to update position. Size: " +
         * executionInfo.size +
         * ", Price: " + executionInfo.price);
         * }
         */
    }

    double LastAccountBalance = 0.0;
    double StartingAccountBalance = 0.0;
    double LastLiquidityValue = 0.0;

    // Implementation of BalanceListener interface
    public void onBalance(BalanceInfo balanceInfo) {

        // Check if we're in simulation mode (not real trading) - check directly from RunModeHelper
        Boolean isRealTrading = RunModeHelper.isRealTrading();
        boolean isSimulation = !Boolean.TRUE.equals(isRealTrading);
        
        if (debug_mode_ordersendercontroller_listner) {
            MaindebugUI.log("onBalance called - isRealTrading: " + isRealTrading + ", isSimulation: " + isSimulation);
        }

        if (isSimulation) {
            // Override balance data for simulation mode
            if (StartingAccountBalance == 0.0) {
                StartingAccountBalance = 5000.0; // Set simulation starting balance to 5000
            }
            

            // Calculate available liquidity as starting balance + total global PnL
            double totalGlobalPnL = getGlobalTotalPnL();
            LastAccountBalance = StartingAccountBalance+ totalGlobalPnL; // Use the simulation starting balance
            LastLiquidityValue = StartingAccountBalance + totalGlobalPnL-2411.85*Math.abs(globalCurrentPosition);
        
            if (debug_mode_ordersendercontroller_listner) {
                MaindebugUI.log("Simulation Mode Balance Override" + 0f +
                        "Starting: " + StartingAccountBalance +
                        ", Current: " + LastAccountBalance +
                        ", Global PnL: " + totalGlobalPnL +
                        ", Liquidity: " + LastLiquidityValue);
            }
        } else {
            if (balanceInfo != null && balanceInfo.balancesInCurrency != null
                    && !balanceInfo.balancesInCurrency.isEmpty()) {
                for (var currencyBalance : balanceInfo.balancesInCurrency) {
                    // Use real balance data for live trading
                    LastAccountBalance = currencyBalance.balance;
                    if (StartingAccountBalance == 0.0 && LastAccountBalance < 1) {
                        StartingAccountBalance = currencyBalance.balance;
                    }
                    LastLiquidityValue = currencyBalance.netLiquidityValue;
                    Log.info("TSC Balance available in the loop: " + currencyBalance.balance);
                }
            }

            // accountBalances.put(key, currencyBalance.balance);
        }

        

        // Always update UI regardless of debug mode
        if (ui != null) {

            ui.updateAccountAndOrders(LastAccountBalance, workingSellOrders, workingBuyOrders, instanceCurrentPosition);
            ui.updateGlobalPnL(getGlobalUnrealizedPnL(), getGlobalRealizedPnL(), getGlobalTotalPnL());
            ui.logBalance(LastAccountBalance, lastAccountCurrency);
           
        } else {
            // Try to get the UI instance and store it for future use
            OrdersTestUIV3 foundUi = OrdersTestUIV3.getInstance(instrumentInfo, this, null);
            if (foundUi != null) {
                this.ui = foundUi; // Assign to instance field

                ui.updateAccountAndOrders(LastAccountBalance, workingSellOrders, workingBuyOrders,
                        instanceCurrentPosition);
                ui.updateGlobalPnL(getGlobalUnrealizedPnL(), getGlobalRealizedPnL(), getGlobalTotalPnL());
                ui.logBalance(LastAccountBalance, lastAccountCurrency);
               ui.updateAvailableLiquidity(LastLiquidityValue);
            ui.updateAlias(this.alias);
            ui.setRealtimeStatus(NowRealtime);

            }
        }
    }

    public boolean sendOcoBuyLimitBuyTrailStopOrder(final int size, final double limitPrice,
            final double initialStopPrice, final int trailingStep, final OrderDuration duration, final double stopLoss,
            final double takeProfit) {
        return sendOcoBuyLimitBuyTrailStopOrder(size, limitPrice, initialStopPrice, trailingStep, duration, stopLoss,
                takeProfit, 0.0);
    }

    public boolean sendOcoSellLimitSellStopOrder(final int size, final double limitPrice, final double stopPrice,
            final OrderDuration duration, final double stopLoss, final double takeProfit) {
        return sendOcoSellLimitSellStopOrder(size, limitPrice, stopPrice, duration, stopLoss, takeProfit, 0.0);
    }

    public boolean sendOcoSellLimitSellTrailStopOrder(final int size, final double limitPrice,
            final double initialStopPrice, final int trailingStep, final OrderDuration duration, final double stopLoss,
            final double takeProfit) {
        return sendOcoSellLimitSellTrailStopOrder(size, limitPrice, initialStopPrice, trailingStep, duration, stopLoss,
                takeProfit, 0.0);
    }

    // Add a method to set the position tracking mode
    public void setUseExchangePositions(boolean useExchangePositions) {
        this.useExchangePositions = useExchangePositions;
    }

    // Implementation of Layer1ApiTradingListener's onStatus method
    @Override
    public void onStatus(StatusInfo statusInfo) {
        if (debug_mode_ordersendercontroller_listner) {
            MaindebugUI.log("Status Update" + " Position: " + statusInfo.position +
                    ", Working Buys: " + statusInfo.workingBuys +
                    ", Working Sells: " + statusInfo.workingSells);
        }

        // Always update UI regardless of debug mode
        if (ui != null) {
            // Create a runnable class instead of a lambda to avoid classloader issues
            final StatusInfo statusInfoFinal = statusInfo;
            final double lastAccountBalance = LastAccountBalance;
            final int instancePos = instanceCurrentPosition;
            final int globalPos = globalCurrentPosition;

            SwingUtilities.invokeLater(new Runnable() {
                @Override
                public void run() {
                    ui.updateAccountAndOrders(lastAccountBalance, statusInfoFinal.workingSells,
                            statusInfoFinal.workingBuys, statusInfoFinal.position);
                    ui.updateGlobalPnL(getGlobalUnrealizedPnL(), getGlobalRealizedPnL(), getGlobalTotalPnL());
                    ui.updatePositions(instancePos, globalPos);
                }
            });
        }

        // Forward the status info to our existing position handling logic
        // onPositionUpdate(statusInfo);
    }

    // 2. Add a helper method to update positions consistently - MODIFIED SIGNATURE
    private void updatePositionForExecution(boolean isBuy, int size, double price, String entryReason) { // MODIFIED:
                                                                                                         // Added
                                                                                                         // entryReason
        boolean positionCycleResetForUnrealizedDD = false;
        int beforePosition = instanceCurrentPosition;

        // Guard for null side (R-5)
        if (isBuy != true && isBuy != false) {
            Log.warn("updatePositionForExecution: null side for order fill, skipping global position update");
            return;
        }
        // --- Validate/correct execution price using rolling window ---
        double validatedPrice = price;
        double rollingAvg = getRollingAverageMarketPrice();
        boolean corrected = false;
        String correctionReason = "";

        if (Double.isNaN(price) || price <= 0) {
            validatedPrice = rollingAvg;
            corrected = true;
            correctionReason = "Execution price invalid (NaN or <=0), skipping update for " + instanceId;
            return;
                } else {

        }
        if (corrected) {
            // Log the correction event to the debug UI general log
            OrderSenderControllerDebugUI.appendToLog(
                    "[ExecutionPriceCorrection] " + correctionReason + " (final used: " + validatedPrice + ")");
            // Try to log the context of the price derivation if available
            // We can't access OrderInfoUpdate directly here, but we can log the stack trace
            // and parameters
            StackTraceElement[] stack = Thread.currentThread().getStackTrace();
            StringBuilder sb = new StringBuilder();
            sb.append("Context for execution price correction:\n");
            sb.append("isBuy=").append(isBuy)
                    .append(", size=").append(size)
                    .append(", originalPrice=").append(price)
                    .append(", rollingAvg=").append(rollingAvg)
                    .append(", pips=").append(pips)
                    .append(", currentMarketPrice=").append(currentMarketPrice)
                    .append("\nStack trace (top 5):\n");
            for (int i = 2; i < Math.min(stack.length, 7); i++) {
                sb.append("  at ").append(stack[i].toString()).append("\n");
            }
            OrderSenderControllerDebugUI.appendToLog(sb.toString());
        }

        // --- Use validatedPrice for all position logic below ---
        if (validatedPrice <= 0 || Double.isNaN(validatedPrice)) {
            sendtoOrderStatusLog("Position Error", 0f,
                    "Invalid execution price after correction: " + validatedPrice + ", skipping update for "
                            + instanceId);
            return;
        }
        if (size <= 0) {
            sendtoOrderStatusLog("Position Error", 0f,
                    "Invalid execution size: " + size + ", skipping update for " + instanceId);
            return;
        }

        synchronized (globalPositions) {
            try {
                // Track the previous position values for logging
                // int beforePosition = instanceCurrentPosition; // MOVED to top of method
                double beforeInstanceRealizedPnL = instanceRealizedPnL;
                double beforeGlobalRealizedPnL = globalRealizedPnL;

                if (instancePosition == null) {
                    instancePosition = new Position(instanceId, validatedPrice, size, isBuy, InstrumentMultiplier, pips,
                            this);
                    if (entryReason != null && !entryReason.isEmpty()) {
                        instancePosition.originalEntryReason = entryReason;
                    }
                    globalPositions.add(instancePosition);
                    instanceCurrentPosition = instancePosition.getNetPosition();
                    positionCycleResetForUnrealizedDD = true; // ADDED: New position created
                    sendtolog("Position Create", 0f,
                            String.format("New position created: %s %d @ %.2f",
                                    isBuy ? "LONG" : "SHORT", size, validatedPrice));
                } else {
                    // FIX HERE: If position quantity is zero, treat it as a new position regardless
                    // of the stored direction
                    if (instancePosition != null && instancePosition.quantity == 0) {
                        instancePosition.reversePosition(size, validatedPrice, isBuy, null, entryReason); // Pass
                                                                                                          // entryReason
                                                                                                          // for new leg
                        if (entryReason != null && !entryReason.isEmpty()) { // Set for the overall instancePosition as
                                                                             // well
                            instancePosition.originalEntryReason = entryReason;
                        }
                        instanceCurrentPosition = instancePosition.getNetPosition();
                        positionCycleResetForUnrealizedDD = true; // ADDED: New position created after flat
                        sendtolog("Position Create", 0f,
                                String.format("New position created after flat: %s %d @ %.2f",
                                        isBuy ? "LONG" : "SHORT", size, validatedPrice));
                    } else if (instancePosition != null
                            && ((instancePosition.isLong && isBuy) || (!instancePosition.isLong && !isBuy))) {
                        instancePosition.addToPosition(size, validatedPrice, isBuy);
                        // If originalEntryReason was empty and this is the first fill establishing the
                        // position direction, set it.
                        if (instancePosition.originalEntryReason.isEmpty() && entryReason != null
                                && !entryReason.isEmpty()) {
                            instancePosition.originalEntryReason = entryReason;
                        }
                        instanceCurrentPosition = instancePosition.getNetPosition();
                        sendtolog("Position Add", 0f,
                                String.format("Added to position: now %d @ %.2f",
                                        instancePosition.quantity, instancePosition.entryPrice));
                    } else if (instancePosition != null) {
                        // Reducing or reversing position - opposite direction
                        int posToReduce = Math.min(size, instancePosition.quantity);
                        // Record position state before reduction for PnL calculation verification
                        double posEntryPrice = instancePosition.entryPrice;
                        int posQuantity = instancePosition.quantity;
                        boolean posIsLong = instancePosition.isLong;
                        sendtolog("Position reduction: About to reduce " + posToReduce + " contracts from " +
                                (posIsLong ? "LONG" : "SHORT") + " position of " + posQuantity +
                                " contracts at entry price " + posEntryPrice);
                        double realizedPnL = instancePosition.reducePosition(posToReduce, validatedPrice, isBuy, null);
                        sendtolog("Position reduced: Realized PnL = " + realizedPnL +
                                ", New position size: " + instancePosition.quantity +
                                ", Direction: " + (instancePosition.isLong ? "LONG" : "SHORT") +
                                ", New entry price: " + instancePosition.entryPrice);
                        // Log detailed PnL calculation for verification
                        if (verbose_position_logging) {
                            double expectedPnL = posIsLong ? (validatedPrice - posEntryPrice) * posToReduce
                                    : (posEntryPrice - validatedPrice) * posToReduce;
                            sendtolog("PnL Calculation", 0f,
                                    String.format("Expected PnL: %.2f, Actual PnL: %.2f, " +
                                            "Direction: %s, EntryPrice: %.2f, ExitPrice: %.2f, Size: %d",
                                            expectedPnL, realizedPnL,
                                            posIsLong ? "LONG" : "SHORT",
                                            posEntryPrice, validatedPrice, posToReduce));
                        }
                        // Update instance and global realized PnL tracking
                        instanceRealizedPnL += realizedPnL;
                        addToGlobalRealizedPnL(realizedPnL);
                        if (instancePosition.quantity == 0) {
                            positionCycleResetForUnrealizedDD = true; // ADDED: Position became flat
                            int remainingSize = size - posToReduce;
                            if (remainingSize > 0) {
                                instancePosition.reversePosition(remainingSize, validatedPrice, isBuy, null,
                                        entryReason); // Pass entryReason for new leg
                                if (entryReason != null && !entryReason.isEmpty()) { // Set for the overall
                                                                                     // instancePosition as well
                                    instancePosition.originalEntryReason = entryReason;
                                }
                            } else {
                                // Position completely closed - keep tracking object
                                sendtolog("Position Close", 0f,
                                        String.format(
                                                "Position closed with PnL=%.2f, total realized PnL for position: %.2f",
                                                realizedPnL, instancePosition.getTotalRealizedPnL()));
                            }
                        } else {
                            // Partial reduction
                            sendtolog("Position Reduce", 0f,
                                    String.format("Position reduced by %d @ %.2f, PnL=%.2f, Remaining: %d @ %.2f",
                                            posToReduce, validatedPrice, realizedPnL,
                                            instancePosition.quantity, instancePosition.entryPrice));
                        }
                        // Update instance position counter
                        instanceCurrentPosition = instancePosition.getNetPosition();
                    }
                }

                // Recalculate global position - only counting active positions
                int oldGlobalPos = globalCurrentPosition;
                int newGlobalPos = 0;
                for (Position pos : globalPositions) {
                    if (pos.isActive()) {
                        newGlobalPos += pos.getNetPosition();
                    }
                }
                int delta = newGlobalPos - globalCurrentPosition;
                addToGlobalCurrentPosition(delta);
                oldGlobalPos = globalCurrentPosition - delta;
                // Update displays if position changed
                if (beforePosition != instanceCurrentPosition || oldGlobalPos != globalCurrentPosition) {
                    updatePositionDisplays(oldGlobalPos, globalCurrentPosition);
                }

                // Log PnL updates
                if (instanceRealizedPnL != beforeInstanceRealizedPnL) {
                    sendtolog("PnL Update", 0f,
                            String.format("Instance Realized PnL: %.2f → %.2f (Δ %.2f)",
                                    beforeInstanceRealizedPnL, instanceRealizedPnL,
                                    instanceRealizedPnL - beforeInstanceRealizedPnL));

                    sendtolog("PnL Update", 0f,
                            String.format("Global Realized PnL: %.2f → %.2f (Δ %.2f)",
                                    beforeGlobalRealizedPnL, globalRealizedPnL,
                                    globalRealizedPnL - beforeGlobalRealizedPnL));
                }

                // Update PnL calculations with current market price
                // updateMarketPrice(currentMarketPrice);

                // Log summary of position after update
                if (verbose_position_logging) {
                    sendtolog("Position Summary", 0f,
                            String.format("After execution - Instance: %s, Position: %d, " +
                                    "Realized PnL: %.2f, Unrealized PnL: %.2f, Total PnL: %.2f",
                                    instanceId, instanceCurrentPosition,
                                    instanceRealizedPnL, getInstanceUnrealizedPnL(), getInstanceTotalPnL()));
                }

                if (ui != null) {
                    ui.updatePositions(instanceCurrentPosition, globalCurrentPosition);
                }

                /*
                 * // --- NEW: Log trade to performance metrics system ---
                 * // Determine the algo name for this trade (replace with your actual logic if
                 * needed)
                 * String algoName = this.algo != null ? this.algo : "GLOBAL";
                 * double realizedPnl = instanceRealizedPnL-beforeInstanceRealizedPnL;
                 * double commission = 0.0; // Set commission if you have it
                 * 
                 * TradePerformanceMetrics.getInstance().recordTradeFromPositionUpdate(
                 * algoName,
                 * isBuy,
                 * size,
                 * price,
                 * System.currentTimeMillis(),
                 * realizedPnl,
                 * commission
                 * );
                 * // --- END NEW ---
                 */

                // Check if flattening for disable is in progress and position reached zero
                if (isFlatteningForDisable && instanceCurrentPosition == 0) {
                    sendtolog("Positions flattened for disable process. Disabling live trading for instance "
                            + instanceId);
                    enableLiveTrading = false; // Disable live trading
                    isFlatteningForDisable = false; // Reset state variable
                    // You might want to add more cleanup or logging here if needed
                }

            } catch (IllegalArgumentException e) {
                sendtolog("Position Error", 0f,
                        "Error updating position: " + e.getMessage());
            }
        } // END synchronized (globalPositions)

        // ---------------------------------------------------------------------
        // Handle position‑side transitions *once* per update.
        // ---------------------------------------------------------------------
        int qtyBefore = beforePosition;
        int qtyAfter = instanceCurrentPosition;

        // 1. ☆ FLAT → NON‑FLAT (new trade begins) ----------------------------
        if (qtyBefore == 0 && qtyAfter != 0 && drawdownGuard != null) {
            // arm guard with the new entry‑notional
            double entryPx = instancePosition.entryPrice;
            long qtyAbs = Math.abs(qtyAfter);
            double pointVal = instrumentInfo != null
                    ? instrumentInfo.multiplier // 50 for ES, 5 for MES, …
                    : 1.0;
            drawdownGuard.armForNewPosition(entryPx, qtyAbs, pointVal);
            // A brand‑new position has no unrealised history.
            drawdownGuard.resetUnrealizedTrackingForNewTrade();
            if (OrderSenderControllerV2.DEBUG_DRAWDOWN_GUARD) {
                MaindebugUI.log(String.format(
                        "[DDG_ARM] Inst:%s | Algo:%s | New trade armed @ %.2f, qty=%d, ptVal=%.0f",
                        instanceId, algo, entryPx, qtyAbs, pointVal));
            }
            return; // nothing else to do for this transition
        }

        // 2. ☆ NON‑FLAT → FLAT (position is fully closed) --------------------
        if (qtyBefore != 0 && qtyAfter == 0 && drawdownGuard != null) {
            drawdownGuard.resetUnrealizedTrackingForNewTrade();
            if (OrderSenderControllerV2.DEBUG_DRAWDOWN_GUARD) {
                MaindebugUI.log(String.format(
                        "[DDG_RESET_UNREAL_TRADE] Inst:%s | Algo:%s | Flat now, peaks cleared.",
                        instanceId, algo));
            }
            return; // done
        }

        // 3. ☆ Still inside the same trade ------------------------------------
    }

    // Map to track order side for orders that may get removed from activeOrders
    private final Map<String, Boolean> orderSideMap = new ConcurrentHashMap<>();
    private final Set<String> processedOrders = ConcurrentHashMap.newKeySet();

    // Add a deduplication mechanism for execution updates
    private final Set<String> processedExecutions = ConcurrentHashMap.newKeySet();

    // Add a periodic cleanup method for processedExecutions set
    private void scheduleProcessedExecutionsCleanup() {
        globalExecutor.scheduleAtFixedRate(() -> {
            int sizeBeforeCleanup = processedExecutions.size();
            if (sizeBeforeCleanup > 1000) {
                processedExecutions.clear();
                if (debug_mode_ordersendercontroller) {
                    sendtolog("Cleanup", 0f, "Cleared " + sizeBeforeCleanup + " processed executions");
                }
            }
        }, 1, 1, TimeUnit.MINUTES);
    }

    // Helper method to determine order type
    private OrderType determineOrderType(OrderInfoUpdate orderInfoUpdate) {
        // Check for stop and limit prices - explicitly check for NaN values
        boolean isStop = orderInfoUpdate.stopPrice > 0 && !Double.isNaN(orderInfoUpdate.stopPrice);
        boolean isLimit = orderInfoUpdate.limitPrice > 0 && !Double.isNaN(orderInfoUpdate.limitPrice);

        // Additional check for trailing stop orders if applicable
        boolean isTrailingStop = isStop && orderInfoUpdate.clientId != null &&
                (orderInfoUpdate.clientId.contains("TrailStop") ||
                        orderInfoUpdate.clientId.toLowerCase().contains("trailing"));

        if (isTrailingStop) {
            return OrderType.TRAIL_STOP;
        } else if (isStop && isLimit) {
            return OrderType.STOP_LIMIT;
        } else if (isStop) {
            return OrderType.STOP;
        } else if (isLimit) {
            return OrderType.LIMIT;
        } else {
            // Default to MARKET if no other price data is available - preserve original
            // behavior
            return OrderType.MARKET;
        }
    }

    // Add a static set to track processed order IDs across all instances
    private static final Set<String> globalProcessedOrderIds = ConcurrentHashMap.newKeySet();
    // Add a static map to track which instance "owns" a specific order ID
    private static final Map<String, String> orderIdToInstanceMap = new ConcurrentHashMap<>();

    // Add a static map to track the relationship between client IDs and the sending
    // instance
    private static final Map<String, String> clientIdToInstanceId = new ConcurrentHashMap<>();

    // Add a static map to track the relationship between exchange order IDs and
    // client IDs
    private static final Map<String, String> exchangeOrderIdToClientId = new ConcurrentHashMap<>();

    // Add enhanced logging configurations (these were missing)
    public static boolean verbose_position_logging = true;
    public static boolean verbose_order_mapping_logging = true;

    // Replace the instanceReversePositions method with the enhanced version
    public boolean instanceReversePositions() {
        if (instanceCurrentPosition == 0) {
            sendtolog("ReversePositions", 0f, "No open positions to reverse for " + instanceId);
            return false;
        }

        sendtolog("ReversePositions", 0f, "Starting reverse operation for instance " + instanceId +
                " with current position: " + instanceCurrentPosition);

        // Store the original position value before we start modifying it
        final int originalPosition = instanceCurrentPosition;

        // Set the reverseInProgress flag
        reverseInProgress.set(true);

        scheduleOrder(() -> {
            if (originalPosition > 0) {
                int qtyToSell = 2 * originalPosition;
                if (debug_mode_ordersendercontroller) {
                    sendtolog("Market Sell Order", (float) qtyToSell, "Sending reverse order from position " +
                            originalPosition + " to position " + (-originalPosition));
                }

                // Generate a client ID for tracking
                String clientId = algo + currentmilliseconds + "ReverseSell";

                // Register which instance created this client ID
                clientIdToInstanceId.put(clientId, instanceId);
                sendtolog("Order Tracking", 0f, "Registered client ID " + clientId + " to instance " + instanceId);

                boolean orderSuccess = orderSender.sendMarketSellOrder(qtyToSell, OrderDuration.DAY, 0.0, 0.0,
                        clientId);

                if (orderSuccess) {
                    // Register the order and immediately update position
                    String orderId = clientId; // For market orders, use clientId as orderId
                    registerOrder(orderId, clientId, false, OrderType.MARKET, qtyToSell, currentMarketPrice,
                            "Market Sell Reversal Order", false, null);

                    sendtolog("Reversed long positions, sold", (float) qtyToSell, "units.");
                } else {
                    sendtolog("Market Sell Order", (float) qtyToSell, "Reverse order rejected by exchange");
                }
            } else if (originalPosition < 0) {
                int qtyToBuy = 2 * Math.abs(originalPosition);
                if (debug_mode_ordersendercontroller) {
                    sendtolog("Market Buy Order", (float) qtyToBuy, "Sending reverse order from position " +
                            originalPosition + " to position " + (-originalPosition));
                }

                // Generate a client ID for tracking
                String clientId = algo + currentmilliseconds + "ReverseBuy";

                // Register which instance created this client ID
                clientIdToInstanceId.put(clientId, instanceId);
                sendtolog("Order Tracking", 0f, "Registered client ID " + clientId + " to instance " + instanceId);

                boolean orderSuccess = orderSender.sendMarketBuyOrder(qtyToBuy, OrderDuration.DAY, 0.0, 0.0, clientId);

                if (orderSuccess) {
                    // Register the order and immediately update position
                    String orderId = clientId; // For market orders, use clientId as orderId
                    registerOrder(orderId, clientId, true, OrderType.MARKET, qtyToBuy, currentMarketPrice,
                            "Market Buy Reversal Order", false, null);

                    sendtolog("Reversed short positions, bought", (float) qtyToBuy, "units.");
                } else {
                    sendtolog("Market Buy Order", (float) qtyToBuy, "Reverse order rejected by exchange");
                }
            }

            // Reset the reverse flag
            reverseInProgress.set(false);
            sendtolog("ReversePositions", 0f, "Completed reverse operation for instance " + instanceId);

            // Add position debug logging
            StringBuilder allPositions = new StringBuilder("All positions after reversal:\n");
            synchronized (globalPositions) {
                for (Position pos : globalPositions) {
                    allPositions.append("  - ")
                            .append(pos.instanceId)
                            .append(": ")
                            .append(pos.isLong ? "LONG " : "SHORT ")
                            .append(pos.quantity)
                            .append(" @ ")
                            .append(pos.entryPrice)
                            .append("\n");
                }
                allPositions.append("Global position: ").append(globalCurrentPosition);
                allPositions.append("\nInstance position: ").append(instanceCurrentPosition);
            }
            sendtolog("Position Summary", 0f, allPositions.toString());
        });
        return true;
    }

    // Add the debugDumpOrderTracking method which was missing
    public void debugDumpOrderTracking() {
        StringBuilder sb = new StringBuilder("=== ORDER TRACKING STATE ===\n");

        // Order to instance mapping
        sb.append("\nOrder ID to Instance mapping (").append(orderIdToInstanceMap.size()).append(" items):\n");
        for (Map.Entry<String, String> entry : orderIdToInstanceMap.entrySet()) {
            sb.append("  - Order ").append(entry.getKey()).append(" → Instance ").append(entry.getValue()).append("\n");
        }

        // Exchange order ID to client ID mapping
        sb.append("\nExchange Order ID to Client ID mapping (").append(exchangeOrderIdToClientId.size())
                .append(" items):\n");
        for (Map.Entry<String, String> entry : exchangeOrderIdToClientId.entrySet()) {
            sb.append("  - Exchange ").append(entry.getKey()).append(" → Client ").append(entry.getValue())
                    .append("\n");
        }

        sendtolog("OrderTrackingDump", 0f, sb.toString());
    }

    // Add the logNumericOrderDistribution method which was missing
    public static void logNumericOrderDistribution() {
        Map<String, Integer> instanceOrderCounts = new HashMap<>();

        synchronized (orderIdToInstanceMap) {
            for (Map.Entry<String, String> entry : orderIdToInstanceMap.entrySet()) {
                if (entry.getKey().matches("\\d+")) { // Only count numeric IDs
                    String instanceId = entry.getValue();
                    instanceOrderCounts.put(instanceId, instanceOrderCounts.getOrDefault(instanceId, 0) + 1);
                }
            }
        }

        StringBuilder sb = new StringBuilder("=== NUMERIC ORDER DISTRIBUTION ===\n");
        for (Map.Entry<String, Integer> entry : instanceOrderCounts.entrySet()) {
            sb.append("Instance ").append(entry.getKey()).append(": ").append(entry.getValue()).append(" orders\n");
        }

        sendtolog("OrderTracking", 0f, sb.toString());
    }

    /**
     * Registers a mapping between an exchange order ID and a client ID.
     * This static method is called from OrderSender to help with order tracking.
     * 
     * @param exchangeOrderId The order ID provided by the exchange
     * @param clientId        The client ID that was used when creating the order
     */
    public static void registerExchangeOrderIdMapping(String exchangeOrderId, String clientId) {
        if (exchangeOrderId != null && clientId != null) {
            exchangeOrderIdToClientId.put(exchangeOrderId, clientId);

            if (verbose_order_mapping_logging) {
                sendtolog("Order Registration", 0f, "Mapped exchange order ID " + exchangeOrderId +
                        " to client ID " + clientId);
            }
        }
    }

    /**
     * Cancel all buy orders
     * 
     * @return Number of orders cancelled
     */
    public int cancelAllBuyOrders() {
        int cancelCount = cancelOrdersByCategory(OrderCategory.BUY);

        if (debug_mode_ordersendercontroller) {
            sendtolog("Cancel All Buy Orders", 0f, "Cancelled " + cancelCount + " buy orders");
        }

        // Update OrdersTestUIV3 log if available

        if (ui != null) {
            ui.appendStatus("Cancelled " + cancelCount + " buy orders");
        }

        return cancelCount;
    }

    /**
     * Cancel all sell orders
     * 
     * @return Number of orders cancelled
     */
    public int cancelAllSellOrders() {
        int cancelCount = cancelOrdersByCategory(OrderCategory.SELL);

        if (debug_mode_ordersendercontroller) {
            sendtolog("Cancel All Sell Orders", 0f, "Cancelled " + cancelCount + " sell orders");
        }

        // Update OrdersTestUIV3 log if available

        if (ui != null) {
            ui.appendStatus("Cancelled " + cancelCount + " sell orders");
        }

        return cancelCount;
    }

    /**
     * Get the total position across all OrderSenderControllerV2 instances
     * 
     * @return The sum of all instance positions
     */
    public static int getTotalPositionAcrossAllInstances() {
        int totalPosition;

        synchronized (instances) {
            totalPosition = instances.stream()
                    .mapToInt(controller -> controller.instanceCurrentPosition)
                    .sum();
        }

        if (debug_mode_ordersendercontroller) {
            sendtolog("Position Summary", 0f, "Total position across all instances: " + totalPosition);
        }

        return totalPosition;
    }

    /**
     * Gets the total realized PnL across all OrderSenderControllerV2 instances
     * 
     * @return Total realized PnL
     */
    public static double getTotalRealizedPnLAcrossAllInstances() {
        synchronized (instances) {
            double totalRealizedPnL = 0.0;
            for (OrderSenderControllerV2 controller : instances) {
                totalRealizedPnL += controller.getInstanceRealizedPnL();
            }

            if (debug_mode_ordersendercontroller) {
                sendtolog("PnL Tracking", 0f, "Total realized PnL across all instances: " + totalRealizedPnL);
            }

            return totalRealizedPnL;
        }
    }

    /**
     * Gets the total unrealized PnL across all OrderSenderControllerV2 instances
     * 
     * @return Total unrealized PnL
     */
    public static double getTotalUnrealizedPnLAcrossAllInstances() {
        synchronized (instances) {
            double totalUnrealizedPnL = 0.0;
            for (OrderSenderControllerV2 controller : instances) {
                totalUnrealizedPnL += controller.getInstanceUnrealizedPnL();
            }

            if (debug_mode_ordersendercontroller) {
                sendtolog("PnL Tracking", 0f, "Total unrealized PnL across all instances: " + totalUnrealizedPnL);
            }

            return totalUnrealizedPnL;
        }
    }

    /**
     * Gets the combined total (realized + unrealized) PnL across all
     * OrderSenderControllerV2 instances
     * 
     * @return Total combined PnL
     */
    public static double getTotalCombinedPnLAcrossAllInstances() {
        double total = getTotalRealizedPnLAcrossAllInstances() + getTotalUnrealizedPnLAcrossAllInstances();

        if (debug_mode_ordersendercontroller) {
            sendtolog("PnL Tracking", 0f, "Total combined PnL across all instances: " + total);
        }

        return total;
    }

    /**
     * Verifies that global PnL tracking matches instance-based PnL tracking
     * 
     * @return true if global and instance-based PnL tracking match within a small
     *         tolerance
     */
    public static boolean verifyPnLTracking() {
        double globalRealizedPnL = getGlobalRealizedPnL();
        double globalUnrealizedPnL = getGlobalUnrealizedPnL();
        double globalTotalPnL = getGlobalTotalPnL();

        double instanceRealizedPnL = getTotalRealizedPnLAcrossAllInstances();
        double instanceUnrealizedPnL = getTotalUnrealizedPnLAcrossAllInstances();
        double instanceTotalPnL = getTotalCombinedPnLAcrossAllInstances();

        // Allow small floating-point differences (0.01 tolerance)
        boolean realizedMatch = Math.abs(globalRealizedPnL - instanceRealizedPnL) < 0.01;
        boolean unrealizedMatch = Math.abs(globalUnrealizedPnL - instanceUnrealizedPnL) < 0.01;
        boolean totalMatch = Math.abs(globalTotalPnL - instanceTotalPnL) < 0.01;

        if (debug_mode_ordersendercontroller) {
            if (!realizedMatch || !unrealizedMatch || !totalMatch) {
                sendtolog("PnL Tracking", 0f, "PnL tracking mismatch detected:\n" +
                        "Global Realized: " + globalRealizedPnL + " vs Instance-based: " + instanceRealizedPnL + "\n" +
                        "Global Unrealized: " + globalUnrealizedPnL + " vs Instance-based: " + instanceUnrealizedPnL
                        + "\n" +
                        "Global Total: " + globalTotalPnL + " vs Instance-based: " + instanceTotalPnL);
            } else {
                sendtolog("PnL Tracking", 0f, "PnL tracking verification passed");
            }
        }

        return realizedMatch && unrealizedMatch && totalMatch;
    }

    /**
     * Generates a complete PnL report across all instances
     * 
     * @return A string containing detailed PnL information
     */
    public static String generatePnLReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== PNL REPORT ===\n\n");

        // Global summary
        report.append("GLOBAL SUMMARY:\n");
        report.append(String.format("Realized PnL: %.2f\n", getGlobalRealizedPnL()));
        report.append(String.format("Unrealized PnL: %.2f\n", getGlobalUnrealizedPnL()));
        report.append(String.format("Total PnL: %.2f\n\n", getGlobalTotalPnL()));

        // Instance details
        report.append("INSTANCE DETAILS:\n");

        synchronized (instances) {
            List<OrderSenderControllerV2> sortedInstances = new ArrayList<>(instances);

            // Sort instances by total PnL (descending)
            Collections.sort(sortedInstances,
                    (a, b) -> Double.compare(b.getInstanceTotalPnL(), a.getInstanceTotalPnL()));

            for (OrderSenderControllerV2 instance : sortedInstances) {
                double realized = instance.getInstanceRealizedPnL();
                double unrealized = instance.getInstanceUnrealizedPnL();
                double total = instance.getInstanceTotalPnL();
                int position = instance.getInstancePosition();

                report.append(String.format("Instance: %s\n", instance.instanceId));
                report.append(String.format("  Position: %d\n", position));
                report.append(String.format("  Realized PnL: %.2f\n", realized));
                report.append(String.format("  Unrealized PnL: %.2f\n", unrealized));
                report.append(String.format("  Total PnL: %.2f\n\n", total));
            }
        }

        // Positions summary
        report.append("ACTIVE POSITIONS:\n");
        synchronized (globalPositions) {
            List<Position> activePositions = globalPositions.stream()
                    .filter(Position::isActive)
                    .collect(Collectors.toList());

            if (activePositions.isEmpty()) {
                report.append("  No active positions\n\n");
            } else {
                for (Position pos : activePositions) {
                    report.append(String.format("  %s: %s %d @ %.2f (PnL: %.2f)\n",
                            pos.instanceId,
                            pos.isLong ? "LONG" : "SHORT",
                            pos.quantity,
                            pos.entryPrice,
                            pos.unrealizedPnL));
                }
                report.append("\n");
            }
        }

        // Add verification
        boolean verified = verifyPnLTracking();
        report.append("PnL Tracking Verification: ").append(verified ? "PASSED" : "FAILED").append("\n");

        return report.toString();
    }

    @Override
    public void onTimestamp(long t) {
        // TODO Auto-generated method stub
        if (t != 0 && referenceTimeNanos != 0) {
            currentnanoseconds = t;
            // Use referenceTimeNanos if currentnanoseconds is zero

            currentmilliseconds = currentnanoseconds / 1_000_000L;
        }
        // --- Reduce-only mode Bookmap-timeout check ---
        if (tradingState.get() == TradingState.REDUCE_ONLY && reduceOnlyModeBookmapTimeoutMillis > 0
                && reduceOnlyModeBookmapStartMillis > 0) {
            long elapsed = currentmilliseconds - reduceOnlyModeBookmapStartMillis;
            if (elapsed >= reduceOnlyModeBookmapTimeoutMillis) {
                tradingState.set(TradingState.ACTIVE);
                reduceOnlyModeBookmapStartMillis = 0;
                reduceOnlyModeBookmapTimeoutMillis = 0;
                sendtolog("ReduceOnlyMode", 0f,
                        "Reduce-only mode auto-disabled after Bookmap timeout for instance " + instanceId);

                if (ui != null) {
                    ui.setTradingDisabledReason("", 0L);
                }
            }
        }

        // --- Propagate Bookmap time to OrdersTestUIV3 for countdowns ---

        if (ui != null) {
            ui.setCurrentBookmapMillis(currentmilliseconds);
        }
        // Update TradePerformanceMetrics with the current Bookmap time
        // TradePerformanceMetrics.setCurrentBookmapTime(currentmilliseconds);
    }

    // Rolling window for recent market prices (contract price units)
    private static final int ROLLING_WINDOW_SIZE = 120;
    private final ArrayDeque<Double> recentMarketPrices = new ArrayDeque<>(ROLLING_WINDOW_SIZE);

    // Add field to store live trading status
    private volatile boolean enableLiveTrading = false;

    public boolean isEnableLiveTrading() {
        return enableLiveTrading;
    }

    // Add with other static fields:
    private static volatile boolean globalEnableLiveTrading = false;

    // Add static getter and setter:
    public static boolean isGlobalEnableLiveTrading() {
        return globalEnableLiveTrading;
    }

    public static void setGlobalEnableLiveTrading(boolean enabled) {
        globalEnableLiveTrading = enabled;

        // DONT need to do below.
        /*
         * // Notify all UI instances of the live trading status change
         * for (OrderSenderControllerV2 instance : instances) {
         * if (instance.ui != null) {
         * instance.ui.setEnableLiveTradingStatus(enabled);
         * }
         * }
         */
    }

    // Add static lock and helpers for global PnL/counters (preserve comments)
    private static final Object PNL_LOCK = new Object();

    private static void addToGlobalRealizedPnL(double d) {
        synchronized (PNL_LOCK) {
            globalRealizedPnL += d;
        }
    }

    private static void resetGlobalRealizedPnL() {
        synchronized (PNL_LOCK) {
            globalRealizedPnL = 0.0;
        }
    }

    private static void addToGlobalCurrentDailyLoss(double d) {
        synchronized (PNL_LOCK) {
            globalCurrentDailyLoss += d;
        }
    }

    private static void resetGlobalCurrentDailyLoss() {
        synchronized (PNL_LOCK) {
            globalCurrentDailyLoss = 0.0;
        }
    }

    private static void addToGlobalCurrentPosition(int d) {
        synchronized (PNL_LOCK) {
            globalCurrentPosition += d;
        }
    }

    private static void resetGlobalCurrentPosition() {
        synchronized (PNL_LOCK) {
            globalCurrentPosition = 0;
        }
    }

    /**
     * A-6: Single entry point for cleanup operations
     * Public method to shutdown and cleanup this controller
     */
    public void shutdown() {
        cleanup();
    }

    // Helper to clip order size to obey position limits
    // OLD:
    // private int clipOrderSize(int currentPosition, int requestedSize, boolean
    // isBuy, int limit) {
    // int newPosition = currentPosition + (isBuy ? requestedSize : -requestedSize);
    // if (Math.abs(newPosition) <= limit) {
    // return requestedSize;
    // }
    // if (isBuy) {
    // return Math.max(0, limit - currentPosition);
    // } else {
    // return Math.max(0, limit + currentPosition);
    // }
    // }

    // NEW: Reject orders that would exceed the position limit (return 0 if not
    // allowed)
    private int clipOrderSize(int currentPosition, int requestedSize, boolean isBuy, int limit) {
        int newPosition = currentPosition + (isBuy ? requestedSize : -requestedSize);
        if (Math.abs(newPosition) <= limit) {
            return requestedSize;
        }
        // If the requested order would exceed the limit, reject by returning 0
        return 0;
    }

    /**
     * Returns true if the instance daily loss limit is exceeded and only
     * closing/reducing orders are allowed.
     */
    public boolean isLossLimitCloseOnly() {
        return isReduceOnlyMode();
    }

    // Add static reference time for all instances
    private static volatile long referenceTimeNanos = 0;

    public static void setReferenceTime(long referenceTimeNanos) {
        OrderSenderControllerV2.referenceTimeNanos = referenceTimeNanos;
        if (referenceTimeNanos != 0) {
            currentnanoseconds = referenceTimeNanos;
            currentmilliseconds = referenceTimeNanos / 1_000_000L;
        }
        synchronized (instances) {
            for (OrderSenderControllerV2 osc : instances) {
                osc.onTimestamp(referenceTimeNanos);
            }
        }

    }

    private static ScheduledExecutorService createNewExecutor() {
        return Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(() -> {
                try {
                    r.run();
                } catch (Throwable t1) {
                    Log.error("Scheduled task error", t1);
                }
            });
            t.setName("OrderSenderControllerV2-GlobalExecutor");
            t.setDaemon(true); // Ensure the executor thread doesn't prevent JVM shutdown
            return t;
        });
    }

    public static synchronized void restartGlobalExecutor() {
        if (globalExecutor != null && !globalExecutor.isShutdown()) {
            globalExecutor.shutdownNow();
        }
        globalExecutor = createNewExecutor();
        isGlobalExecutorShutdown = false;
        Log.info("OrderSenderControllerV2: globalExecutor restarted.");
    }

    // Shutdown for the new aggregator service
    private static void shutdownGlobalAggregatorService() {
        if (globalAggregatorService != null && !globalAggregatorService.isShutdown()) {
            globalAggregatorService.shutdown();
            try {
                if (!globalAggregatorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    globalAggregatorService.shutdownNow();
                }
            } catch (InterruptedException ie) {
                globalAggregatorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    // --- Health tracking for market price updates ---
    // Change to V2
    InputDataHealthTrackerV2 inputDataHealthTracker;
    private boolean ready = false;
    // Restore healthMonitoringEnabled flag
    private boolean healthMonitoringEnabled = true;

    /**
     * Returns the health status of the market price input data for this controller.
     */
    public InputDataHealthTrackerV2.Status getDataHealthStatus() {
        return inputDataHealthTracker.getStatus();
    }

    /**
     * Returns true if the controller is ready (market price feed is healthy).
     */
    public boolean isReady() {

        return inputDataHealthTracker.isHealthy() && ui != null && ui.isReady();
    }

    // Restore setHealthMonitoringEnabled method for this class
    public void setHealthMonitoringEnabled(boolean enabled) {
        this.healthMonitoringEnabled = enabled;
    }

    // Add a utility method to convert nanoseconds to "HH:mm:ss.SSS"
    /**
     * Converts the given nanoseconds (since epoch) to a formatted time string
     * "HH:mm:ss.SSS".
     * If input is 0 or negative, returns "00:00:00.000".
     */
    public static String formatNanosecondsToTime(long nanoseconds) {
        if (nanoseconds <= 0) {
            return "00:00:00.000";
        }
        long millis = nanoseconds / 1_000_000L;
        java.time.Instant instant = java.time.Instant.ofEpochMilli(millis);
        java.time.ZoneId zone = java.time.ZoneId.systemDefault();
        java.time.LocalTime time = instant.atZone(zone).toLocalTime();
        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss.SSS");
        return time.format(formatter);
    }

    /**
     * Releases resources that belong strictly to *this* controller instance.
     * Global objects and Swing singletons are disposed in {@link #cleanupAll()}.
     */
    public void cleanup() {
        Log.info("OrderSenderControllerV2: starting instance cleanup for " + instanceId);

        /* ───────── 1. Unregister from API provider ───────── */
        if (api != null) {
            try {
                Layer1ApiProvider provider = api.getProvider();
                if (provider != null) {
                    ListenableHelper.removeListeners(provider, this);
                    Log.info("OrderSenderControllerV2: unregistered provider for " + instanceId);
                }
            } catch (Exception e) {
                Log.error("OrderSenderControllerV2: API‐unregister failed – " + e.getMessage(), e);
            }
        }

        /* ───────── 2. Instance helpers & tasks ───────── */
        if (orderSender != null) {
            orderSender.cleanup();
        }

        // ─── Refresh UI time-gate display on cleanup ───
        OrdersTestUIV3 ui = OrdersTestUIV3.getInstance(instrumentInfo, this, null);
        if (ui != null) {
            var snap = getTimeGateSnapshot();
            ui.updateTimeGate(snap.state, snap.positionCap, snap.orderCap, snap.secondsToNext);
        }

        /* ───────── Inform OrdersTestUIV3 to deregister this controller ───────── */

        if (ui != null) {
            this.ui.updateAccountAndOrders(lastAccountBalance, workingSellOrders, workingBuyOrders,
                    instanceCurrentPosition);
            this.ui.updateGlobalPnL(getGlobalUnrealizedPnL(), getGlobalRealizedPnL(), getGlobalTotalPnL());
            this.ui.updateStatusWarningLabel();
            ui.deregisterController(this.algo);
        }

        /* ───────── 3. Remove from global registry ───────── */
        instances.remove(this);

        /* ───────── 4. Clear instance state ───────── */
        activeOrders.clear();
        orderHistory.clear();
        orderClientIdToOrderId.clear();
        orderSideMap.clear();
        processedOrders.clear();
        processedExecutions.clear();

        // Reset DrawdownGuard and Position PnL trackers for this instance
        if (drawdownGuard != null) {
            drawdownGuard.resetDaily();
        }
        // instancePosition itself is nulled out later, but reset its internal trackers
        // first if it exists.
        if (instancePosition != null) {
            instancePosition.resetPnLTrackers();
        }

        instanceCurrentDailyLoss = 0;
        instanceMaxDailyLoss = 0;
        instanceCurrentPosition = 0;
        instanceMaxPositionSize = 0;
        instanceRealizedPnL = 0;
        instanceMaxDailyProfitRealized = 0; // Reset profit target
        instanceMaxDailyProfitTotal = 0; // Reset total profit target
        enableProfitTargetChecks = true; // Reset to default enabled state
        lastUpdateTime = null;
        instanceLastBuyOrderTimeMillis = 0;
        instanceLastSellOrderTimeMillis = 0;

        // --- Ensure instancePosition is reset and removed from globalPositions ---
        instancePosition = null;
        synchronized (globalPositions) {
            globalPositions.removeIf(p -> instanceId.equals(p.instanceId));
        }

        enableLiveTrading = true; // Ensure live trading is disabled on cleanup
        isFlatteningForDisable = false; // Reset flattening state
        autoDisableOnProfitTarget = false; // Reset auto-disable flag
        enableProfitTargetChecks = true;

        /* Remove this instance's records from shared collections */
        synchronized (globalPositions) {
            globalPositions.removeIf(p -> instanceId.equals(p.instanceId));
        }
        synchronized (orderIdToInstanceMap) {
            orderIdToInstanceMap.entrySet()
                    .removeIf(e -> instanceId.equals(e.getValue()));
        }
        clientIdToInstanceId.entrySet()
                .removeIf(e -> instanceId.equals(e.getValue()));

        Log.info("OrderSenderControllerV2: instance cleanup complete for " + instanceId);
    }

    /**
     * One-shot global cleanup to be called on application shutdown.
     * 1. Runs {@link #cleanup()} on every remaining instance.
     * 2. Disposes Swing singletons & other global resources.
     * 3. Resets all static state and shuts down executors.
     */
    public static void cleanupAll() {
        Log.info("OrderSenderControllerV2: starting global cleanup (" +
                instances.size() + " instance(s))");

        /* ───────── 1. Instance-level cleanup ───────── */
        for (OrderSenderControllerV2 controller : new ArrayList<>(instances)) {
            try {
                controller.cleanup();
            } catch (Exception e) {
                Log.error("OrderSenderControllerV2: instance cleanup failed – " +
                        e.getMessage(), e);
            }
        }

        /* ───────── 2. Dispose global / UI resources ───────── */
        final PerformanceDashboardUI pduiToCleanup = performanceDashboardUI; // Capture for lambda

        Runnable disposeUI = () -> {
            try {
                if (pduiToCleanup != null) {
                    pduiToCleanup.cleanup(); // Call instance cleanup method
                }
                OrderSenderControllerDebugUI.disposeInstance();
                MaindebugUI.disposeInstance();

                if (testUIFrame != null) {
                    testUIFrame.dispose();
                    testUIFrame = null;
                }
            } catch (Exception uiEx) {
                Log.error("OrderSenderControllerV2: UI-disposal error – " +
                        uiEx.getMessage(), uiEx);
            }
        };

        try {
            // Always use invokeLater for UI disposal during cleanup to avoid potential
            // deadlocks
            // Ensuring UI operations are on the EDT without blocking the calling thread.
            javax.swing.SwingUtilities.invokeLater(disposeUI);
        } catch (Exception e) {
            // invokeLater itself doesn't throw InterruptedException or
            // InvocationTargetException
            // Catching a general Exception for any unexpected issues during scheduling.
            Log.error("OrderSenderControllerV2: failed to schedule global UI dispose – " +
                    e.getMessage(), e);
        }

        OrdersTestUIV3.disposeInstance();

        /* ───────── 3. Reset static references ───────── */
        debugUIInstance = null;
        maindebugUIInstance = null;
        performanceDashboardUI = null; // Nullify after cleanup is scheduled

        resetGlobalCurrentDailyLoss();
        resetGlobalCurrentPosition();
        resetGlobalRealizedPnL();

        shutdownGlobalAggregatorService(); // Shutdown the new service

        currentMarketPrice = 0.0;

        globalPositions.clear();

        globalProcessedOrderIds.clear();
        instances.clear();

        // In OrderSenderControllerV2.cleanupAll() or similar global shutdown:
        if (TradePerformanceMetrics.getInstance() != null) {
            TradePerformanceMetrics.getInstance().clear();
        }

        if (globalExecutor != null && !globalExecutor.isShutdown()) {
            globalExecutor.shutdown(); // Disable new tasks from being submitted
            try {
                // Wait a finite time for existing tasks to terminate
                if (!globalExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    globalExecutor.shutdownNow(); // Cancel currently executing tasks
                    // Wait a little more for tasks to respond to being cancelled
                    if (!globalExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                        Log.error("OrderSenderControllerV2: Global executor did not terminate.");
                    }
                }
            } catch (InterruptedException ie) {
                // (Re-)Cancel if current thread also interrupted
                globalExecutor.shutdownNow();
                // Preserve interrupt status
                Thread.currentThread().interrupt();
            }
            isGlobalExecutorShutdown = true;
        }

        shutdownGlobalAggregatorService(); // Shutdown the new service

        Log.info("OrderSenderControllerV2: global cleanup complete");
    }

    /**
     * Returns a specific status message if a profit target is hit (Realized OR
     * Total), otherwise an empty string.
     * Prioritizes Total PnL messages if that target is hit.
     * 
     * @return String indicating which profit target was hit, or empty.
     */
    public String getProfitTargetStatusMessage() {
        if (!enableProfitTargetChecks) {
            return "";
        }

        boolean realizedProfitTargetHit = (instanceMaxDailyProfitRealized > 0
                && instanceRealizedPnL >= instanceMaxDailyProfitRealized);

        double currentUnrealizedPnL = getInstanceUnrealizedPnL();
        double currentTotalPnL = instanceRealizedPnL + currentUnrealizedPnL;
        boolean totalProfitTargetHit = (instanceMaxDailyProfitTotal > 0
                && currentTotalPnL >= instanceMaxDailyProfitTotal);

        // If neither active target is hit, return empty string
        if (!((realizedProfitTargetHit && instanceMaxDailyProfitRealized > 0)
                || (totalProfitTargetHit && instanceMaxDailyProfitTotal > 0))) {
            return "";
        }

        // Determine message based on which target is hit and auto-disable state
        // Prioritize Total Profit Target message if it's active and hit.
        if (totalProfitTargetHit && instanceMaxDailyProfitTotal > 0) {
            if (autoDisableOnProfitTarget) {
                return "Total Profit Met - Auto-Disabled";
            }
            return "Total Profit Met - Close Only";
        }

        // If Total wasn't hit (or not active), check Realized Profit Target.
        if (realizedProfitTargetHit && instanceMaxDailyProfitRealized > 0) {
            if (autoDisableOnProfitTarget) {
                return "Realized Profit Met - Auto-Disabled";
            }
            return "Realized Profit Met - Close Only";
        }

        return ""; // Fallback, should ideally be covered by the initial check
    }

    /**
     * Displays a rejected order icon on the last trade indicator at the current
     * market price.
     * 
     * @param price The price at which to display the icon.
     */
    private void displayRejectedOrderIcon(double price) {
        if (lastTradeIndicator != null) {
            IconFactory.addIconWithAutoClearance(
                    lastTradeIndicator,
                    IconFactory.ShapeType.REJECTED,
                    Color.RED,
                    IconFactory.IconSize.SMALL,
                    IconFactory.Position.CENTER_OF,
                    price);
        }
    }

    private boolean isFlatteningForDisable = false; // State variable to track flattening for disable process

    /**
     * Sets whether to automatically disable trading when a profit target is hit and
     * close only is enabled.
     * 
     * @param autoDisable true to enable auto-disable, false to disable.
     */
    public void setAutoDisableOnProfitTarget(boolean autoDisable) {
        this.autoDisableOnProfitTarget = autoDisable;
        sendtolog("Auto-disable on profit target set to: " + autoDisable + " for instance " + instanceId);
    }

    /**
     * Returns whether auto-disable on profit target is enabled.
     * 
     * @return true if enabled, false otherwise.
     */
    public boolean isAutoDisableOnProfitTarget() {
        return autoDisableOnProfitTarget;
    }

    public void stop() {
        sendtolog("OrderSenderControllerV2: stop called for instance: " + instanceId);
        // Perform cleanup when the strategy is stopped
        cleanup();

        // Additional stop-specific actions if any (e.g., unregistering listeners)
        // Currently, cleanup handles the main state resets.
        enableLiveTrading = true; // Ensure flag is false on stop as well
        autoDisableOnProfitTarget = false; // Ensure flag is false on stop as well
    }

    // Add a static getter for pips for use in static context
    public static double getPipsStatic() {
        // Use the first instance's pips if available, otherwise default to 1.0
        if (!instances.isEmpty()) {
            for (OrderSenderControllerV2 osc : instances) {
                if (osc != null && osc.pips > 0)
                    return osc.pips;
            }
        }
        return 1.0;
    }

    // Add simulation mode flag
    private static volatile boolean simulationMode = false;

    // Add a private static helper to set simulationMode based on Bookmap run mode
    private static void updateSimulationModeFromRunMode() {
        // Defensive: check for nulls (API returns Boolean, not boolean)
        Boolean isLive = RunModeHelper.isLive();
        Boolean isRealTrading = RunModeHelper.isRealTrading();
        // If either is false or null, treat as simulation
        simulationMode = !(Boolean.TRUE.equals(isLive) && Boolean.TRUE.equals(isRealTrading));
    }

    // Add static getter/setter for simulation mode
    public static boolean isSimulationMode() {
        return simulationMode;
    }

    public static void setSimulationMode(boolean enabled) {
        simulationMode = enabled;
    }

    // --- Reduce Only Mode ---
    /**
     * If true, only orders that reduce or close the current position are allowed.
     * This can be set by risk logic or manually via the setter.
     */
    private volatile boolean reduceOnlyMode = false;

    // At the very top of the class or in a static block, add logging to verify
    // state on startup
    static {
        sendtolog("OrderSenderControllerV2 static init: globalPositions.size="
                + (globalPositions != null ? globalPositions.size() : -1)
                + ", globalRealizedPnL=" + globalRealizedPnL
                + ", globalCurrentPosition=" + globalCurrentPosition);
    }

    // ───────────────────────────────────────────────────────────────────────
    // TRADING‑STATE MACHINE (replaces boolean flags)
    // ───────────────────────────────────────────────────────────────────────
    private enum TradingState {
        /** All orders allowed. */
        ACTIVE,
        /** Only strictly reducing orders allowed; can auto‑expire. */
        REDUCE_ONLY,
        /** Stuck in reduce‑only until resetDailyRisk(). */
        DISABLED_PERMANENT
    }

    /** Single source of truth, CAS‑protected. */
    private final java.util.concurrent.atomic.AtomicReference<TradingState> tradingState = new java.util.concurrent.atomic.AtomicReference<>(
            TradingState.ACTIVE);

    // Helper: centralised banner text
    private String getTradingDisabledReason() {
        switch (tradingState.get()) {
            case DISABLED_PERMANENT:
                return "Permanently Disabled (Risk/Profit Limit)";
            case REDUCE_ONLY:
                return "Reduce-Only Mode";
            default:
                return "";
        }
    }

    public boolean isPermanentlyDisabled() {
        return tradingState.get() == TradingState.DISABLED_PERMANENT;
    }

    private long reduceOnlyModeBookmapTimeoutMillis = 0;
    private long reduceOnlyModeBookmapStartMillis = 0;

    public boolean isReduceOnlyMode() {
        TradingState s = tradingState.get();
        return s == TradingState.REDUCE_ONLY || s == TradingState.DISABLED_PERMANENT;
    }

    public boolean isProfitTargetCloseOnly() {
        return isReduceOnlyMode();
    }

    public void setReduceOnlyMode(boolean reduceOnly) {
        tradingState.updateAndGet(s -> {
            if (s == TradingState.DISABLED_PERMANENT)
                return s; // immutable
            return reduceOnly ? TradingState.REDUCE_ONLY : TradingState.ACTIVE;
        });

        if (ui != null) {
            ui.setTradingDisabledReason(getTradingDisabledReason(), reduceOnly ? 0L : currentmilliseconds);
        }
        sendtolog("ReduceOnlyMode", 0f, "Reduce-only mode set to: " + reduceOnly + " for instance " + instanceId);
    }

    public void setReduceOnlyModeWithTimeout(boolean reduceOnly, int seconds) {

        if (tradingState.get() == TradingState.DISABLED_PERMANENT && !reduceOnly) {
            sendtolog("ReduceOnlyMode", 0f,
                    "Attempt to unset reduce-only mode (with timeout) blocked: permanently disabled for risk/profit limits.");

            if (ui != null) {
                ui.setTradingDisabledReason("Permanently Disabled (Risk/Profit Limit)", 0L);
            }
            return;
        }
        setReduceOnlyMode(reduceOnly);
        if (reduceOnly && seconds > 0) {
            reduceOnlyModeBookmapStartMillis = currentmilliseconds;
            reduceOnlyModeBookmapTimeoutMillis = seconds * 1000L;
            sendtolog("ReduceOnlyMode", 0f, "Reduce-only mode will auto-disable after " + seconds
                    + " seconds (Bookmap time) for instance " + instanceId);

            if (ui != null) {
                long until = currentmilliseconds + seconds * 1000L;
                ui.setTradingDisabledReason(getTradingDisabledReason(), until);
            }
        } else {
            reduceOnlyModeBookmapStartMillis = 0;
            reduceOnlyModeBookmapTimeoutMillis = 0;

            if (ui != null) {
                ui.setTradingDisabledReason("", 0L);
            }
        }
    }

    public void setEnableLiveTrading(boolean enable) {
        if (tradingState.get() == TradingState.DISABLED_PERMANENT && enable) {
            sendtolog("LiveTrading", 0f,
                    "Attempt to re-enable trading blocked: permanently disabled for risk/profit limits.");

            if (ui != null) {
                ui.setTradingDisabledReason("Permanently Disabled (Risk/Profit Limit)", 0L);
            }
            return;
        }
        this.enableLiveTrading = enable;

        if (ui != null) {
            ui.setEnableLiveTradingStatus(enable);
            if (enable) {
                ui.setTradingDisabledReason("", 0L);
                if (this.ui != null) {
                    this.ui.updateAccountAndOrders(lastAccountBalance, workingSellOrders, workingBuyOrders,
                            instanceCurrentPosition);
                    this.ui.updateGlobalPnL(getGlobalUnrealizedPnL(), getGlobalRealizedPnL(), getGlobalTotalPnL());
                    this.ui.updateStatusWarningLabel();
                }
            } else {
                ui.setTradingDisabledReason("Manual Disable", 0L);
            }
        }
    }

    public void resetDailyRisk() {
        if (debug_mode_ordersendercontroller) {
            sendtolog(instanceId + " OrderSenderControllerV2: resetDailyRisk called. Trading state set to ACTIVE.");
            if (OrderSenderControllerV2.DEBUG_DRAWDOWN_GUARD) {
                MaindebugUI.log(String.format(
                        "[RESET_DAILY_RISK] Inst: %s | Algo: %s | Resetting daily risk. Previous unrealizedDrawdownFlattenCount: %d",
                        this.instanceId, this.algo,
                        (drawdownGuard != null ? drawdownGuard.getUnrealizedDrawdownFlattenCount() : -1)));
            }
        }
        instanceCurrentDailyLoss = 0;
        // globalCurrentDailyLoss is reset elsewhere or managed globally, not per
        // instance reset.
        // instanceCurrentPosition does not get reset here, it reflects the actual
        // current position.

        // Reset trading state to allow trading again if it was DISABLED_PERMANENT
        // or if reduceOnlyMode was active due to a previous condition that's now
        // resolved.
        tradingState.set(TradingState.ACTIVE);
        reduceOnlyMode = false; // Explicitly turn off reduce-only mode
        reduceOnlyModeBookmapTimeoutMillis = 0; // Clear any timeout for reduce-only
        reduceOnlyModeBookmapStartMillis = 0;

        // Reset PnL trackers in the Position object and DrawdownGuard state
        if (instancePosition != null) {
            instancePosition.resetPnLTrackers();
        }
        if (drawdownGuard != null) {
            drawdownGuard.resetDaily();
        }

        // Reset profit target related flags
        // profitTargetHit = false; // This flag was removed, replaced by
        // isProfitTargetCloseOnly()
        // isProfitTargetCloseOnly is a state derived from PnL, not a flag to be reset
        // directly here.
        // The PnL itself (instanceRealizedPnL, instancePosition.unrealizedPnL) will
        // determine this state.

        sendtolog(instanceId, 0f, "Daily risk parameters and trading state have been reset. Trading is ACTIVE.");

        // If using OrdersTestUIV3, update its status warning label
        // Assuming 'this' can be passed as OrderSenderControllerV2
        if (ui != null && ui.isReady()) {
            ui.setTradingDisabledReason("", 0L); // Clear any disable reason
            // The UI will update its status label during its next scheduled refresh based
            // on controller state
        }
    }

    public void closePositionsAndDisableTrading() {
        if (enableLiveTrading) {
            sendtolog("Initiating close positions and disable trading process for instance " + instanceId);
            isFlatteningForDisable = true;

            if (ui != null) {
                String reason = getProfitTargetStatusMessage();
                if (reason.isEmpty())
                    reason = "Profit/Loss Target Met";
                ui.setTradingDisabledReason(reason, 0L);
            }
            if (instanceCurrentPosition != 0) {
                instanceFlattenPositions(); // Initiate flattening
            }
            tradingState.set(TradingState.DISABLED_PERMANENT);
        } else {
            sendtolog("Live trading is already disabled for this instance " + instanceId);
        }
    }

    // Helper to get dynamic position cap for current time
    private int getDynamicPositionCap() {
        return hoursManager.capPosition(Instant.ofEpochMilli(currentmilliseconds), instanceMaxPositionSize);
    }

    /** Emergency flatten & disable by draw‑down guard */
    private void emergencyClose(String reason) {
        sendtolog("Draw‑down Guard", 0f, reason + " – flattening all positions and disabling trading");
        this.lastDrawdownCloseReason = reason; // Set reason
        closePositionsAndDisableTrading(); // existing helper
    }

    // Getter for the DrawdownGuard instance
    public DrawdownGuard getDrawdownGuard() {
        // Consider thread safety if DrawdownGuard is mutable and accessed concurrently.
        // If it's immutable or its state changes are internally synchronized, this is
        // fine.
        return drawdownGuard;
    }

    /**
     * Resets all global daily counters (loss, position, PnL).
     * This should be called at the start of a new global trading day/session for
     * all strategies.
     */
    public static synchronized void resetGlobalDailyCounters() {
        resetGlobalRealizedPnL();
        resetGlobalCurrentDailyLoss();
        resetGlobalCurrentPosition();
        resetGlobalRealizedPnL();
        sendtolog("OrderSenderControllerV2", 0f, "GLOBAL daily counters (loss, position, PnL) have been reset.");
    }

    private void checkDrawdownAndLimits() {
        if (drawdownGuard == null || tradingState.get() == TradingState.DISABLED_PERMANENT) {
            return; // No checks if no guard or permanently disabled
        }

        if (OrderSenderControllerV2.DEBUG_DRAWDOWN_GUARD) {
            MaindebugUI.log(String.format(
                    "[CDDL_CHECK] Inst: %s | Algo: %s | Checking DD. totalDDHit: %b, unrDDAction: %b, unrDDCount: %d, current TradingState: %s",
                    this.instanceId, this.algo, drawdownGuard.hasTotalDrawdownHit(),
                    drawdownGuard.hasUnrealizedDrawdownActionTaken(), drawdownGuard.getUnrealizedDrawdownFlattenCount(),
                    tradingState.get()));
        }

        // Priority 1: Total Drawdown
        if (drawdownGuard.hasTotalDrawdownHit()) {
            if (this.configTotalDDActionEnabled) { // Check if OSC instance is configured to ACT on total DD
                if (OrderSenderControllerV2.DEBUG_DRAWDOWN_GUARD) {
                    MaindebugUI.log(String.format(
                            "[CDDL_ACTION] Inst: %s | Algo: %s | Total DD HIT AND ACTION ENABLED. Reason: %s. Calling emergencyClose(). Current TradingState: %s",
                            this.instanceId, this.algo, drawdownGuard.getTotalDrawdownTriggerReason(),
                            tradingState.get()));
                }
                emergencyClose(drawdownGuard.getTotalDrawdownTriggerReason());
                // NOTE: After emergencyClose, tradingState might change to DISABLED_PERMANENT
                // or REDUCE_ONLY
                // This return is crucial to prevent further actions if a total DD emergency
                // stop is triggered.
                return; // Exit if total drawdown action was taken
            } else { // Total DD hit, but OSC instance is configured NOT to act
                if (OrderSenderControllerV2.DEBUG_DRAWDOWN_GUARD) {
                    MaindebugUI.log(String.format(
                            "[CDDL_INFO] Inst: %s | Algo: %s | Total DD HIT BUT ACTION DISABLED. Reason: %s. Current TradingState: %s. Will proceed to check unrealized drawdown.",
                            this.instanceId, this.algo, drawdownGuard.getTotalDrawdownTriggerReason(),
                            tradingState.get()));
                }
                // If total DD action is disabled, we no longer return here, allowing
                // fall-through to unrealized DD check.
            }
            // OLD POSITION of return; was here. It's been moved up if action is enabled.
        }

        // Priority 2: Unrealized Drawdown Flatten (if total drawdown not hit, OR if
        // total DD hit but its action was disabled at OSC level)
        if (drawdownGuard.hasUnrealizedDrawdownActionTaken()) {
            if (this.configUnrealizedDDActionEnabled) { // Check if OSC instance is configured to ACT on unrealized DD
                if (OrderSenderControllerV2.DEBUG_DRAWDOWN_GUARD) {
                    MaindebugUI.log(String.format(
                            "[CDDL_ACTION] Inst: %s | Algo: %s | Unrealized DD Action Taken flag IS TRUE and ACTION IS ENABLED. Reason: %s. Calling emergencyFlattenForUnrealizedDrawdown(). Current TradingState: %s, unrDDFlattenCount: %d, unrDDDetectionCount: %d",
                            this.instanceId, this.algo, drawdownGuard.getUnrealizedDrawdownTriggerReason(),
                            tradingState.get(), drawdownGuard.getUnrealizedDrawdownFlattenCount(),
                            drawdownGuard.getUnrealizedDrawdownDetectionCount()));
                }
                emergencyFlattenForUnrealizedDrawdown(drawdownGuard.getUnrealizedDrawdownTriggerReason());
            } else { // Unrealized DD action indicated by guard, but OSC instance is configured NOT
                     // to act
                if (OrderSenderControllerV2.DEBUG_DRAWDOWN_GUARD) {
                    MaindebugUI.log(String.format(
                            "[CDDL_INFO] Inst: %s | Algo: %s | Unrealized DD Action Taken flag IS TRUE BUT OSC ACTION DISABLED. Reason: %s. Current TradingState: %s, unrDDFlattenCount: %d, unrDDDetectionCount: %d",
                            this.instanceId, this.algo, drawdownGuard.getUnrealizedDrawdownTriggerReason(),
                            tradingState.get(), drawdownGuard.getUnrealizedDrawdownFlattenCount(),
                            drawdownGuard.getUnrealizedDrawdownDetectionCount()));
                }
            }
        } else { // Guard indicates no unrealized drawdown action is currently needed
            if (OrderSenderControllerV2.DEBUG_DRAWDOWN_GUARD) {
                MaindebugUI.log(String.format(
                        "[CDDL_INFO] Inst: %s | Algo: %s | Unrealized DD Action Taken flag IS FALSE. No emergency flatten call. OSC Action Enabled: %b. Current TradingState: %s, unrDDFlattenCount: %d, unrDDDetectionCount: %d",
                        this.instanceId, this.algo, this.configUnrealizedDDActionEnabled, tradingState.get(),
                        drawdownGuard.getUnrealizedDrawdownFlattenCount(),
                        drawdownGuard.getUnrealizedDrawdownDetectionCount()));
            }
        }

        // Profit Target Check (only if not already disabled by drawdown)
        // ... existing code ...
    }

    private void emergencyFlattenForUnrealizedDrawdown(String reason) {
        if (tradingState.get() == TradingState.DISABLED_PERMANENT) {
            if (debug_mode_ordersendercontroller)
                sendtolog(
                        instanceId + " emergencyFlattenForUnrealizedDrawdown: SKIPPING, already permanently disabled.");
            return;
        }
        if (drawdownGuard.hasTotalDrawdownHit() && this.configTotalDDActionEnabled) {
            if (debug_mode_ordersendercontroller)
                sendtolog(instanceId
                        + " emergencyFlattenForUnrealizedDrawdown: SKIPPING, total drawdown is also hit. emergencyClose should handle.");
            if (tradingState.get() != TradingState.DISABLED_PERMANENT) {
                // If total drawdown is hit, use its reason for emergencyClose
                String totalDDReason = drawdownGuard.getTotalDrawdownTriggerReason();
                if (totalDDReason == null || totalDDReason.isEmpty())
                    totalDDReason = "Total Drawdown hit during unrealized flatten attempt";
                this.lastDrawdownCloseReason = totalDDReason; // Set reason for total DD
                emergencyClose(totalDDReason);
            }
            return;
        }
        this.lastDrawdownCloseReason = reason; // Set reason for unrealized DD flatten

        int countBefore = drawdownGuard.getUnrealizedDrawdownFlattenCount();
        if (OrderSenderControllerV2.DEBUG_DRAWDOWN_GUARD) {
            MaindebugUI.log(String.format(
                    "[EFFUD_ENTER] Inst: %s | Algo: %s | Reason: %s. Current unrealizedDrawdownFlattenCount: %d. unrealizedDrawdownActionTaken: %b",
                    this.instanceId, this.algo, reason, countBefore, drawdownGuard.hasUnrealizedDrawdownActionTaken()));
        }

        drawdownGuard.incrementUnrealizedDrawdownFlattenCount();
        int countAfter = drawdownGuard.getUnrealizedDrawdownFlattenCount();

        if (debug_mode_ordersendercontroller) {
            sendtolog(instanceId + " OrderSenderControllerV2: emergencyFlattenForUnrealizedDrawdown for " + reason +
                    ". Count incremented from " + countBefore + " to " + countAfter);
        }
        if (OrderSenderControllerV2.DEBUG_DRAWDOWN_GUARD) { // Check flag before logging
            MaindebugUI.log(String.format(
                    "[EFFUD_COUNT_INC] Inst: %s | Algo: %s | Reason: %s. Unrealized flatten counter incremented from %d to %d.",
                    this.instanceId, this.algo, reason, countBefore, countAfter));
        }

        requestStrategyForFlatteningUnrealised = true; // Set flag for strategy to handle

        boolean flattenSuccess = instanceFlattenPositions();

        if (flattenSuccess) {
            if (debug_mode_ordersendercontroller)
                sendtolog(instanceId
                        + " emergencyFlattenForUnrealizedDrawdown: Instance positions flattened successfully.");
        } else {
            if (debug_mode_ordersendercontroller)
                sendtolog(instanceId + " emergencyFlattenForUnrealizedDrawdown: Failed to flatten instance positions.");
        }

        if (OrderSenderControllerV2.DEBUG_DRAWDOWN_GUARD) {
            MaindebugUI.log(String.format(
                    "[EFFUD_EXIT] Inst: %s | Algo: %s | Flatten attempt made (Success: %b). New unrealizedDrawdownFlattenCount: %d",
                    this.instanceId, this.algo, flattenSuccess, countAfter));
        }
    }

    // New method to update DrawdownGuard settings
    public void updateDrawdownGuardSettings(double ddCash, double ddPct,
            double ddUnrCash, double ddUnrPct,
            double ddGivePct, double profitEpsilonTicks) {
        if (DEBUG_DRAWDOWN_GUARD) {
            sendtolog(instanceId + " Updating DrawdownGuard settings: ddCash=" + ddCash + ", ddPct=" + ddPct +
                    ", ddUnrCash=" + ddUnrCash + ", ddUnrPct=" + ddUnrPct +
                    ", ddGivePct=" + ddGivePct + ", profitEpsilonTicks=" + profitEpsilonTicks);
        }
        this.drawdownGuard = new DrawdownGuard(ddCash, ddPct, ddUnrCash, ddUnrPct, ddGivePct, profitEpsilonTicks,
                this.configTotalDDActionEnabled, this.configUnrealizedDDActionEnabled, this.instanceId, this.algo,
                this);
        // TODO: Consider the implications of changing guard settings mid-trade.
        // Resetting certain states or re-arming with current position info might be
        // necessary.
        // For example:
        // this.drawdownGuard.resetDaily();
        // if (this.instancePosition != null && this.instancePosition.isActive()) {
        // this.drawdownGuard.armForNewPosition(this.instancePosition.entryPrice,
        // Math.abs(this.instancePosition.getNetPosition()),
        // this.instrumentInfo.pips); // or pointValue if available
        // this.drawdownGuard.updateEntryNotional(this.instancePosition.entryPrice *
        // Math.abs(this.instancePosition.getNetPosition()) * this.instrumentInfo.pips);
        // // or pointValue
        // }
    }
    public static int getGlobalPositionLimit() {
        return GLOBAL_MAX_POSITION_SIZE;
    }
}


