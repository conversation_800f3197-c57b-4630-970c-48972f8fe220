package com.bookmap.api.simple.demo.utils.data;

import java.util.Map;
import java.util.TreeMap;
import java.util.Arrays;
import velox.api.layer1.common.Log;
import velox.api.layer1.simplified.SnapshotEndListener;
import velox.api.layer1.simplified.DepthDataListener;
import velox.api.layer1.simplified.TimeListener;

/**
 * Enhanced version of MultiLevelOrderBookExponential that incorporates delta tracking functionality.
 * This class combines exponential decay weights with delta tracking in a unified implementation.
 */
public class MultiLevelOrderBookExpwithQDSelfCalcV3 extends MultiLevelOrderBookSumSelfCalc implements  DepthDataListener, SnapshotEndListener,TimeListener {

    // Debug flag - set to false for production performance
    private static final boolean DEBUG_MODE = false;
    
    // Exponential weighting functionality
    private double[] decayFactors;
    private double decayFactor = 0.85; // Default decay factor
    
    /* ===== Liquidity cluster detection fields ===== */
    private final java.util.NavigableMap<Integer, Long> lastUpdateNanos = new java.util.concurrent.ConcurrentSkipListMap<>();
    private final LiquidityClusterAnalyzer liquidityClusterAnalyzer;

    // Configuration parameters (could be wired to @Config)
    private final int clusterRadiusTicks = 2;
    private final long minClusterCumSize = 200;
    private final int maxTargetsPerSide = 5;
    private final double decayFactorClusters = 0.85;
    private final long crossoverWindowMillis = 5000;
    private final double crossoverThreshold = 0.60;
    
    // Market type constants
    private static final int MARKET_GENERIC = 0;
    private static final int MARKET_ES = 1;
    private static final int MARKET_NQ = 2;
    private static final int MARKET_BTC = 3;
    private static final int MARKET_FOREX = 4;
    private static final int MARKET_EQUITIES = 5;
    
    // Optimal decay factors for different market types
    private static final double DECAY_FACTOR_ES = 0.85;
    private static final double DECAY_FACTOR_NQ = 0.80;
    private static final double DECAY_FACTOR_BTC = 0.70;
    private static final double DECAY_FACTOR_FOREX = 0.90;
    private static final double DECAY_FACTOR_EQUITIES = 0.85;
    private static final double DECAY_FACTOR_GENERIC = 0.85;
      // Session adjustment factors
    private static final double OVERNIGHT_DECAY_ADJUSTMENT = 0.05;
    
    // Named constants for magic numbers used in decay calculations
    private static final int NUM_STANDARD_DECAY_LEVELS = 3;
    private static final int DEEP_STANDARD_LEVEL_INDEX = 2; // Index for the 3rd standard level
    private static final int DEEP_STANDARD_LEVEL_SIZE_THRESHOLD = 100;
    private static final double DEEP_STANDARD_LEVEL_DECAY_MIN = 0.5;
    private static final double DEEP_STANDARD_LEVEL_DECAY_ADJUSTMENT = 0.05;
    private static final int EXTRA_DECAY_APPLY_LEVEL_INDEX_THRESHOLD = 2; // Level index 2 and above (3rd level onwards)
    private static final int EXTRA_DECAY_APPLY_DISTANCE_THRESHOLD = 20;
    private static final double EXTRA_DECAY_FACTOR_COMPONENT = -0.05; // Multiplied by (distance - threshold)
    
    private int marketType = MARKET_GENERIC;
    private boolean isOvernightSession = false;
    
    // QuotesDelta decay factors
    private static final double QUOTES_DELTA_DECAY_FACTOR_PRIMARY = 0.90;
    private static final double QUOTES_DELTA_DECAY_FACTOR_SECONDARY = 0.85;
    private static final double QUOTES_DELTA_DECAY_FACTOR_DEEP = 0.70;
      // Maximum distance from best price to consider
    private static final int MAX_DISTANCE = 200;
    
    // Liquidity cluster analyzer scan depth in ticks
    private static final int LIQUIDITY_SCAN_DEPTH_TICKS = 200;
    
    // Delta tracking functionality
    private int[] bidDeltas;
    private int[] askDeltas;
    
    // Size filtering
    private int minSizeFilter = 0;
    private int maxSizeFilter = Integer.MAX_VALUE;
    
    // Price depth tracking
    private TreeMap<Integer, Integer> bidPriceDepths;
    private TreeMap<Integer, Integer> askPriceDepths;
    
    // Snapshot completion tracking
    private volatile boolean snapshotCompleted = false;

    // Renamed for clarity and specificity - removed redundant variable
    private int QD_LastLevelsToProcess = 0;    /**
     * Constructor with market type parameter
     * 
     * @param maxLevels Array of max levels to track for each level index
     * @param marketType Market type constant
     */
    public MultiLevelOrderBookExpwithQDSelfCalcV3(int[] maxLevels, int marketType) {


        super(maxLevels);
        if (maxLevels == null || maxLevels.length == 0) { throw new IllegalArgumentException("maxLevels cannot be null or empty."); }
        

        this.marketType = marketType;
        
        initializeDataStructures();
        setDecayFactorByMarketType();
        calculateDecayFactors(maxLevels);
        
        if (DEBUG_MODE) {
            Log.info("MultiLevelOrderBookExpwithQD initialized with decay factor: " + decayFactor + 
                    " and delta tracking for " + maxLevels.length + " levels");
        }
        // --- Liquidity cluster analyzer init ---
        this.liquidityClusterAnalyzer = new LiquidityClusterAnalyzer(
                bidPriceDepths,
                askPriceDepths,
                lastUpdateNanos,
                clusterRadiusTicks,
                minClusterCumSize,
                maxTargetsPerSide,
                decayFactorClusters,
                crossoverWindowMillis,
                crossoverThreshold);
    }    /**
     * Default constructor - uses generic market type
     * 
     * @param maxLevels Array of max levels to track
     */
    public MultiLevelOrderBookExpwithQDSelfCalcV3(int[] maxLevels) {
        this(maxLevels, MARKET_GENERIC);
    }      /**
     * Constructor with custom decay factors
     * 
     * @param maxLevels Array of max levels to track
     * @param decayFactors Array of decay factors to use
     */
    public MultiLevelOrderBookExpwithQDSelfCalcV3(int[] maxLevels, double[] decayFactors) {
        super(maxLevels);
        
        initializeDataStructures();
        
        this.decayFactors = decayFactors;
        if (decayFactors.length > 0) {
            this.decayFactor = decayFactors[0];
        }
        
        this.liquidityClusterAnalyzer = new LiquidityClusterAnalyzer(
                bidPriceDepths,
                askPriceDepths,
                lastUpdateNanos,
                clusterRadiusTicks,
                minClusterCumSize,
                maxTargetsPerSide,
                decayFactorClusters,
                crossoverWindowMillis,
                crossoverThreshold);
        
        if (DEBUG_MODE) {
            Log.info("MultiLevelOrderBookExpwithQD initialized with custom decay factors");
        }
    }
    
    /**
     * Initialize all internal data structures
     */
    private void initializeDataStructures() {
        // Initialize decay factors array
        decayFactors = new double[maxLevels.length];
        
        // Initialize delta tracking arrays
        bidDeltas = new int[maxLevels.length];
        askDeltas = new int[maxLevels.length];
        
        // Initialize depth tracking maps
        bidPriceDepths = new TreeMap<>((a, b) -> b - a); // Descending for bids
        askPriceDepths = new TreeMap<>(); // Ascending for asks
    }

    /**
     * Overridden depth update method that tracks deltas and maintains the order book
     */
    @Override
    public synchronized void onDepth(boolean isBid, int price, int size) {
        // Get previous size by calling parent implementation
        //
        super.onDepth(isBid, price, size);
        lastUpdateNanos.put(price, currentTimestampNanos);
        int oldSize = super.getOldSize();

        // Only track deltas after snapshot completion and if there's a size change
        if (snapshotCompleted && oldSize != size) {
            int delta = size - oldSize;
            
            // Skip further processing if there's no actual change
            if (delta != 0) {
                // Apply size filtering
                boolean passesFilter = (size >= minSizeFilter && size <= maxSizeFilter) || 
                                      (oldSize >= minSizeFilter && oldSize <= maxSizeFilter);
                
                if (passesFilter) {
                    // Get depth of this price by directly querying the sorted maps
                    Map<Integer, Integer> sortedMap = isBid ? super.getSortedBids() : super.getSortedAsks();
                    int depth = calculatePriceDepth(sortedMap, isBid, price);
                    
                    // Apply skip levels to the depth
                    int adjustedDepth = depth - getSkipLevels();
                    
                    // Only process if adjustedDepth is valid (non-negative)
                    if (adjustedDepth >= 0) {
                        // Determine which array indices to process based on QD_LastLevelsToProcess
                        int startIdx, endIdx;
                        
                        if (QD_LastLevelsToProcess > 0 && QD_LastLevelsToProcess <= maxLevels.length) {
                            // Only process the last N levels as specified
                            startIdx = maxLevels.length - QD_LastLevelsToProcess;
                            endIdx = maxLevels.length;
                        } else {
                            // Process all levels
                            startIdx = 0;
                            endIdx = maxLevels.length;
                        }
                        
                        // For each level in our processing range
                        for (int i = startIdx; i < endIdx; i++) {
                            // CRITICAL FIX: We need to ensure each level only tracks updates within its depth boundary
                            // Only update delta if this depth is within the level's max depth
                            if (adjustedDepth < maxLevels[i]) {
                                if (isBid) {
                                    bidDeltas[i] += delta;
                                } else {
                                    askDeltas[i] += delta;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
      /**
     * Calculate the depth of a price in the order book
     * 
     * @param sortedMap the sorted bid or ask map
     * @param isBid whether this is for the bid side
     * @param targetPrice the price to find the depth for
     * @return the depth of the price (0-based)
     */
    private int calculatePriceDepth(Map<Integer, Integer> sortedMap, boolean isBid, int targetPrice) {
        // Optimized for TreeMap, which is what super.getSortedBids/Asks return.
        if (sortedMap instanceof TreeMap) {
            TreeMap<Integer, Integer> treeMap = (TreeMap<Integer, Integer>) sortedMap;
            if (isBid) {
                // Bids are sorted descending by price. We want count of prices > targetPrice.
                // tailMap(targetPrice, false) gives entries whose keys are strictly greater than targetPrice.
                return treeMap.tailMap(targetPrice, false).size();
            } else {
                // Asks are sorted ascending by price. We want count of prices < targetPrice.
                // headMap(targetPrice, false) gives entries whose keys are strictly less than targetPrice.
                return treeMap.headMap(targetPrice, false).size();
            }
        } else {
            // Fallback to original iteration logic if not a TreeMap (defensive coding)
            int depth = 0;
            if (isBid) {
                for (int price : sortedMap.keySet()) {
                    if (price > targetPrice) depth++;
                    else break;
                }
            } else {
                for (int price : sortedMap.keySet()) {
                    if (price < targetPrice) depth++;
                    else break;
                }
            }
            return depth;
        }
    }

    /**
     * Calculate exponentially weighted sum with size filtering
     */
    @Override
    public int getSizeSum(boolean isBid, int levelIndex, int minSize, int maxSize) {
        // Basic validation
        if (levelIndex < 0 || levelIndex >= maxLevels.length) {
            if (DEBUG_MODE) {
                Log.error("Level index out of bounds: " + levelIndex);
            }
            return 0;
        }
        
        // Normalize filter parameters
        minSize = Math.max(0, minSize);
        maxSize = Math.max(minSize, maxSize);
        
        // Calculate the sum with exponential decay
        return calculateExponentialWeightedSum(isBid, levelIndex, minSize, maxSize);
    }
    
    /**
     * Original getSizeSum method for backwards compatibility
     */
    @Override
    public int getSizeSum(boolean isBid, int levelIndex) {
        return getSizeSum(isBid, levelIndex, 0, Integer.MAX_VALUE);
    }
    
    /**
     * Unified method to calculate exponential weighted sum with size filtering
     */
    private int calculateExponentialWeightedSum(boolean isBid, int levelIndex, int minSize, int maxSize) {
        // Get the sorted map
        TreeMap<Integer, Integer> sortedMap = isBid ? super.getSortedBids() : super.getSortedAsks();
        
        // Fast path for empty map
        if (sortedMap == null || sortedMap.isEmpty()) {
            return 0;
        }
        
        // Get the decay factor for this level
        final double levelDecayFactor = decayFactors[levelIndex];
        
        // Get skip levels configuration once
        final int effectiveSkipLevels = super.getSkipLevels();
        
        // Find the reference price
        final int referencePrice = findReferencePrice(sortedMap, isBid, effectiveSkipLevels);
        
        double weightedSum = 0.0;
        int skipped = 0;
        
        // Single pass through the order book
        for (Map.Entry<Integer, Integer> entry : sortedMap.entrySet()) {
            final int price = entry.getKey();
            final int size = entry.getValue();
            
            // Skip non-positive sizes
            if (size <= 0) continue;
            
            // Skip levels if configured
            if (skipped < effectiveSkipLevels) {
                skipped++;
                continue;
            }
            
            // Apply size filtering
            if (size < minSize || size > maxSize) {
                continue;
            }
            
            // Calculate distance from reference price
            final int distance = Math.abs(price - referencePrice);
            
            // Skip entries too far from reference price 
            if (distance > MAX_DISTANCE) {
                continue;
            }
              // Calculate weight using decay factor
            double weight = fastPow(levelDecayFactor, distance);
            
            // Apply extra decay for higher levels at greater distances
            if (levelIndex >= EXTRA_DECAY_APPLY_LEVEL_INDEX_THRESHOLD && distance > EXTRA_DECAY_APPLY_DISTANCE_THRESHOLD) {
                weight *= Math.exp(EXTRA_DECAY_FACTOR_COMPONENT * (distance - EXTRA_DECAY_APPLY_DISTANCE_THRESHOLD));
            }
            
            // Add weighted size to sum
            weightedSum += size * weight;
        }
        
        // Round to nearest integer
        return (int)Math.round(weightedSum);
    }
    
    /**
     * Fast power function optimized for common cases
     */
    private double fastPow(double base, int exponent) {
        // Handle common cases directly
        if (exponent == 0) return 1.0;
        if (exponent == 1) return base;
        if (exponent == 2) return base * base;
        
        // For small exponents, use iteration
        if (exponent <= 8) {
            double result = 1.0;
            for (int i = 0; i < exponent; i++) {
                result *= base;
            }
            return result;
        }
        
        // For larger exponents, use binary exponentiation
        double result = 1.0;
        double basePower = base;
        
        while (exponent > 0) {
            if ((exponent & 1) == 1) { // If exponent is odd
                result *= basePower;
            }
            basePower *= basePower;  // Square the base
            exponent >>= 1;  // Divide exponent by 2
        }
        
        return result;
    }
    
    /**
     * Find the reference price for exponential calculations
     */
    private int findReferencePrice(TreeMap<Integer, Integer> map, boolean isBid, int skipLevels) {
        if (map.isEmpty()) return 0;
        
        // Fast path for no skip levels
        if (skipLevels <= 0) {
            return map.firstKey();
        }
        
        // Get n-th price level (after skipping)
        int skipped = 0;
        for (Integer price : map.keySet()) {
            if (skipped >= skipLevels) {
                return price;
            }
            skipped++;
        }
        
        // Fallback to first price if skip count exceeds available prices
        return map.firstKey();
    }
    
    /**
     * Updates price depth maps to track which depth level each price belongs to
     */
    private synchronized void updatePriceDepths() {
        // Clear existing depths
        bidPriceDepths.clear();
        askPriceDepths.clear();
        
        // Get the sorted maps
        TreeMap<Integer, Integer> sortedBids = getSortedBids();
        TreeMap<Integer, Integer> sortedAsks = getSortedAsks();

        // Process bids (highest first)
        int depth = 0;
        for (Map.Entry<Integer, Integer> entry : sortedBids.entrySet()) {
            if (entry.getValue() > 0) {  // Only count non-zero sizes
                bidPriceDepths.put(entry.getKey(), depth);
                lastUpdateNanos.put(entry.getKey(), currentTimestampNanos);
                depth++;
            }
        }
        
        // Process asks (lowest first)
        depth = 0;
        for (Map.Entry<Integer, Integer> entry : sortedAsks.entrySet()) {
            if (entry.getValue() > 0) {  // Only count non-zero sizes
                askPriceDepths.put(entry.getKey(), depth);
                lastUpdateNanos.put(entry.getKey(), currentTimestampNanos);
                depth++;
            }
        }
    }
    
    /**
     * Determines which level indices a price belongs to based on its depth
     */
    private int[] getLevelsForDepth(int depth) {
        int[] result = new int[maxLevels.length];
        int count = 0;
        
        // Skip levels as configured
        depth -= getSkipLevels();
        
        // If after skipping, the depth is negative, it's not in any level
        if (depth < 0) {
            return new int[0];
        }
        
        // Check each configured level
        for (int i = 0; i < maxLevels.length; i++) {
            if (depth < maxLevels[i]) {
                result[count++] = i;
            }
        }
        
        // Return only the valid levels
        return Arrays.copyOf(result, count);
    }
      /**
     * Calculates decay factors for all buffer levels
     */
    private void calculateDecayFactors(int[] maxLevels) {
        // Fill standard levels with the base decay factor
        for (int i = 0; i < Math.min(NUM_STANDARD_DECAY_LEVELS, decayFactors.length); i++) {
            decayFactors[i] = decayFactor;
            
            // Fine-tune for deep standard levels
            if (i == DEEP_STANDARD_LEVEL_INDEX && maxLevels[DEEP_STANDARD_LEVEL_INDEX] > DEEP_STANDARD_LEVEL_SIZE_THRESHOLD) {
                decayFactors[DEEP_STANDARD_LEVEL_INDEX] = Math.max(DEEP_STANDARD_LEVEL_DECAY_MIN, decayFactor - DEEP_STANDARD_LEVEL_DECAY_ADJUSTMENT);
            }
        }
        
        // Apply special QuotesDelta decay factors if needed
        if (decayFactors.length > 3) {
            decayFactors[3] = QUOTES_DELTA_DECAY_FACTOR_PRIMARY;
        }
        
        if (decayFactors.length > 4) {
            decayFactors[4] = QUOTES_DELTA_DECAY_FACTOR_SECONDARY;
        }
        
        if (decayFactors.length > 5) {
            decayFactors[5] = QUOTES_DELTA_DECAY_FACTOR_DEEP;
        }
          if (DEBUG_MODE) {
            StringBuilder logMsg = new StringBuilder("Decay factors calculated:");
            for (int i = 0; i < decayFactors.length; i++) {
                logMsg.append(" Level ").append(i).append(": ").append(String.format("%.2f", decayFactors[i]));
                if (i == DEEP_STANDARD_LEVEL_INDEX) { // Corresponds to the previous i == 2 logic for formatting
                    logMsg.append(" |");
                }
            }
            Log.info(logMsg.toString());
        }
    }
    
    /**
     * Sets the decay factor based on market type
     */
    private void setDecayFactorByMarketType() {
        switch (marketType) {
            case MARKET_ES:
                decayFactor = DECAY_FACTOR_ES;
                break;
            case MARKET_NQ:
                decayFactor = DECAY_FACTOR_NQ;
                break;
            case MARKET_BTC:
                decayFactor = DECAY_FACTOR_BTC;
                break;
            case MARKET_FOREX:
                decayFactor = DECAY_FACTOR_FOREX;
                break;
            case MARKET_EQUITIES:
                decayFactor = DECAY_FACTOR_EQUITIES;
                break;
            default:
                decayFactor = DECAY_FACTOR_GENERIC;
        }
        
        // Apply session adjustment if overnight
        if (isOvernightSession) {
            decayFactor -= OVERNIGHT_DECAY_ADJUSTMENT;
        }
    }
    
    /**
     * Implements SnapshotEndListener interface to directly receive snapshot completion events
     * This eliminates the need for manual synchronization with the indicator
     */
    @Override
    public void onSnapshotEnd() {
        // Update price depths first
        updatePriceDepths();
        
        // Reset all delta counters - no need to store snapshots anymore
        resetDeltas();
        
        // Log the initial state if in debug mode
        if (DEBUG_MODE) {
            int[] bidSums = getAllSizeSums(true, minSizeFilter, maxSizeFilter);
            int[] askSums = getAllSizeSums(false, minSizeFilter, maxSizeFilter);
            
            for (int i = 0; i < maxLevels.length; i++) {
                Log.info("Initial state Level " + (i+1) + " - Bid sum: " + bidSums[i] + ", Ask sum: " + askSums[i]);
            }
        }
        
        // Mark snapshot as complete
        snapshotCompleted = true;
        
        if (DEBUG_MODE) {
            Log.info("MultiLevelOrderBookExpwithQD received snapshot end event, now tracking deltas");
        }
    }
    
    /**
     * Set size filter parameters
     */
    public void setSizeFilter(int minSize, int maxSize) {
        if (minSize >= 0 && maxSize >= minSize) {
            this.minSizeFilter = minSize;
            this.maxSizeFilter = maxSize;
            
            if (DEBUG_MODE) {
                Log.info("Size filter set to: [" + minSize + "-" + maxSize + "]");
            }
        } else {
            Log.error("Invalid size filter parameters: min=" + minSize + ", max=" + maxSize);
        }
    }
    
    /**
     * Reset all delta counters - optimized to only reset levels of interest
     */
    public synchronized void resetDeltas() {
        // If we're only tracking specific levels, only handle those to save time
        if (QD_LastLevelsToProcess > 0 && QD_LastLevelsToProcess < maxLevels.length) {
            // Only reset the levels we're actually tracking
            int startIdx = Math.max(0, maxLevels.length - QD_LastLevelsToProcess);
            for (int i = startIdx; i < maxLevels.length; i++) {
                // Reset current values
                bidDeltas[i] = 0;
                askDeltas[i] = 0;
            }
            
            if (DEBUG_MODE) {
                Log.info("Deltas reset for levels " + (startIdx+1) + " to " + maxLevels.length + 
                        " (only tracking last " + QD_LastLevelsToProcess + " levels)");
            }
        } else {
            // Reset all levels if we're tracking everything
            Arrays.fill(bidDeltas, 0);
            Arrays.fill(askDeltas, 0);
            
            if (DEBUG_MODE) {
                Log.info("All deltas reset");
            }
        }
    }
    
    /**
     * Get the net raw delta (bid delta - ask delta) for a level
     */
    public int getRawNetDelta(int levelIndex) {
        if (levelIndex >= 0 && levelIndex < maxLevels.length) {
            return bidDeltas[levelIndex] - askDeltas[levelIndex];
        } else {
            if (DEBUG_MODE) {
                Log.error("Invalid level index: " + levelIndex);
            }
            return 0;
        }
    }

    /**
     * Get the raw bid delta for a level
     */
    public int getRawBidDelta(int levelIndex) {
        if (levelIndex >= 0 && levelIndex < maxLevels.length) {
            return bidDeltas[levelIndex];
        } else {
            if (DEBUG_MODE) {
                Log.error("Invalid level index: " + levelIndex);
            }
            return 0;
        }
    }

    /**
     * Get the raw ask delta for a level
     */
    public int getRawAskDelta(int levelIndex) {
        if (levelIndex >= 0 && levelIndex < maxLevels.length) {
            return askDeltas[levelIndex];
        } else {
            if (DEBUG_MODE) {
                Log.error("Invalid level index: " + levelIndex);
            }
            return 0;
        }
    }

    /**
     * Get all raw net deltas in one operation
     */
    public int[] getAllRawNetDeltas() {
        int[] result;
        int levelsToProcess = QD_LastLevelsToProcess > 0 ? 
            Math.min(QD_LastLevelsToProcess, maxLevels.length) : maxLevels.length;
            
        synchronized(this) {
            result = new int[levelsToProcess];
            for (int i = 0; i < levelsToProcess; i++) {
                // Calculate indexes based on whether we're processing all levels or just the last N
                int sourceIndex = QD_LastLevelsToProcess > 0 ? 
                    (maxLevels.length - levelsToProcess + i) : i;
                    
                // Ensure sourceIndex is valid and within bounds
                if (sourceIndex >= 0 && sourceIndex < bidDeltas.length) {
                    result[i] = bidDeltas[sourceIndex] - askDeltas[sourceIndex];
                }
            }
        }
        
        return result;
    }

    /**
     * Get all raw bid deltas
     */
    public int[] getAllRawBidDeltas() {
        int levelsToProcess = QD_LastLevelsToProcess > 0 ? 
            Math.min(QD_LastLevelsToProcess, maxLevels.length) : maxLevels.length;
            
        synchronized(this) {
            int[] result = new int[levelsToProcess];
            for (int i = 0; i < levelsToProcess; i++) {
                // Calculate indexes based on whether we're processing all levels or just the last N
                int sourceIndex = QD_LastLevelsToProcess > 0 ? 
                    (maxLevels.length - levelsToProcess + i) : i;
                    
                // Ensure sourceIndex is valid and within bounds
                if (sourceIndex >= 0 && sourceIndex < bidDeltas.length) {
                    result[i] = bidDeltas[sourceIndex];
                }
            }
            return result;
        }
    }

    /**
     * Get all raw ask deltas
     */
    public int[] getAllRawAskDeltas() {
        int levelsToProcess = QD_LastLevelsToProcess > 0 ? 
            Math.min(QD_LastLevelsToProcess, maxLevels.length) : maxLevels.length;
            
        synchronized(this) {
            int[] result = new int[levelsToProcess];
            for (int i = 0; i < levelsToProcess; i++) {
                // Calculate indexes based on whether we're processing all levels or just the last N
                int sourceIndex = QD_LastLevelsToProcess > 0 ? 
                    (maxLevels.length - levelsToProcess + i) : i;
                    
                // Ensure sourceIndex is valid and within bounds
                if (sourceIndex >= 0 && sourceIndex < askDeltas.length) {
                    result[i] = askDeltas[sourceIndex];
                }
            }
            return result;
        }
    }
    
    /**
     * Get all size sums for both sides
     */
    public int[][] getAllSizeSumsForBidAsk() {
        int[][] result = new int[2][];
        result[0] = getAllSizeSums(true, minSizeFilter, maxSizeFilter);
        result[1] = getAllSizeSums(false, minSizeFilter, maxSizeFilter);
        return result;
    }
    
    /**
     * Calculate all exponentially weighted sums in a single pass through the order book.
     * This optimized method avoids multiple traversals of the book when getting sums for all levels.
     * Results are identical to calling getSizeSum() for each level individually.
     * 
     * @param isBid true for bid side, false for ask side
     * @param minSize minimum order size to include
     * @param maxSize maximum order size to include
     * @return array of exponentially weighted sums for each level
     */
    public int[] getAllExponentialSums(boolean isBid, int minSize, int maxSize) {
        int[] results = new int[maxLevels.length];
        
        // Get the sorted map
        TreeMap<Integer, Integer> sortedMap = isBid ? super.getSortedBids() : super.getSortedAsks();
        
        // Fast path for empty map
        if (sortedMap == null || sortedMap.isEmpty()) {
            return results; // Return array of zeros
        }
        
        // Normalize filter parameters once
        final int effectiveMinSize = Math.max(0, minSize);
        final int effectiveMaxSize = Math.max(effectiveMinSize, maxSize);
        
        // Get skip levels configuration once
        final int effectiveSkipLevels = super.getSkipLevels();
        
        // Find the reference price
        final int referencePrice = findReferencePrice(sortedMap, isBid, effectiveSkipLevels);
        
        // Pre-calculate weights for each level's decay factor to avoid redundant computations
        double[][] weightCache = new double[maxLevels.length][MAX_DISTANCE + 1];
        boolean[] useExtraDecay = new boolean[maxLevels.length];
        
        for (int levelIndex = 0; levelIndex < maxLevels.length; levelIndex++) {
            final double levelDecayFactor = decayFactors[levelIndex];
            useExtraDecay[levelIndex] = (levelIndex >= 2); // Extra decay for level 3+
            
            // Pre-calculate weights for common distances
            for (int distance = 0; distance <= MAX_DISTANCE; distance++) {
                // Get base weight using decay factor
                weightCache[levelIndex][distance] = fastPow(levelDecayFactor, distance);
            }
        }
        
        // Initialize working arrays for weighted sums
        double[] weightedSums = new double[maxLevels.length];
        
        // Single pass through the order book
        int skipped = 0;
        for (Map.Entry<Integer, Integer> entry : sortedMap.entrySet()) {
            final int price = entry.getKey();
            final int size = entry.getValue();
            
            // Skip non-positive sizes
            if (size <= 0) continue;
            
            // Skip levels if configured
            if (skipped < effectiveSkipLevels) {
                skipped++;
                continue;
            }
            
            // Apply size filtering
            if (size < effectiveMinSize || size > effectiveMaxSize) {
                continue;
            }
              // Calculate distance from reference price
            final int distance = Math.abs(price - referencePrice);
            
            // Skip entries too far from reference price
            if (distance > MAX_DISTANCE) {
                continue;
            }
            
            // Update weighted sum for each level
            for (int levelIndex = 0; levelIndex < maxLevels.length; levelIndex++) {
                // Get pre-calculated weight for this distance and level
                double weight = weightCache[levelIndex][distance];
                
                // Apply extra decay for higher levels at greater distances
                if (useExtraDecay[levelIndex] && distance > EXTRA_DECAY_APPLY_DISTANCE_THRESHOLD) {
                    weight *= Math.exp(EXTRA_DECAY_FACTOR_COMPONENT * (distance - EXTRA_DECAY_APPLY_DISTANCE_THRESHOLD));
                }
                
                // Add weighted size to sum
                weightedSums[levelIndex] += size * weight;
            }
        }
        
        // Convert weighted sums to integers
        for (int i = 0; i < maxLevels.length; i++) {
            results[i] = (int) Math.round(weightedSums[i]);
        }
        
        return results;
    }
    
    /**
     * Get all exponential sums with default size filters
     * 
     * @param isBid true for bid side, false for ask side
     * @return array of exponentially weighted sums for each level
     */
    public int[] getAllExponentialSums(boolean isBid) {
        return getAllExponentialSums(isBid, minSizeFilter, maxSizeFilter);
    }
    
    /**
     * Get all exponential weighted sums for both sides
     * 
     * @param minSize minimum order size to include
     * @param maxSize maximum order size to include
     * @return 2D array with [bidSums, askSums]
     */
    public int[][] getAllExponentialSumsForBidAsk(int minSize, int maxSize) {
        int[][] result = new int[2][];
        result[0] = getAllExponentialSums(true, minSize, maxSize);
        result[1] = getAllExponentialSums(false, minSize, maxSize);
        return result;
    }
    
    /**
     * Get all exponential weighted sums for both sides with default size filters
     * 
     * @return 2D array with [bidSums, askSums]
     */
    public int[][] getAllExponentialSumsForBidAsk() {
        return getAllExponentialSumsForBidAsk(minSizeFilter, maxSizeFilter);
    }
    
    /**
     * Check if snapshot is completed
     */
    public boolean isSnapshotCompleted() {
        return snapshotCompleted;
    }
    
    /**
     * Set session type
     */
    public void setSessionType(boolean isOvernight) {
        if (this.isOvernightSession != isOvernight) {
            this.isOvernightSession = isOvernight;
            setDecayFactorByMarketType();
            calculateDecayFactors(maxLevels);
        }
    }
    
    /**
     * Set market type
     */
    public void setMarketType(int marketType) {
        if (this.marketType != marketType) {
            this.marketType = marketType;
            setDecayFactorByMarketType();
            calculateDecayFactors(maxLevels);
        }
    }
    
    /**
     * Set custom decay factor
     */
    public void setDecayFactor(double decayFactor) {
        if (decayFactor > 0.0 && decayFactor <= 1.0) {
            this.decayFactor = decayFactor;
            calculateDecayFactors(maxLevels);
        }
    }
    
    /**
     * Set custom decay factors for QuotesDelta levels
     */
    public void setQuotesDeltaDecayFactors(double primaryDecay, double secondaryDecay, double deepDecay) {
        if (decayFactors != null) {
            if (decayFactors.length > 3) {
                decayFactors[3] = primaryDecay;
            }
            
            if (decayFactors.length > 4) {
                decayFactors[4] = secondaryDecay;
            }
            
            if (decayFactors.length > 5) {
                decayFactors[5] = deepDecay;
            }
            
            if (DEBUG_MODE) {
                Log.info("Custom QuotesDelta decay factors set: " + 
                        primaryDecay + ", " + secondaryDecay + ", " + deepDecay);
            }
        }
    }
    
    /**
     * Get current decay factor
     */
    public double getDecayFactor() {
        return decayFactor;
    }
    
    /**
     * Handle skip levels change
     */
    @Override
    public void setSkipLevels(int skipLevels) {
        super.setSkipLevels(skipLevels);
        updatePriceDepths();
    }

    /**
     * Set the maximum number of levels to process
     * @param QDmaxLevelsToProcess maximum number of levels to process (0 means all levels)
     */
    public void setQDLastLevelsToProcess(int QDmaxLevelsToProcess) {
        if (QDmaxLevelsToProcess >= 0) {
            this.QD_LastLevelsToProcess = QDmaxLevelsToProcess;
            
            if (DEBUG_MODE) {
                Log.info("Set QD_LastLevelsToProcess: " + 
                    (QDmaxLevelsToProcess == 0 ? "All levels" : QDmaxLevelsToProcess + " levels") + 
                    " (will only track deltas for the last " + QDmaxLevelsToProcess + " levels)");
            }
        } else {
            if (DEBUG_MODE) {
                Log.error("Invalid QD_LastLevelsToProcess value: " + QDmaxLevelsToProcess);
            }
        }
    }
    
    /**
     * Get the maximum number of levels to process
     * @return maximum number of levels to process (0 means all levels)
     */
    public int getMaxLevelsToProcess() {
        return QD_LastLevelsToProcess;
    }
    
    /**
     * Get raw size sum directly from parent class without applying exponential weighting
     * This method already respects skipLevels setting from the parent class
     * 
     * @param isBid true for bid side, false for ask side
     * @param levelIndex index of level to get sum for
     * @return raw size sum without exponential weighting
     */
    public int getRawSizeSum(boolean isBid, int levelIndex) {
        return super.getSizeSum(isBid, levelIndex);
    }
    
    /**
     * Get raw size sum directly from parent class without applying exponential weighting
     * This method already respects skipLevels setting from the parent class
     * 
     * @param isBid true for bid side, false for ask side
     * @param levelIndex index of level to get sum for
     * @param minSize minimum size to include
     * @param maxSize maximum size to include
     * @return raw size sum without exponential weighting
     */
    public int getRawSizeSum(boolean isBid, int levelIndex, int minSize, int maxSize) {
        return super.getSizeSum(isBid, levelIndex, minSize, maxSize);
    }
    
    /**
     * Get all raw size sums for a side directly from parent class
     * This method respects skipLevels setting from the parent class
     *
     * @param isBid true for bid side, false for ask side
     * @param minSize minimum size to include
     * @param maxSize maximum size to include  
     * @return array of raw size sums for each level
     */
    public int[] getAllRawSizeSums(boolean isBid, int minSize, int maxSize) {
        return super.getAllSizeSums(isBid, minSize, maxSize);
    }
    
    
    
    /**
     * Get liquidity stats (bid, ask, absolute imbalance, relative imbalance) for a level
     * 
     * @param levelIndex index of the level
     * @param minSize minimum size to include
     * @param maxSize maximum size to include
     * @return array containing [bidSum, askSum, absoluteImbalance, relativeImbalance*10000]
     *         where relative imbalance is scaled by 10000 to preserve 4 decimal places as an int
     */
    public int[] getLiquidityStats(int levelIndex, int minSize, int maxSize) {
        if (levelIndex < 0 || levelIndex >= maxLevels.length) {
            return new int[] {0, 0, 0, 0};
        }
        
        int bidSum = getSizeSum(true, levelIndex, minSize, maxSize);
        int askSum = getSizeSum(false, levelIndex, minSize, maxSize);
        int imbalance = bidSum - askSum;
        
        // Calculate relative imbalance (scaled by 10000 to preserve precision as an int)
        int relImbalanceScaledx10000;
        if (bidSum + askSum == 0) {
            relImbalanceScaledx10000 = 0; // Avoid division by zero
        } else {
            relImbalanceScaledx10000 = (int)Math.round((double)imbalance / (bidSum + askSum) * 10000);
        }
        
        return new int[] {bidSum, askSum, imbalance, relImbalanceScaledx10000};
    }
    
    /**
     * Get liquidity stats with default size filters
     * 
     * @param levelIndex index of the level
     * @return array containing [bidSum, askSum, absoluteImbalance, relativeImbalance*10000]
     */
    public int[] getLiquidityStats(int levelIndex) {
        return getLiquidityStats(levelIndex, minSizeFilter, maxSizeFilter);
    }
    
    /**
     * Get comprehensive liquidity stats including imbalance significance indicators with separate bid/ask thresholds
     * 
     * @param levelIndex index of the level
     * @param relBidImbalanceThreshold minimum relative bid imbalance to be considered significant (positive value)
     * @param relAskImbalanceThreshold minimum relative ask imbalance to be considered significant (positive value)
     * @param minAbsoluteImbalance minimum absolute imbalance to be considered significant
     * @return array containing [bidSum, askSum, absoluteImbalance, relativeImbalance*10000, 
     *                          isSignificant, direction]
     *         where isSignificant is 1 if significant, 0 if not
     *         and direction is 1 (bid), -1 (ask), or 0 (balanced)
     */
    public int[] getComprehensiveLiquidityStats(int levelIndex, double relBidImbalanceThreshold, 
                                               double relAskImbalanceThreshold, int minAbsoluteImbalance) {
        return getComprehensiveLiquidityStats(levelIndex, minSizeFilter, maxSizeFilter, 
                                             relBidImbalanceThreshold, relAskImbalanceThreshold, minAbsoluteImbalance);
    }
    
    /**
     * Get comprehensive liquidity stats with custom size filters and separate bid/ask thresholds
     * 
     * @param levelIndex index of the level
     * @param minSize minimum size to include
     * @param maxSize maximum size to include
     * @param relBidImbalanceThreshold minimum relative bid imbalance to be considered significant (positive value)
     * @param relAskImbalanceThreshold minimum relative ask imbalance to be considered significant (positive value)
     * @param minAbsoluteImbalance minimum absolute imbalance to be considered significant
     * @return array containing [bidSum, askSum, absoluteImbalance, relativeImbalance*10000, 
     *                          isSignificant, direction]
     */
    public int[] getComprehensiveLiquidityStats(int levelIndex, int minSize, int maxSize, 
                                               double relBidImbalanceThreshold, double relAskImbalanceThreshold, 
                                               int minAbsoluteImbalance) {
        if (levelIndex < 0 || levelIndex >= maxLevels.length) {
            return new int[] {0, 0, 0, 0, 0, 0};
        }
        
        // Get basic stats first
        int bidSum = getSizeSum(true, levelIndex, minSize, maxSize);
        int askSum = getSizeSum(false, levelIndex, minSize, maxSize);
        int imbalance = bidSum - askSum;
        
        // Calculate relative imbalance (scaled by 10000 to preserve precision as an int)
        int relImbalanceScaledx10000;
        double relImbalance;
        if (bidSum + askSum == 0) {
            relImbalance = 0.0;
            relImbalanceScaledx10000 = 0; // Avoid division by zero
        } else {
            relImbalance = (double)imbalance / (bidSum + askSum);
            relImbalanceScaledx10000 = (int)Math.round(relImbalance * 10000);
        }
        
        // Determine if imbalance is significant - use different thresholds for bid vs ask
        int isSignificant = 0;
        if (Math.abs(imbalance) >= minAbsoluteImbalance) {
            if (imbalance > 0 && relImbalance >= relBidImbalanceThreshold) {
                // Bid imbalance is significant (bids > asks)
                isSignificant = 1;
            } else if (imbalance < 0 && relImbalance <= -relAskImbalanceThreshold) {
                // Ask imbalance is significant (asks > bids)
                isSignificant = 1;
            }
        }
        
        // Determine direction
        int direction = 0;
        if (isSignificant == 1) {
            direction = Integer.compare(imbalance, 0);
        }
        
        return new int[] {bidSum, askSum, imbalance, relImbalanceScaledx10000, isSignificant, direction};
    }
    
    /**
     * Check if there's a significant imbalance at a specific level using separate bid/ask thresholds
     * 
     * @param levelIndex index of the level
     * @param relBidImbalanceThreshold minimum relative bid imbalance to be considered significant (positive value)
     * @param relAskImbalanceThreshold minimum relative ask imbalance to be considered significant (positive value)
     * @param minAbsoluteImbalance minimum absolute imbalance to be considered significant
     * @return true if imbalance is significant
     */
    public boolean hasSignificantImbalance(int levelIndex, double relBidImbalanceThreshold, 
                                          double relAskImbalanceThreshold, int minAbsoluteImbalance) {
        if (levelIndex < 0 || levelIndex >= maxLevels.length) {
            return false;
        }
        
        // Use the comprehensive stats method to avoid duplicate calculations
        int[] stats = getComprehensiveLiquidityStats(levelIndex, minSizeFilter, maxSizeFilter, 
                                                    relBidImbalanceThreshold, relAskImbalanceThreshold, 
                                                    minAbsoluteImbalance);
        return stats[4] == 1; // isSignificant flag
    }
    
    /**
     * Get imbalance direction using separate bid/ask thresholds
     * 
     * @param levelIndex index of the level
     * @param relBidImbalanceThreshold minimum relative bid imbalance to be considered significant (positive value)
     * @param relAskImbalanceThreshold minimum relative ask imbalance to be considered significant (positive value)
     * @param minAbsoluteImbalance minimum absolute imbalance to be considered significant
     * @return imbalance direction: 1 (bid), -1 (ask), 0 (balanced)
     */
    public int getImbalanceDirection(int levelIndex, double relBidImbalanceThreshold, 
                                    double relAskImbalanceThreshold, int minAbsoluteImbalance) {
        if (levelIndex < 0 || levelIndex >= maxLevels.length) {
            return 0;
        }
        
        // Use the comprehensive stats method to avoid duplicate calculations
        int[] stats = getComprehensiveLiquidityStats(levelIndex, minSizeFilter, maxSizeFilter, 
                                                    relBidImbalanceThreshold, relAskImbalanceThreshold, 
                                                    minAbsoluteImbalance);
        return stats[5]; // direction flag
    }
    
    /**
     * Get comprehensive stats for all levels using separate bid/ask thresholds
     * 
     * @param relBidImbalanceThreshold minimum relative bid imbalance to be considered significant (positive value)
     * @param relAskImbalanceThreshold minimum relative ask imbalance to be considered significant (positive value) 
     * @param minAbsoluteImbalance minimum absolute imbalance to be considered significant
     * @return 2D array where each row contains [bidSum, askSum, absoluteImbalance, relativeImbalance*10000,
     *                                          isSignificant, direction]
     */
    public int[][] getAllComprehensiveLiquidityStats(double relBidImbalanceThreshold, 
                                                   double relAskImbalanceThreshold, 
                                                   int minAbsoluteImbalance) {
        return getAllComprehensiveLiquidityStats(minSizeFilter, maxSizeFilter, 
                                               relBidImbalanceThreshold, relAskImbalanceThreshold, 
                                               minAbsoluteImbalance);
    }
    
    /**
     * Get comprehensive stats for all levels with custom filters and separate bid/ask thresholds
     * 
     * @param minSize minimum size to include
     * @param maxSize maximum size to include
     * @param relBidImbalanceThreshold minimum relative bid imbalance to be considered significant (positive value)
     * @param relAskImbalanceThreshold minimum relative ask imbalance to be considered significant (positive value)
     * @param minAbsoluteImbalance minimum absolute imbalance to be considered significant
     * @return 2D array of comprehensive stats for each level
     */    public int[][] getAllComprehensiveLiquidityStats(int minSize, int maxSize, 
                                                   double relBidImbalanceThreshold, 
                                                   double relAskImbalanceThreshold, 
                                                   int minAbsoluteImbalance) {
        int[][] result = new int[maxLevels.length][6]; // 6 values per level
        
        // Get all sums at once for efficiency
        int[] bidSums = getAllExponentialSums(true, minSize, maxSize);
        int[] askSums = getAllExponentialSums(false, minSize, maxSize);
        
        // Note: Results are generated for levels 0 up to (maxLevels.length - QD_LastLevelsToProcess - 1).
        // Entries in the 'result' array for indices from (maxLevels.length - QD_LastLevelsToProcess)
        // to (maxLevels.length - 1) will retain their default zero values.
        // This matches the existing behavior.
        // Calculate stats for each level
        for (int i = 0; i < maxLevels.length-QD_LastLevelsToProcess; i++) {
            int bidSum = bidSums[i];
            int askSum = askSums[i];
            int imbalance = bidSum - askSum;
            
            // Calculate relative imbalance
            int relImbalanceScaledx10000;
            double relImbalance;
            if (bidSum + askSum == 0) {
                relImbalance = 0.0;
                relImbalanceScaledx10000 = 0;
            } else {
                relImbalance = (double)imbalance / (bidSum + askSum);
                relImbalanceScaledx10000 = (int)Math.round(relImbalance * 10000);
            }
            
            // Determine if imbalance is significant using separate thresholds for bid/ask
            int isSignificant = 0;
            if (Math.abs(imbalance) >= minAbsoluteImbalance) {
                if (imbalance > 0 && relImbalance >= relBidImbalanceThreshold) {
                    // Bid imbalance is significant (bids > asks)
                    isSignificant = 1;
                } else if (imbalance < 0 && relImbalance <= -relAskImbalanceThreshold) {
                    // Ask imbalance is significant (asks > bids)
                    isSignificant = 1;
                }
            }
            
            // Determine direction
            int direction = 0;
            if (isSignificant == 1) {
                direction = Integer.compare(imbalance, 0);
            }
            
            result[i] = new int[] {bidSum, askSum, imbalance, relImbalanceScaledx10000, isSignificant, direction};
        }
        
        return result;
    }
    
    /**
     * Get comprehensive stats for all levels
     * 
     * @param minImbalanceRatio minimum relative imbalance to be considered significant
     * @param minAbsoluteImbalance minimum absolute imbalance to be considered significant
     * @return 2D array where each row contains [bidSum, askSum, absoluteImbalance, relativeImbalance*10000,
     *                                          isSignificant, direction]
     */
    public int[][] getAllComprehensiveLiquidityStats(double minImbalanceRatio, int minAbsoluteImbalance) {
        return getAllComprehensiveLiquidityStats(minSizeFilter, maxSizeFilter, minImbalanceRatio, minAbsoluteImbalance);
    }
    
    /**
     * Get comprehensive stats for all levels with custom filters
     * 
     * @param minSize minimum size to include
     * @param maxSize maximum size to include
     * @param minImbalanceRatio minimum relative imbalance to be considered significant
     * @param minAbsoluteImbalance minimum absolute imbalance to be considered significant
     * @return 2D array of comprehensive stats for each level
     */    public int[][] getAllComprehensiveLiquidityStats(int minSize, int maxSize, 
                                                   double minImbalanceRatio, int minAbsoluteImbalance) {
        int[][] result = new int[maxLevels.length][6]; // 6 values per level
        
        // Get all sums at once for efficiency
        int[] bidSums = getAllExponentialSums(true, minSize, maxSize);
        int[] askSums = getAllExponentialSums(false, minSize, maxSize);
        
        // Note: Results are generated for levels 0 up to (maxLevels.length - QD_LastLevelsToProcess - 1).
        // Entries in the 'result' array for indices from (maxLevels.length - QD_LastLevelsToProcess)
        // to (maxLevels.length - 1) will retain their default zero values.
        // This matches the existing behavior.
        // Calculate stats for each level
        for (int i = 0; i < maxLevels.length-QD_LastLevelsToProcess; i++) {
            int bidSum = bidSums[i];
            int askSum = askSums[i];
            int imbalance = bidSum - askSum;
            
            // Calculate relative imbalance
            int relImbalanceScaledx10000;
            double relImbalance;
            if (bidSum + askSum == 0) {
                relImbalance = 0.0;
                relImbalanceScaledx10000 = 0;
            } else {
                relImbalance = (double)imbalance / (bidSum + askSum);
                relImbalanceScaledx10000 = (int)Math.round(relImbalance * 10000);
            }
            
            // Determine if imbalance is significant
            int isSignificant = 0;
            if (Math.abs(imbalance) >= minAbsoluteImbalance && Math.abs(relImbalance) >= minImbalanceRatio) {
                isSignificant = 1;
            }
            
            // Determine direction
            int direction = 0;
            if (isSignificant == 1) {
                direction = Integer.compare(imbalance, 0);
            }
            
            result[i] = new int[] {bidSum, askSum, imbalance, relImbalanceScaledx10000, isSignificant, direction};
        }
        
        return result;
    }
    
    /**
     * Get liquidity stats for all levels at once
     * 
     * @param minSize minimum size to include
     * @param maxSize maximum size to include
     * @return 2D array where each row contains [bidSum, askSum, absoluteImbalance, relativeImbalance*10000]
     *         for a level
     */    public int[][] getAllLiquidityStats(int minSize, int maxSize) {
        int[][] result = new int[maxLevels.length][4]; // 4 values per level
        
        // Get all sums at once for efficiency
        int[] bidSums = getAllExponentialSums(true, minSize, maxSize);
        int[] askSums = getAllExponentialSums(false, minSize, maxSize);
        
        // Note: Results are generated for levels 0 up to (maxLevels.length - QD_LastLevelsToProcess - 1).
        // Entries in the 'result' array for indices from (maxLevels.length - QD_LastLevelsToProcess)
        // to (maxLevels.length - 1) will retain their default zero values.
        // This matches the existing behavior.
        // Calculate stats for each level
        for (int i = 0; i < maxLevels.length-QD_LastLevelsToProcess; i++) {
            int bidSum = bidSums[i];
            int askSum = askSums[i];
            int imbalance = bidSum - askSum;
            
            // Calculate relative imbalance (scaled by 10000 to preserve precision as an int)
            int relImbalanceScaledx10000;
            if (bidSum + askSum == 0) {
                relImbalanceScaledx10000 = 0; // Avoid division by zero
            } else {
                relImbalanceScaledx10000 = (int)Math.round((double)imbalance / (bidSum + askSum) * 10000);
            }
            
            result[i] = new int[] {bidSum, askSum, imbalance, relImbalanceScaledx10000};
        }
        
        return result;
    }
    
    /**
     * Get liquidity stats for all levels with default size filters
     * 
     * @return 2D array of liquidity stats for each level
     */
    public int[][] getAllLiquidityStats() {
        return getAllLiquidityStats(minSizeFilter, maxSizeFilter);
    }

    private long currentTimestampNanos;

    
    @Override
    public void onTimestamp(long timestamp) {
        currentTimestampNanos = timestamp;
    }

    /* ===== Liquidity cluster analyzer nested class ===== */
    /**
     * LiquidityClusterAnalyzer
     * (Full implementation with time‑decay weighting + crossover filter)
     * NOTE: This is the exact class previously placed in the canvas.
     */
    public class LiquidityClusterAnalyzer {

        // ----- parameters -----
        private final int clusterRadiusTicks;
        private final long minClusterCumSize;
        private final int maxTargetsPerSide;
        private final double decayFactor;
        private final long crossoverWindowNanos;
        private final double crossoverThreshold;

        private final java.util.NavigableMap<Integer, Integer> bidDepth;
        private final java.util.NavigableMap<Integer, Integer> askDepth;
        private final java.util.Map<Integer, Long> lastUpdateNanos;
        private final java.util.Map<Integer, java.util.Deque<Sample>> clusterHistory = new java.util.HashMap<>();

        private class Sample {
            final long t;
            final long s;

            Sample(long t, long s) {
                this.t = t;
                this.s = s;
            }
        }

        public class LiquidityCluster {
            public final int startPrice, endPrice, centerPrice;
            public final long cumSize, peakLevelSize;

            LiquidityCluster(int st, int en, int c, long cu, long pk) {
                startPrice = st;
                endPrice = en;
                centerPrice = c;
                cumSize = cu;
                peakLevelSize = pk;
            }
        }

        public LiquidityClusterAnalyzer(java.util.NavigableMap<Integer, Integer> bidDepth,
                java.util.NavigableMap<Integer, Integer> askDepth,
                java.util.Map<Integer, Long> lastUpdateNanos,
                int clusterRadiusTicks, long minClusterCumSize, int maxTargetsPerSide,
                double decayFactor, long crossoverWindowMillis, double crossoverThreshold) {
            this.bidDepth = bidDepth;
            this.askDepth = askDepth;
            this.lastUpdateNanos = lastUpdateNanos;
            this.clusterRadiusTicks = clusterRadiusTicks;
            this.minClusterCumSize = minClusterCumSize;
            this.maxTargetsPerSide = maxTargetsPerSide;
            this.decayFactor = decayFactor;
            this.crossoverWindowNanos = crossoverWindowMillis * 1_000_000L;
            this.crossoverThreshold = crossoverThreshold;
        }

        // ---- public API ----
        public java.util.List<LiquidityCluster> getSellTargets(int bestAsk, long ts) {
            return compute(false, bestAsk, ts);
        }

        public java.util.List<LiquidityCluster> getBuyTargets(int bestBid, long ts) {
            return compute(true, bestBid, ts);
        }

        public java.util.Optional<LiquidityCluster> getNearestTarget(boolean isLong, int ref, long ts) {
            java.util.List<LiquidityCluster> lst = isLong ? getSellTargets(ref, ts) : getBuyTargets(ref, ts);
            return lst.isEmpty() ? java.util.Optional.empty() : java.util.Optional.of(lst.get(0));
        }        private synchronized java.util.List<LiquidityCluster> compute(boolean bidSide, int ref, long now) {
            java.util.NavigableMap<Integer, Integer> book = bidSide ? bidDepth : askDepth;
            book = new java.util.TreeMap<>(book); // Defensive copy
            int dir = bidSide ? -1 : 1;
            int scan = LIQUIDITY_SCAN_DEPTH_TICKS;
            long windowSum = 0;
            java.util.Deque<Long> w = new java.util.ArrayDeque<>();
            java.util.Map<Integer, Long> band = new java.util.HashMap<>();
            int p = ref + dir;
            for (int i = 0; i < scan; i++, p += dir) {
                Integer lvlInt = book.get(p);
                long lvl = lvlInt == null ? 0L : lvlInt.longValue();
                long upd = lastUpdateNanos.getOrDefault(p, now);
                double age = (now - upd) / 1e9;
                long dec = (long) (lvl * Math.pow(decayFactor, age));
                w.addLast(dec);
                windowSum += dec;
                if (w.size() > clusterRadiusTicks * 2 + 1)
                    windowSum -= w.removeFirst();
                band.put(p, windowSum);
            }
            java.util.List<LiquidityCluster> out = new java.util.ArrayList<>();
            for (java.util.Map.Entry<Integer, Long> e : band.entrySet()) {
                int price = e.getKey();
                long s = e.getValue();
                if (s < minClusterCumSize)
                    continue;
                long l = band.getOrDefault(price - dir, 0L), r = band.getOrDefault(price + dir, 0L);
                if (s >= l && s >= r) {
                    int st = price, en = price;
                    while (band.getOrDefault(st - dir, 0L) >= minClusterCumSize / 2)
                        st -= dir;
                    while (band.getOrDefault(en + dir, 0L) >= minClusterCumSize / 2)
                        en += dir;
                    long cum = 0, peak = 0, wSum = 0;
                    for (int lvl = st; lvl <= en; lvl += dir) {
                        Integer lvInt = book.get(lvl);
                        long lv = lvInt == null ? 0L : lvInt.longValue();
                        long up = lastUpdateNanos.getOrDefault(lvl, now);
                        double ag = (now - up) / 1e9;
                        long d = (long) (lv * Math.pow(decayFactor, ag));
                        cum += d;
                        peak = Math.max(peak, d);
                        wSum += d * lvl;
                    }
                    int center = (int) (wSum / Math.max(1, cum));
                    if (!pass(center, cum, now))
                        continue;
                    out.add(new LiquidityCluster(st, en, center, cum, peak));
                }
            }
            out.sort(java.util.Comparator.comparingInt(c -> Math.abs(c.centerPrice - ref)));
            return out.size() > maxTargetsPerSide ? out.subList(0, maxTargetsPerSide) : out;
        }

        private boolean pass(int center, long cum, long now) {
            java.util.Deque<Sample> h = clusterHistory.computeIfAbsent(center, k -> new java.util.ArrayDeque<>());
            h.addLast(new Sample(now, cum));
            while (!h.isEmpty() && now - h.peekFirst().t > crossoverWindowNanos)
                h.removeFirst();
            long peak = 0;
            for (Sample s : h)
                peak = Math.max(peak, s.s);
            return cum >= crossoverThreshold * peak;
        }
    }

    // --- wrapper helpers (replace System.nanoTime) ---
    public java.util.List<LiquidityClusterAnalyzer.LiquidityCluster> getSellTargets() {
        return liquidityClusterAnalyzer.getSellTargets(getBestAskPrice(), currentTimestampNanos);
    }
    public java.util.List<LiquidityClusterAnalyzer.LiquidityCluster> getBuyTargets() {
        return liquidityClusterAnalyzer.getBuyTargets(getBestBidPrice(), currentTimestampNanos);
    }
    public java.util.Optional<LiquidityClusterAnalyzer.LiquidityCluster> getNearestTarget(boolean isLongSide) {
        int ref = isLongSide ? getBestAskPrice() : getBestBidPrice();
        return liquidityClusterAnalyzer.getNearestTarget(isLongSide, ref, currentTimestampNanos);
    }

    /**
     * Returns the best (lowest) ask price, or Integer.MAX_VALUE if no asks exist.
     */
    public int getBestAskPrice() {
        TreeMap<Integer, Integer> asks = getSortedAsks();
        return asks.isEmpty() ? Integer.MAX_VALUE : asks.firstKey();
    }

    /**
     * Returns the best (highest) bid price, or Integer.MIN_VALUE if no bids exist.
     */
    public int getBestBidPrice() {
        TreeMap<Integer, Integer> bids = getSortedBids();
        return bids.isEmpty() ? Integer.MIN_VALUE : bids.firstKey();
    }
}