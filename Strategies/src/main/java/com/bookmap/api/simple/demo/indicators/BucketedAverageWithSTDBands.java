package com.bookmap.api.simple.demo.indicators;

import java.util.TreeMap;
import java.util.Map;
import java.util.HashMap;
import java.util.Deque;
import java.util.ArrayDeque;
import java.util.Iterator; // Add missing import for Iterator
import com.bookmap.api.simple.demo.indicators.InputDataHealthTrackerV2;

/**
 * A memory-efficient implementation of time-based average price and standard deviation bands.
 * Uses a bucketed approach to drastically reduce memory usage for large time windows.
 */
public class BucketedAverageWithSTDBands extends AverageWithSTDBands {
    
    // Use TreeMap instead of ArrayList for more efficient bucket lookup and management
    private final TreeMap<Long, TimeBucket> bucketMap = new TreeMap<>();
    
    
    // Default expiration buffer reduced to a more reasonable value (2x window size)
    private long bucketExpirationBufferNanos;
    
    // Configuration
    private final double stdMultiplier;
    private final int windowSizeSecs;
    private final long windowSizeNanos;
    
    // Add an adaptive transition factor
    private double transitionSmoothingFactor = 0.2; // Default value
    
    // Statistics
    private long totalSamples = 0;
    private double mean = 0;
    private double variance = 0;
    private double standardDeviation = 0;
    private double upperBand = 0;
    private double lowerBand = 0;
    private boolean isFirstUpdate = true; // Add flag to track first update
    
    // For tracking variance transitions
    private double previousStandardDeviation = 0;
    private double maxStandardDeviation = 0;
    private static final double DEFAULT_MAX_STANDARD_DEVIATION_CHANGE_RATIO = 0.3; // Max 30% change at once
    private double maxStandardDeviationChangeRatio = DEFAULT_MAX_STANDARD_DEVIATION_CHANGE_RATIO;
    
    // For detecting regime changes
    private double previousMean = 0;
    private int regimeChangeCounter = 0;
    
    // Latest timestamp
    private long latestTimestamp = 0;
    
    // Debugging flag - set to true to enable detailed logging
    private final boolean DEBUG = false;
    private boolean extraDebugMode = false;
    
    // Minimum samples required for band crossing detection
    private static final int MIN_SAMPLES_FOR_BAND_CROSSING = 5;
    
    // Add storage for initial price to ensure proper band calculation
    private double initialPrice = Double.NaN;
    
    // Add last price storage for band crossing detection
    private double lastPrice = Double.NaN;
    
    // Hysteresis factor for band crossing detection (0.1% by default)
    private double hysteresisFactor = 0.001; // Default value of 0.1%
    
    // Constants for band crossing directions
    private static final int UPPER_BAND = 1;
    private static final int LOWER_BAND = -1;
    
    // State cache for band crossing detection
    private Map<BandCrossingKey, BandCrossingState> crossingStateCache = new HashMap<>();
    
    // NEW CONFIG KNOBS
    private static final int    MIN_SAMPLES_PER_BUCKET = 2;     // tune per symbol
    private long bucketSizeNanos = 5_000_000_000L; // Default 5s, now configurable
    
    // ► STATE
    private long lastRecalcBucket = -1;     // last bucket we ran statistics for
    
    // Slope/trend detection fields
    // TODO: Use emaMeanSlope and meanSlopeThreshold for trend-based logic or signals.
    private double lastMean = Double.NaN;
    private double emaMeanSlope = 0;
    private double meanSlopeEmaAlpha = 0.2; // Configurable
    private double meanSlopeThreshold = 0.0; // Configurable
    public enum MeanSlopeState { BULL, BEAR, NEUTRAL }
    // Ready state: true if at least 2 mature buckets and time span >= windowSizeNanos (set in recalculateStatistics()).
    private boolean ready = false;
    
    // Input data health tracking
    InputDataHealthTrackerV2 inputDataHealthTracker;
    
    // Health monitoring enable flag
    private boolean healthMonitoringEnabled = true;
    
    /**
     * Key class for band crossing state tracking
     */
    private static class BandCrossingKey {
        final int direction; // 1 for above upper band, -1 for below lower band
        
        BandCrossingKey(int direction) {
            this.direction = direction;
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            BandCrossingKey other = (BandCrossingKey) obj;
            return direction == other.direction;
        }
        
        @Override
        public int hashCode() {
            return direction;
        }
    }
    
    /**
     * Detailed state tracking for band crossings
     */
    private static class BandCrossingState {
        boolean confirmedCrossing = false;
        boolean wasInCrossingZone = false;
        boolean noLongerInCrossingZone = false;
        
        void reset() {
            confirmedCrossing = false;
            wasInCrossingZone = false;
            noLongerInCrossingZone = false;
        }
    }

    /**
     * A class representing a time bucket that stores summary statistics
     * for all samples within a specific time period.
     */
    private static class TimeBucket {
        final long startTimeNanos;
        long count = 0;
        double sum = 0;
        double sumSquared = 0;
        
        // Track min and max within bucket for debugging
        double minPrice = Double.MAX_VALUE;
        double maxPrice = Double.MIN_VALUE;
        
        TimeBucket(long startTimeNanos) {
            this.startTimeNanos = startTimeNanos;
        }
        
        /**
         * Add a sample to this bucket.
         */
        void addSample(double value) {
            count++;
            sum += value;
            sumSquared += value * value;
            
            // Update min/max
            minPrice = Math.min(minPrice, value);
            maxPrice = Math.max(maxPrice, value);
        }
        
        @Override
        public String toString() {
            return String.format("Bucket[time=%d, count=%d, sum=%.2f, avg=%.2f, min=%.2f, max=%.2f]", 
                startTimeNanos, count, sum, (count > 0 ? sum/count : 0), 
                minPrice == Double.MAX_VALUE ? 0 : minPrice, 
                maxPrice == Double.MIN_VALUE ? 0 : maxPrice);
        }
    }
    
    /**
     * Creates a new bucketed average with standard deviation bands.
     * 
     * @param stdMultiplier Multiplier for standard deviation bands
     * @param windowSizeSecs Window size in seconds
     */
    public BucketedAverageWithSTDBands(double stdMultiplier, int windowSizeSecs) {
        super(stdMultiplier, true, windowSizeSecs); // Call parent constructor for compatibility
        if (stdMultiplier <= 0) throw new IllegalArgumentException("stdMultiplier must be > 0");
        if (windowSizeSecs < 1) throw new IllegalArgumentException("windowSizeSecs must be >= 1");
        this.stdMultiplier = stdMultiplier;
        this.windowSizeSecs = windowSizeSecs;
        this.windowSizeNanos = (long) windowSizeSecs * 1_000_000_000L;
        this.bucketExpirationBufferNanos = windowSizeNanos * 2;
        initializeCrossingStateCache();
        debug("Created BucketedAverageWithSTDBands with window size: " + windowSizeSecs + " seconds");
        // Instantiate V2 tracker
        this.inputDataHealthTracker = new InputDataHealthTrackerV2("StdBands-Tracker");
    }
    
    /**
     * Creates a new bucketed average with standard deviation bands with custom parameters.
     * 
     * @param stdMultiplier Multiplier for standard deviation bands
     * @param windowSizeSecs Window size in seconds
     * @param expirationBufferSecs Additional buffer time in seconds before buckets are expired
     * @param transitionFactor Factor controlling how smooth transitions are (0.0-1.0)
     * @param maxChangeRatio Maximum change ratio for standard deviation between updates
     */
    public BucketedAverageWithSTDBands(double stdMultiplier, int windowSizeSecs, 
                                      int expirationBufferSecs, double transitionFactor,
                                      double maxChangeRatio) {
        super(stdMultiplier, true, windowSizeSecs);
        if (stdMultiplier <= 0) throw new IllegalArgumentException("stdMultiplier must be > 0");
        if (windowSizeSecs < 1) throw new IllegalArgumentException("windowSizeSecs must be >= 1");
        if (expirationBufferSecs < 0) throw new IllegalArgumentException("expirationBufferSecs must be >= 0");
        if (transitionFactor < 0.0 || transitionFactor > 1.0) throw new IllegalArgumentException("transitionFactor must be in [0,1]");
        if (maxChangeRatio < 0.05 || maxChangeRatio > 1.0) throw new IllegalArgumentException("maxChangeRatio must be in [0.05,1.0]");
        this.stdMultiplier = stdMultiplier;
        this.windowSizeSecs = windowSizeSecs;
        this.windowSizeNanos = (long) windowSizeSecs * 1_000_000_000L;
        this.bucketExpirationBufferNanos = (long) expirationBufferSecs * 1_000_000_000L;
        this.transitionSmoothingFactor = Math.max(0.0, Math.min(1.0, transitionFactor));
        this.maxStandardDeviationChangeRatio = Math.max(0.05, Math.min(1.0, maxChangeRatio));
        initializeCrossingStateCache();
        debug("Created BucketedAverageWithSTDBands with custom parameters: window=" + windowSizeSecs + "s, buffer=" + expirationBufferSecs + "s, transition=" + transitionFactor + ", maxChange=" + maxChangeRatio);
        // Instantiate V2 tracker
        this.inputDataHealthTracker = new InputDataHealthTrackerV2("StdBands-Tracker");
    }
    
    /**
     * Initialize the state tracking objects for band crossings
     */
    private void initializeCrossingStateCache() {
        crossingStateCache = new HashMap<>();
        crossingStateCache.put(new BandCrossingKey(UPPER_BAND), new BandCrossingState());
        crossingStateCache.put(new BandCrossingKey(LOWER_BAND), new BandCrossingState());
    }
    
    /**
     * Update calculations with a new price.
     */
    @Override
    public void updatePrice(double price, long timestampNanos) {
        boolean valid = !Double.isNaN(price) && !Double.isInfinite(price);
        updateInputDataHealth(price, timestampNanos, valid);
        // ── basic guards ──────────────────────────────────────────────────────────
        if (Double.isNaN(price) || Double.isInfinite(price) || timestampNanos <= 0)
            return;

        lastPrice = price;                          // store for band-crossing logic
        latestTimestamp = Math.max(latestTimestamp, timestampNanos);

        // ── bucket handling ───────────────────────────────────────────────────────
        long bucketStart = (timestampNanos / bucketSizeNanos)
                           * bucketSizeNanos;
        TimeBucket bucket = bucketMap.computeIfAbsent(bucketStart,
                                                      TimeBucket::new);

        bucket.addSample(price);

        // first tick ever → seed things but DON'T recalc yet
        if (isFirstUpdate) {
            isFirstUpdate   = false;
            initialPrice    = price;
        }

        // ── only recalc when we *finish* a bucket and it is "mature" ─────────────
        boolean bucketFinished = bucketStart > lastRecalcBucket;
        boolean bucketMature   = bucket.count >= MIN_SAMPLES_PER_BUCKET;

        if (bucketFinished && bucketMature) {
            // expire old data *before* counting this sample
            removeExpiredBuckets(timestampNanos - windowSizeNanos);

            totalSamples += bucket.count;          // now safe to update the tally
            recalculateStatistics();

            lastRecalcBucket = bucketStart;
        }

        // Band crossing detection with hysteresis
        updateBandCrossingStates(price);
    }
    
    /**
     * Enables or disables extra debug logging without modifying the code.
     * This is useful for diagnosing issues in production.
     * 
     * @param enabled Whether to enable extra debug mode
     */
    public void setExtraDebugMode(boolean enabled) {
        this.extraDebugMode = enabled;
        if (enabled) {
            System.out.println("Extra debug mode enabled for BucketedAverageWithSTDBands");
            // Dump the current state for diagnostic purposes
            System.out.println(dumpBuckets());
        }
    }
    
    /**
     * Checks if debug logging is enabled through either the DEBUG constant
     * or the extraDebugMode setting.
     */
    private boolean isDebugEnabled() {
        return DEBUG || extraDebugMode;
    }
    
    /**
     * Removes buckets that are older than the cutoff time.
     * Uses a configurable buffer to control when buckets are expired.
     * The buffer is subtracted from the cutoff, extending the grace period for bucket expiration.
     */
    private void removeExpiredBuckets(long cutoffTimeNanos) {
        if (bucketMap.isEmpty()) return;

        long effectiveCutoff = cutoffTimeNanos - bucketExpirationBufferNanos;

        if (effectiveCutoff <= 0) return;          // nothing to do
        Map<Long, TimeBucket> expired = bucketMap.headMap(effectiveCutoff, false);

        if (expired.isEmpty()) return;

        // Use a more adaptive approach for preservation
        // If we have enough recent data (at least 20% of the window), we can safely remove old data
        boolean haveEnoughRecentData = false;
        long mostRecentBucketTime = bucketMap.isEmpty() ? 0 : bucketMap.lastKey();
        long recentDataThreshold = mostRecentBucketTime - (windowSizeNanos / 5); // 20% of window
        
        // Count how many buckets are in the recent 20% window
        int recentBucketCount = 0;
        for (Long time : bucketMap.keySet()) {
            if (time >= recentDataThreshold) {
                recentBucketCount++;
            }
        }
        
        // If we have at least 3 buckets in the recent window, we have enough data
        haveEnoughRecentData = recentBucketCount >= 3;
        
        Long mostRecentExpiredKey = null;
        TimeBucket mostRecentExpiredBucket = null;
        
        // Only preserve a bucket if we don't have enough recent data
        if (!expired.isEmpty() && !haveEnoughRecentData) {
            try {
                mostRecentExpiredKey = expired.keySet().stream()
                        .max(Long::compareTo)
                        .orElse(null);
                
                if (mostRecentExpiredKey != null) {
                    mostRecentExpiredBucket = expired.get(mostRecentExpiredKey);
                    if (isDebugEnabled()) {
                        System.out.println("Preserving historical bucket: " + mostRecentExpiredKey + 
                                          " due to insufficient recent data (bucketSizeNanos=" + bucketSizeNanos + ")");
                    }
                }
            } catch (Exception e) {
                if (isDebugEnabled()) {
                    System.out.println("Error while finding most recent expired bucket: " + e.getMessage());
                }
            }
        }
        
        if (!expired.isEmpty()) {
            long removedSamples = 0;
            double removedSum = 0;
            double removedSumSquared = 0;
            
            for (Map.Entry<Long, TimeBucket> entry : expired.entrySet()) {
                Long key = entry.getKey();
                TimeBucket bucket = entry.getValue();
                
                // Skip the bucket we want to preserve
                if (mostRecentExpiredKey != null && key.equals(mostRecentExpiredKey)) {
                    continue;
                }
                
                removedSamples += bucket.count;
                removedSum += bucket.sum;
                removedSumSquared += bucket.sumSquared;
            }
            
            if (isDebugEnabled()) {
                System.out.println("Removing " + (expired.size() - (mostRecentExpiredKey != null ? 1 : 0)) + 
                                  " expired buckets with " + removedSamples + 
                                  " samples, effective cutoff: " + effectiveCutoff + 
                                  " (bucketSizeNanos=" + bucketSizeNanos + ")");
            }
            
            totalSamples -= removedSamples;
            
            // Clear the expired buckets, but preserve the one we want to keep
            if (mostRecentExpiredKey != null) {
                TimeBucket preserved = expired.remove(mostRecentExpiredKey);
                expired.clear();
                bucketMap.put(mostRecentExpiredKey, preserved);
            } else {
                expired.clear();
            }
            
            // Enhanced transition handling with adaptive smoothing
            if (removedSamples > 0 && totalSamples > 0) {
                double retainedRatio = (double) totalSamples / (totalSamples + removedSamples);
                double removedRatio = 1.0 - retainedRatio;
                
                // Adapt the transition factor based on how much data was removed
                double adaptiveTransitionFactor = Math.min(transitionSmoothingFactor * 2, 
                                                        removedRatio * transitionSmoothingFactor);
                
                if (removedRatio > 0.2) { // Only apply when significant data is removed
                    if (isDebugEnabled()) {
                        System.out.println("Applying adaptive transition factor: " + adaptiveTransitionFactor + 
                                          " to smooth statistical change");
                    }
                    
                    if (!Double.isNaN(mean) && mean != 0 && totalSamples > 0) {
                        double removedMean = removedSum / removedSamples;
                        
                        // Store current mean to detect regime changes
                        previousMean = mean;
                        
                        // Apply transition smoothing to mean
                        mean = (mean * (1.0 - adaptiveTransitionFactor)) + (removedMean * adaptiveTransitionFactor);
                        
                        if (variance > 0) {
                            double removedMeanOfSquares = removedSumSquared / removedSamples;
                            double removedVariance = removedMeanOfSquares - (removedMean * removedMean);
                            
                            if (removedVariance >= 0) {
                                // Apply transition smoothing to variance
                                variance = (variance * (1.0 - adaptiveTransitionFactor)) + 
                                           (removedVariance * adaptiveTransitionFactor);
                                standardDeviation = Math.sqrt(variance);
                            }
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Recalculates mean, variance, standard deviation, and bands.
     * Enhanced with more adaptive transition handling and regime change detection.
     */
    private void recalculateStatistics() {
        if (bucketMap.isEmpty() || totalSamples == 0) return;

        // ‣‣‣ NEW: skip buckets that never reached the min sample count
        double sum = 0, sumSq = 0;
        long   cnt = 0;
        int matureBucketCount = 0;
        long minTime = Long.MAX_VALUE, maxTime = Long.MIN_VALUE;
        for (TimeBucket b : bucketMap.values()) {
            if (b.count < MIN_SAMPLES_PER_BUCKET) continue;   // ignore "infant" buckets
            sum   += b.sum;
            sumSq += b.sumSquared;
            cnt   += b.count;
            matureBucketCount++;
            minTime = Math.min(minTime, b.startTimeNanos);
            maxTime = Math.max(maxTime, b.startTimeNanos);
        }
        if (cnt == 0) {
            ready = false;
            return;     // nothing mature yet
        }
        totalSamples = cnt;

        // Ready state: at least 2 mature buckets and time span >= windowSizeNanos
        ready = (matureBucketCount >= 2) && ((maxTime - minTime) >= windowSizeNanos);

        // replace every occurrence of 'totalSamples' by 'cnt' in the
        // calculations that follow (mean, variance, etc.)
        double newMean = sum / cnt;
        double newVar  = (sumSq / cnt) - (newMean * newMean);

        // Slope/trend detection
        if (!Double.isNaN(lastMean)) {
            double meanDelta = newMean - lastMean;
            emaMeanSlope = meanSlopeEmaAlpha * meanDelta + (1 - meanSlopeEmaAlpha) * emaMeanSlope;
        }
        lastMean = newMean;
        
        // Store previous values for transition smoothing
        previousStandardDeviation = standardDeviation;
        double oldMean = mean;
        
        // Check for regime change (significant mean shift)
        if (previousMean != 0 && Math.abs(newMean - previousMean) > previousStandardDeviation * 2) {
            regimeChangeCounter++;
            
            if (isDebugEnabled()) {
                System.out.println("Potential regime change detected: old mean=" + previousMean + 
                                  ", new mean=" + newMean + 
                                  ", counter=" + regimeChangeCounter);
            }
            
            // If we detect consistent regime change over 3 calculations, adapt faster
            if (regimeChangeCounter >= 3) {
                // Reset max standard deviation to allow faster adaptation
                maxStandardDeviation = 0;
                
                // Use a faster transition for sudden market regime changes
                double regimeTransitionFactor = 0.7; // 70% weight to new values
                mean = (newMean * regimeTransitionFactor) + (mean * (1.0 - regimeTransitionFactor));
                
                if (isDebugEnabled()) {
                    System.out.println("Confirmed regime change - accelerating adaptation");
                }
                
                // Reset the counter after adapting
                regimeChangeCounter = 0;
            }
        } else {
            mean = newMean;
            regimeChangeCounter = 0; // Reset counter when no regime change
        }
        
        // Handle potential floating point errors that might cause slightly negative variance
        if (newVar < 0) {
            if (Math.abs(newVar) < 1e-10) { // Small negative value due to floating point error
                newVar = 0;
            } else {
                if (isDebugEnabled()) {
                    System.out.println("WARNING: Negative variance calculated: " + newVar);
                    System.out.println("  mean: " + mean + ", meanOfSquares: " + (sumSq / cnt));
                    System.out.println("  sumOfAll: " + sum + ", sumSquaredOfAll: " + sumSq);
                    System.out.println("  totalSamples: " + cnt + ", buckets: " + bucketMap.size());
                }
                // Fall back to traditional method for variance calculation
                recalculateVarianceTraditionally();
            }
        }
        
        // If we lack data diversity or have a very small variance, establish a minimum variance
        // based on the scale of the mean to prevent band collapse
        if (newVar < 1e-10) {
            double meanAbsValue = Math.abs(mean);
            double minVariance = meanAbsValue > 0 ? 
                    Math.max(meanAbsValue * meanAbsValue * 1e-6, 1e-10) : 1e-10;
            
            if (newVar < minVariance) {
                if (isDebugEnabled()) {
                    System.out.println("Applying minimum variance: " + minVariance + 
                                      " due to insufficient data diversity");
                }
                newVar = minVariance;
            }
        }
        
        // Calculate standard deviation
        double newStdDev = Math.sqrt(Math.max(newVar, 0));
        
        // Apply more adaptive transition smoothing
        if (previousStandardDeviation > 0) {
            // Track maximum observed standard deviation with a decay factor
            // This allows the maximum to slowly decrease over time when volatility subsides
            if (newStdDev > maxStandardDeviation) {
                maxStandardDeviation = newStdDev;
            } else if (maxStandardDeviation > 0) {
                // Apply slow decay to max std dev (0.1% per calculation)
                maxStandardDeviation = maxStandardDeviation * 0.999;
            }
            
            // More adaptive transition logic
            if (newStdDev < previousStandardDeviation) {
                // Dynamic change limit based on data stability
                double changeLimit = maxStandardDeviationChangeRatio;
                
                // If we're in a volatile period, allow faster decreases
                boolean isVolatile = maxStandardDeviation > 0 && 
                                   previousStandardDeviation > (maxStandardDeviation * 0.8);
                
                if (isVolatile) {
                    changeLimit = Math.min(1.0, changeLimit * 1.5); // Up to 50% more reduction allowed
                }
                
                double minAllowedStdDev = previousStandardDeviation * (1.0 - changeLimit);
                
                // Establish an absolute floor based on the max observed std dev
                // More dynamic floor that considers recent market conditions
                double absoluteMinStdDev = maxStandardDeviation * Math.max(0.05, 
                                          Math.min(0.3, newStdDev / maxStandardDeviation));
                
                minAllowedStdDev = Math.max(minAllowedStdDev, absoluteMinStdDev);
                
                if (newStdDev < minAllowedStdDev) {
                    if (isDebugEnabled()) {
                        System.out.println("Limiting standard deviation reduction: " + 
                                          newStdDev + " -> " + minAllowedStdDev + 
                                          " (previous: " + previousStandardDeviation + 
                                          ", max: " + maxStandardDeviation + ")");
                    }
                    newStdDev = minAllowedStdDev;
                }
            } else {
                // For increases, allow faster adaptation
                newStdDev = Math.sqrt(Math.max(newVar, 0));
            }
        } else {
            newStdDev = Math.sqrt(Math.max(newVar, 0));
            maxStandardDeviation = newStdDev; // Initialize max
        }
        
        // Calculate bands using the smoothed standard deviation
        upperBand = mean + (stdMultiplier * newStdDev);
        lowerBand = mean - (stdMultiplier * newStdDev);
        
        if (isDebugEnabled() && totalSamples % 1000 == 0) {
            System.out.println("Statistics recalculated: mean=" + mean + 
                              ", std=" + newStdDev + 
                              " (raw: " + newStdDev + ")" +
                              ", bands=[" + lowerBand + ", " + upperBand + "]" +
                              ", buckets=" + bucketMap.size());
        }

        // ...rest of your smoothing / regime-change logic remains unchanged…
        // (for simplicity, assign totalSamples = cnt; right after this block)
 
        mean = newMean;
        variance = newVar;
        standardDeviation = newStdDev;
        upperBand = mean + (stdMultiplier * standardDeviation);
        lowerBand = mean - (stdMultiplier * standardDeviation);
    }
    
    /**
     * Alternative method to calculate variance using the traditional formula.
     * This is used as a fallback if the computational formula produces errors.
     */
    private void recalculateVarianceTraditionally() {
        // Calculate variance using sum of squared differences
        double sumSquaredDiff = 0;
        long count = 0;
        
        // First pass: calculate the mean
        double sum = 0;
        for (TimeBucket bucket : bucketMap.values()) {
            sum += bucket.sum;
            count += bucket.count;
        }
        double calculatedMean = sum / count;
        
        // Second pass: for each bucket, calculate contribution to variance
        for (TimeBucket bucket : bucketMap.values()) {
            // If bucket has more than one sample, we need to expand it
            if (bucket.count > 1) {
                // We can't know exact differences without original values, 
                // so we'll assume uniform distribution within bucket
                double bucketMean = bucket.sum / bucket.count;
                double bucketVariance = bucket.sumSquared / bucket.count - (bucketMean * bucketMean);
                
                // Each sample contributes both its bucket's internal variance
                // and the squared difference of the bucket mean from the overall mean
                sumSquaredDiff += bucket.count * bucketVariance;
                sumSquaredDiff += bucket.count * Math.pow(bucketMean - calculatedMean, 2);
            } else if (bucket.count == 1) {
                // For single-sample buckets, just use the squared difference from mean
                double singleValue = bucket.sum; // For one sample, sum equals the value
                sumSquaredDiff += Math.pow(singleValue - calculatedMean, 2);
            }
        }
        
        // Set the variance and update mean
        variance = count > 1 ? sumSquaredDiff / count : 0;
        mean = calculatedMean;
        
        if (isDebugEnabled()) {
            System.out.println("Variance recalculated traditionally: " + variance);
        }
    }
    
    /**
     * Check if enough data has been processed to provide meaningful results.
     */
    @Override
    public boolean isValid() {
        return totalSamples > 0;
    }
    
    /**
     * Reset all calculations.
     */
    @Override
    public void reset() {
        super.reset();
        if (bucketMap != null) {
            bucketMap.clear();
        }
        
        // Reset statistics
        totalSamples = 0;
        mean = 0;
        variance = 0;
        standardDeviation = 0;
        upperBand = 0;
        lowerBand = 0;
        latestTimestamp = 0;
        isFirstUpdate = true;
        initialPrice = Double.NaN; // Reset initial price
        lastPrice = Double.NaN;
        
        // Reset variance transition tracking
        previousStandardDeviation = 0;
        maxStandardDeviation = 0;
        previousMean = 0;
        regimeChangeCounter = 0;
        
        // Reset crossing state cache
        initializeCrossingStateCache();
        
        // Reset slope/trend fields
        lastMean = Double.NaN;
        emaMeanSlope = 0;
        
        // Reset ready flag
        ready = false;
        
        // Debug
        debug("BucketedAverageWithSTDBands reset");
        
        healthMonitoringEnabled = true;
    }
    
    /**
     * Update time without adding a new price point.
     * This is useful for cleaning up old buckets when no new data has arrived.
     */
    @Override
    public void updateTime(long timestampNanos) {
        if (timestampNanos <= 0) {
            return;
        }
        
        // Update latest timestamp if the provided time is more recent
        if (timestampNanos > latestTimestamp) {
            latestTimestamp = timestampNanos;
            
            // Remove old buckets
            removeExpiredBuckets(timestampNanos - windowSizeNanos);
            
            // Recalculate statistics if buckets were removed
            recalculateStatistics();
        }
    }
    
    /**
     * Updates band crossing states with hysteresis.
     * This is the core of the band crossing detection mechanism.
     */
    private void updateBandCrossingStates(double price) {
        if (Double.isNaN(price)) {
            return;
        }
        
        // Get current bands
        double upperBand = getUpperBand();
        double lowerBand = getLowerBand();
        
        // Get band crossing states with null safety
        BandCrossingState upperState = crossingStateCache.get(new BandCrossingKey(UPPER_BAND));
        BandCrossingState lowerState = crossingStateCache.get(new BandCrossingKey(LOWER_BAND));
        
        // Guard against null states (should never happen, but be defensive)
        if (upperState == null || lowerState == null) {
            return;
        }
        
        // Reset one-time transition flags at start of each update
        upperState.noLongerInCrossingZone = false;
        lowerState.noLongerInCrossingZone = false;
        
        // Calculate deadbands based on band distance from mean and hysteresis factor
        // The deadband should be relative to the band width (standard deviation), not the absolute band value
        double bandWidth = standardDeviation * stdMultiplier;
        double upperDeadband = bandWidth * hysteresisFactor;
        double lowerDeadband = bandWidth * hysteresisFactor;
        
        // Step 1: Check if price is above upper band with deadband
        boolean isAboveUpperBand = (price - upperBand) > upperDeadband;
        
        // Step 2: Check if price is below lower band with deadband
        boolean isBelowLowerBand = (lowerBand - price) > lowerDeadband;
        
        // Handle impossible case of being both above upper and below lower
        if (isAboveUpperBand && isBelowLowerBand) {
            upperState.confirmedCrossing = false;
            upperState.wasInCrossingZone = false;
            lowerState.confirmedCrossing = false;
            lowerState.wasInCrossingZone = false;
            return;
        }
        
        // Handle upper band crossing
        if (isAboveUpperBand) {
            upperState.confirmedCrossing = true;
            upperState.wasInCrossingZone = true;
            lowerState.confirmedCrossing = false;
        } else if (upperState.confirmedCrossing) {
            if ((upperBand - price) > upperDeadband) {
                upperState.noLongerInCrossingZone = true;
                upperState.confirmedCrossing = false;
            }
        }
        
        // Handle lower band crossing
        if (isBelowLowerBand) {
            lowerState.confirmedCrossing = true;
            lowerState.wasInCrossingZone = true;
            upperState.confirmedCrossing = false;
        } else if (lowerState.confirmedCrossing) {
            if ((price - lowerBand) > lowerDeadband) {
                lowerState.noLongerInCrossingZone = true;
                lowerState.confirmedCrossing = false;
            }
        }
    }

    // Remove or comment out the unused calculateRecentVolatility() method as we're 
    // using a simpler momentum approach now

    // Getters
    
    @Override
    public double getAverage() {
        return mean;
    }
    
    @Override
    public double getStandardDeviation() {
        return standardDeviation;
    }
    
    @Override
    public double getUpperBand() {
        // Special handling for initial state or invalid state
        if (totalSamples == 0 || upperBand == 0) {
            if (!Double.isNaN(initialPrice)) {
                double initStdDev = Math.max(0.0001, Math.abs(initialPrice) * 0.0001);
                return initialPrice + (stdMultiplier * initStdDev);
            }
        }
        return upperBand;
    }
    
    @Override
    public double getLowerBand() {
        // Special handling for initial state or invalid state
        if (totalSamples == 0 || lowerBand == 0) {
            if (!Double.isNaN(initialPrice)) {
                double initStdDev = Math.max(0.0001, Math.abs(initialPrice) * 0.0001);
                return initialPrice - (stdMultiplier * initStdDev);
            }
        }
        return lowerBand;
    }
    
    @Override
    public long getSampleCount() {
        return totalSamples;
    }
    
    @Override
    public double getStdMultiplier() {
        return stdMultiplier;
    }
    
    @Override
    public boolean isRunningAverage() {
        return true;
    }
    
    @Override
    public int getWindowSizeSecs() {
        return windowSizeSecs;
    }
    
    @Override
    public long getWindowSizeNanos() {
        return windowSizeNanos;
    }
    
    @Override
    public long getLatestTimestamp() {
        return latestTimestamp;
    }
    
    /**
     * Gets the number of buckets currently in use.
     */
    public int getBucketCount() {
        return bucketMap.size();
    }
    
    /**
     * For debugging: print detailed information about all buckets
     */
    public String dumpBuckets() {
        StringBuilder sb = new StringBuilder();
        sb.append("BucketedAverageWithSTDBands state:\n");
        sb.append(String.format("  Total samples: %d, Buckets: %d\n", totalSamples, bucketMap.size()));
        sb.append(String.format("  Mean: %.4f, StdDev: %.4f (Max observed: %.4f)\n", 
                               mean, standardDeviation, maxStandardDeviation));
        sb.append(String.format("  Bands: [%.4f, %.4f]\n", lowerBand, upperBand));
        
        sb.append("  Buckets:\n");
        for (Map.Entry<Long, TimeBucket> entry : bucketMap.entrySet()) {
            TimeBucket bucket = entry.getValue();
            sb.append(String.format("    %s\n", bucket));
        }
        
        return sb.toString();
    }
    
    /**
     * Set the transition smoothing factor.
     * Higher values (closer to 1.0) produce more responsive transitions.
     * Lower values (closer to 0.0) produce smoother transitions.
     * 
     * @param factor Value between 0.0 and 1.0
     */
    public void setTransitionSmoothingFactor(double factor) {
        this.transitionSmoothingFactor = Math.max(0.0, Math.min(1.0, factor));
        if (isDebugEnabled()) {
            System.out.println("Transition smoothing factor set to: " + this.transitionSmoothingFactor);
        }
    }
    
    /**
     * Set the maximum standard deviation change ratio per update.
     * 
     * @param ratio Value between 0.05 and 1.0
     */
    public void setMaxStdDevChangeRatio(double ratio) {
        this.maxStandardDeviationChangeRatio = Math.max(0.05, Math.min(1.0, ratio));
        if (isDebugEnabled()) {
            System.out.println("Max standard deviation change ratio set to: " + this.maxStandardDeviationChangeRatio);
        }
    }
    
    /**
     * Set the bucket expiration buffer in seconds.
     * 
     * @param bufferSecs Additional time in seconds to keep buckets beyond the window size
     */
    public void setBucketExpirationBuffer(int bufferSecs) {
        this.bucketExpirationBufferNanos = (long) bufferSecs * 1_000_000_000L;
        if (isDebugEnabled()) {
            System.out.println("Bucket expiration buffer set to: " + bufferSecs + " seconds");
        }
    }
    
    /**
     * Checks if the last updated price is above the upper band with hysteresis.
     * 
     * @return true if the price is confirmed to be above the upper band
     */
    public boolean isAboveUpperBand() {
        // First check if we have enough data for meaningful results
        if (!isValid() || totalSamples < MIN_SAMPLES_FOR_BAND_CROSSING) {
            return false;
        }
        
        BandCrossingState state = crossingStateCache.get(new BandCrossingKey(UPPER_BAND));
        return state != null && state.confirmedCrossing;
    }
    
    /**
     * Checks if the last updated price is below the lower band with hysteresis.
     * 
     * @return true if the price is confirmed to be below the lower band
     */
    public boolean isBelowLowerBand() {
        // First check if we have enough data for meaningful results
        if (!isValid() || totalSamples < MIN_SAMPLES_FOR_BAND_CROSSING) {
            return false;
        }
        
        BandCrossingState state = crossingStateCache.get(new BandCrossingKey(LOWER_BAND));
        return state != null && state.confirmedCrossing;
    }
    
    /**
     * Checks if the last updated price has transitioned from above the upper band
     * to inside the bands, with hysteresis.
     * 
     * @return true if the price is confirmed to have moved back inside from above
     */
    public boolean isNoLongerAboveUpperBand() {
        // First check if we have enough data for meaningful results
        if (!isValid() || totalSamples < MIN_SAMPLES_FOR_BAND_CROSSING) {
            return false;
        }
        
        BandCrossingState state = crossingStateCache.get(new BandCrossingKey(UPPER_BAND));
        return state != null && state.noLongerInCrossingZone;
    }
    
    /**
     * Checks if the last updated price has transitioned from below the lower band
     * to inside the bands, with hysteresis.
     * 
     * @return true if the price is confirmed to have moved back inside from below
     */
    public boolean isNoLongerBelowLowerBand() {
        // First check if we have enough data for meaningful results
        if (!isValid() || totalSamples < MIN_SAMPLES_FOR_BAND_CROSSING) {
            return false;
        }
        
        BandCrossingState state = crossingStateCache.get(new BandCrossingKey(LOWER_BAND));
        return state != null && state.noLongerInCrossingZone;
    }
    
    /**
     * Checks if the last price was previously confirmed above upper band 
     * (regardless of current status)
     */
    public boolean wasConfirmedAboveUpperBand() {
        // First check if we have enough data for meaningful results
        if (!isValid() || totalSamples < MIN_SAMPLES_FOR_BAND_CROSSING) {
            return false;
        }
        
        BandCrossingState state = crossingStateCache.get(new BandCrossingKey(UPPER_BAND));
        return state != null && state.wasInCrossingZone;
    }
    
    /**
     * Checks if the last price was previously confirmed below lower band
     * (regardless of current status)
     */
    public boolean wasConfirmedBelowLowerBand() {
        // First check if we have enough data for meaningful results
        if (!isValid() || totalSamples < MIN_SAMPLES_FOR_BAND_CROSSING) {
            return false;
        }
        
        BandCrossingState state = crossingStateCache.get(new BandCrossingKey(LOWER_BAND));
        return state != null && state.wasInCrossingZone;
    }

    /**
     * Gets the last price that was updated.
     */
    public double getLastPrice() {
        return lastPrice;
    }

    /**
     * Set the hysteresis factor used for band crossing detection.
     * Higher values create a larger deadband, reducing false signals but increasing lag.
     * Lower values increase sensitivity but may produce more false signals.
     * 
     * @param factor Value between 0.0001 (0.01%) and 0.01 (1%)
     */
    public void setHysteresisFactor(double factor) {
        // Limit the range to reasonable values
        // Tests expect exactly 0.0001 to be allowed
        if (factor < 0.0001) {
            factor = 0.0001;
        } else if (factor > 0.01) {
            factor = 0.01;
        }
        this.hysteresisFactor = factor;
        
        if (isDebugEnabled()) {
            System.out.println("Hysteresis factor set to: " + (this.hysteresisFactor * 100) + "%");
        }
    }

    /**
     * Gets the current hysteresis factor used for band crossing detection.
     * 
     * @return The current hysteresis factor
     */
    public double getHysteresisFactor() {
        return hysteresisFactor;
    }

    /**
     * Set the bucket size in nanoseconds.
     * @param nanos The new bucket size in nanoseconds (must be > 0)
     */
    public void setBucketSizeNanos(long nanos) {
        if (nanos > 0) {
            this.bucketSizeNanos = nanos;
            // Optionally reset buckets if you want to start fresh
            // reset();
            if (isDebugEnabled()) {
                System.out.println("Bucket size set to: " + nanos + " nanoseconds");
            }
        }
    }

    public void setMinSamplesPerBucket(int minSamples) {
        if (minSamples > 0) {
            // MIN_SAMPLES_PER_BUCKET = minSamples; // Uncomment if you make it non-final
            // As above, MIN_SAMPLES_PER_BUCKET is final, so to allow runtime change, make it non-final.
        }
    }

    // Slope/trend detection accessors
    public double getMeanSlope() {
        return emaMeanSlope;
    }
    public MeanSlopeState getMeanSlopeState() {
        if (emaMeanSlope > meanSlopeThreshold) return MeanSlopeState.BULL;
        else if (emaMeanSlope < -meanSlopeThreshold) return MeanSlopeState.BEAR;
        else return MeanSlopeState.NEUTRAL;
    }
    public void setMeanSlopeEmaAlpha(double alpha) {
        if (alpha <= 0 || alpha >= 1) throw new IllegalArgumentException("meanSlopeEmaAlpha must be in (0,1)");
        this.meanSlopeEmaAlpha = alpha;
    }
    public void setMeanSlopeThreshold(double threshold) {
        this.meanSlopeThreshold = threshold;
    }

    // Normalized distance metrics
    public double getPriceDistanceFromMean(double price) {
        return price - mean;
    }
    public double getNormalizedPriceDistanceFromMean(double price) {
        double std = getStandardDeviation();
        double denom = stdMultiplier * std;
        if (denom == 0) return Double.NaN;
        return (price - mean) / denom;
    }

    // Dedicated debug method
    private void debug(String msg) {
        if (isDebugEnabled()) System.out.println(msg);
    }

    // Expose ready state
    public boolean isReady() {
        return ready && (!healthMonitoringEnabled || inputDataHealthTracker.isHealthy());
    }

    // Signal snapshot class
    public static class SignalSnapshot {
        private final boolean ready;
        private final double mean;
        private final double standardDeviation;
        private final double upperBand;
        private final double lowerBand;
        private final double lastPrice;
        private final double meanSlope;
        private final MeanSlopeState meanSlopeState;
        private final boolean aboveUpperBand;
        private final boolean belowLowerBand;
        private final boolean noLongerAboveUpperBand;
        private final boolean noLongerBelowLowerBand;
        private final double priceDistanceFromMean;
        private final double normalizedPriceDistanceFromMean;

        public SignalSnapshot(boolean ready, double mean, double standardDeviation, double upperBand, double lowerBand, double lastPrice, double meanSlope, MeanSlopeState meanSlopeState, boolean aboveUpperBand, boolean belowLowerBand, boolean noLongerAboveUpperBand, boolean noLongerBelowLowerBand, double priceDistanceFromMean, double normalizedPriceDistanceFromMean) {
            this.ready = ready;
            this.mean = mean;
            this.standardDeviation = standardDeviation;
            this.upperBand = upperBand;
            this.lowerBand = lowerBand;
            this.lastPrice = lastPrice;
            this.meanSlope = meanSlope;
            this.meanSlopeState = meanSlopeState;
            this.aboveUpperBand = aboveUpperBand;
            this.belowLowerBand = belowLowerBand;
            this.noLongerAboveUpperBand = noLongerAboveUpperBand;
            this.noLongerBelowLowerBand = noLongerBelowLowerBand;
            this.priceDistanceFromMean = priceDistanceFromMean;
            this.normalizedPriceDistanceFromMean = normalizedPriceDistanceFromMean;
        }

        public boolean isReady() { return ready; }
        public double getMean() { return mean; }
        public double getStandardDeviation() { return standardDeviation; }
        public double getUpperBand() { return upperBand; }
        public double getLowerBand() { return lowerBand; }
        public double getLastPrice() { return lastPrice; }
        public double getMeanSlope() { return meanSlope; }
        public MeanSlopeState getMeanSlopeState() { return meanSlopeState; }
        public boolean isAboveUpperBand() { return aboveUpperBand; }
        public boolean isBelowLowerBand() { return belowLowerBand; }
        public boolean isNoLongerAboveUpperBand() { return noLongerAboveUpperBand; }
        public boolean isNoLongerBelowLowerBand() { return noLongerBelowLowerBand; }
        public double getPriceDistanceFromMean() { return priceDistanceFromMean; }
        public double getNormalizedPriceDistanceFromMean() { return normalizedPriceDistanceFromMean; }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            SignalSnapshot that = (SignalSnapshot) o;
            return ready == that.ready &&
                   Double.compare(that.mean, mean) == 0 &&
                   Double.compare(that.standardDeviation, standardDeviation) == 0 &&
                   Double.compare(that.upperBand, upperBand) == 0 &&
                   Double.compare(that.lowerBand, lowerBand) == 0 &&
                   Double.compare(that.lastPrice, lastPrice) == 0 &&
                   Double.compare(that.meanSlope, meanSlope) == 0 &&
                   meanSlopeState == that.meanSlopeState &&
                   aboveUpperBand == that.aboveUpperBand &&
                   belowLowerBand == that.belowLowerBand &&
                   noLongerAboveUpperBand == that.noLongerAboveUpperBand &&
                   noLongerBelowLowerBand == that.noLongerBelowLowerBand &&
                   Double.compare(that.priceDistanceFromMean, priceDistanceFromMean) == 0 &&
                   Double.compare(that.normalizedPriceDistanceFromMean, normalizedPriceDistanceFromMean) == 0;
        }

        @Override
        public int hashCode() {
            return java.util.Objects.hash(ready, mean, standardDeviation, upperBand, lowerBand, lastPrice, meanSlope, meanSlopeState, aboveUpperBand, belowLowerBand, noLongerAboveUpperBand, noLongerBelowLowerBand, priceDistanceFromMean, normalizedPriceDistanceFromMean);
        }
        
    }
    public SignalSnapshot getSignalSnapshot() {
        return new SignalSnapshot(
            isReady(),
            mean,
            standardDeviation,
            getUpperBand(),
            getLowerBand(),
            lastPrice,
            getMeanSlope(),
            getMeanSlopeState(),
            isAboveUpperBand(),
            isBelowLowerBand(),
            isNoLongerAboveUpperBand(),
            isNoLongerBelowLowerBand(),
            getPriceDistanceFromMean(lastPrice),
            getNormalizedPriceDistanceFromMean(lastPrice)
        );
    }

    public InputDataHealthTrackerV2.Status getInputDataHealth() {
        return inputDataHealthTracker.getStatus();
    }
    public boolean isInputDataHealthy() {
        return inputDataHealthTracker.isHealthy();
    }

    private void updateInputDataHealth(double value, long timestampNanos, boolean valid) {
        if (healthMonitoringEnabled && inputDataHealthTracker != null) {
            inputDataHealthTracker.update(value, timestampNanos, valid);
        }
    }

    public void cleanup() {
        if (inputDataHealthTracker != null) {
            inputDataHealthTracker.reset();
            inputDataHealthTracker = null;
        }
    }

    public void setHealthMonitoringEnabled(boolean enabled) {
        this.healthMonitoringEnabled = enabled;
    }
}