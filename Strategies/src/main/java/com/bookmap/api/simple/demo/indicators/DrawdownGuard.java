package com.bookmap.api.simple.demo.indicators;

import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * High-performance, thread-safe drawdown protection system with two-stage protection:
 * Stage A: Hard stops for trades that never go profitable
 * Stage B: Profit give-back protection for profitable trades
 * 
 * Optimized for minimal allocations and cache-friendly access patterns.
 */
public final class DrawdownGuard {
    // Constants
    private static final double FP_EPSILON = 1e-9;
    private static final long LOG_THROTTLE_MS = 1_000;
    private static final String TOTAL_DD_CASH_REASON = "Total DD: Cash Limit";
    private static final String TOTAL_DD_PCT_REASON = "Total DD: Percent Limit"; 
    private static final String TOTAL_DD_UNKNOWN_REASON = "Total DD: Unknown";
    private static final String UNREAL_DD_HARD_CASH = "Unrealized DD: Hard Cash Stop";
    private static final String UNREAL_DD_HARD_PCT = "Unrealized DD: Hard Percent Stop";
    private static final String UNREAL_DD_GIVE_BACK = "Unrealized DD: Profit Give-Back %";
    private static final String UNREAL_DD_OTHER = "Unrealized DD: Other";

    // Configuration (immutable)
    private final double ddCash;
    private final double ddPct;
    private final double ddUnrCash;
    private final double ddUnrPct;
    private final double ddGivePct;
    private final double profitEpsilon;
    private final boolean totalDDProtectionActiveConfig;
    private final boolean unrealizedDDProtectionActiveConfig;
    private final String instanceId;
    private final String algo;
    private final DrawdownGuardLogger logger;
    
    // State variables (thread-safe)
    private volatile String totalDrawdownTriggerReason = "";
    private volatile String unrealizedDrawdownTriggerReason = "";
    private volatile double entryNotional = 0.0;
    private volatile long lastLog = 0L;

    // Peaks (atomic for multi-threading)
    private final AtomicReference<Double> peakGain = new AtomicReference<>(Double.NEGATIVE_INFINITY);
    private final AtomicReference<Double> peakLoss = new AtomicReference<>(Double.POSITIVE_INFINITY);
    private final AtomicReference<Double> peakTotal = new AtomicReference<>(Double.NEGATIVE_INFINITY);

    // Flags and counters
    private final AtomicBoolean totalDrawdownHit = new AtomicBoolean(false);
    private final AtomicBoolean unrealizedDrawdownActionTaken = new AtomicBoolean(false);
    private final AtomicInteger unrealizedDrawdownFlattenCount = new AtomicInteger(0);
    private final AtomicInteger unrealizedDrawdownDetectionCount = new AtomicInteger(0);

    // Snapshot values (cached for performance)
    private volatile double currentTotalDrawdownValueSnapshot = 0.0;
    private volatile double currentUnrealizedDrawdownValueSnapshot = 0.0;
    private volatile double currentTotalDrawdownPercentSnapshot = 0.0;
    private volatile double currentUnrealizedDrawdownPercentSnapshot = 0.0;

    // Interface for logging to allow dependency injection
    public interface DrawdownGuardLogger {
        void log(String message);
        boolean isDebugEnabled();
        double getStartingAccountBalance();
    }

    public DrawdownGuard(double ddCash, double ddPct,
                  double ddUnrCash, double ddUnrPct,
                  double ddGivePct, double profitEpsilonTicks,
                  boolean totalDDProtectionActive, boolean unrealizedDDProtectionActive,
                  String instanceId, String algo, DrawdownGuardLogger logger) {

        validateConstructorArgs(ddCash, ddUnrCash, profitEpsilonTicks, instanceId, algo, logger);

        this.ddCash = ddCash;
        this.ddPct = ddPct;
        this.ddUnrCash = ddUnrCash;
        this.ddUnrPct = ddUnrPct;
        this.ddGivePct = ddGivePct;
        this.profitEpsilon = profitEpsilonTicks;
        this.totalDDProtectionActiveConfig = totalDDProtectionActive;
        this.unrealizedDDProtectionActiveConfig = unrealizedDDProtectionActive;
        this.instanceId = instanceId;
        this.algo = algo;
        this.logger = logger;

        logInitialization();
    }

    private void validateConstructorArgs(double ddCash, double ddUnrCash, double profitEpsilonTicks,
                                       String instanceId, String algo, DrawdownGuardLogger logger) {
        if (ddCash < 0) {
            throw new IllegalArgumentException("ddCash cannot be negative.");
        }
        if (ddUnrCash < 0) {
            throw new IllegalArgumentException("ddUnrCash cannot be negative.");
        }
        if (profitEpsilonTicks < 0) {
            logger.log(String.format(
                "[DDG_WARN] Inst: %s | Algo: %s | profitEpsilonTicks (%.2f) is negative. Consider using a non-negative value.",
                instanceId, algo, profitEpsilonTicks));
        }
    }

    private void logInitialization() {
        if (logger.isDebugEnabled()) {
            logger.log(String.format(
                "[DDG_INIT] Inst: %s | Algo: %s | Params: ddCash=%.2f, ddPct=%.2f, ddUnrCash=%.2f, ddUnrPct=%.2f, ddGivePct=%.2f, profitEpsilonTicks=%.4f, totalActive=%b, unrActive=%b",
                instanceId, algo, ddCash, ddPct, ddUnrCash, ddUnrPct, ddGivePct, profitEpsilon, totalDDProtectionActiveConfig, unrealizedDDProtectionActiveConfig));
        }
    }

    /**
     * Two‑stage draw‑down protection.
     * <br>Stage A – "hard stop" caps the *first* adverse excursion (even if the trade never goes green).  
     * <br>Stage B – classic profit give‑back once the position has been in the money.
     * <br>Total‑equity guard (realised + unrealised) remains unchanged.
     */
    public void update(double realised, double unrealised, double pointValue) {
        final double currentTotal = realised + unrealised + logger.getStartingAccountBalance();

        // Cache atomic reads for performance
        final double pt = peakTotal.updateAndGet(p -> Math.max(p, currentTotal));
        final double pg = peakGain.updateAndGet(p -> Math.max(p, unrealised));
        peakLoss.updateAndGet(p -> Math.min(p, unrealised));

        if (entryNotional <= 0.0) {
            handleInvalidNotional(realised, unrealised);
            return;
        }

        final boolean neverGreen = pg <= this.profitEpsilon * pointValue;
        
        // Evaluate protection conditions
        final ProtectionConditions conditions = evaluateProtectionConditions(
            realised, unrealised, currentTotal, pt, pg, neverGreen);

        // Handle total drawdown
        handleTotalDrawdown(conditions.cashTot, conditions.pctTot);
        
        // Handle unrealized drawdown
        handleUnrealizedDrawdown(conditions.hardCash, conditions.hardPct, conditions.givePct);

        // Throttled logging
        performThrottledLogging(realised, unrealised, pt, pg);

        // Update snapshots
        updateSnapshots(realised, unrealised, currentTotal, pt, pg, neverGreen);
    }

    private void handleInvalidNotional(double realised, double unrealised) {
        if (logger.isDebugEnabled()) {
            logger.log(String.format(
                "[DDG_WARN] Inst: %s | Algo: %s | entryNotional=%.2f <= 0.0, skipping update(). R=%.2f, U=%.2f",
                instanceId, algo, entryNotional, realised, unrealised));
        }
        resetSnapshots();
    }

    private void resetSnapshots() {
        this.currentTotalDrawdownValueSnapshot = 0.0;
        this.currentUnrealizedDrawdownValueSnapshot = 0.0;
        this.currentTotalDrawdownPercentSnapshot = 0.0;
        this.currentUnrealizedDrawdownPercentSnapshot = 0.0;
    }

    private static class ProtectionConditions {
        final boolean hardCash, hardPct, givePct, cashTot, pctTot;
        
        ProtectionConditions(boolean hardCash, boolean hardPct, boolean givePct, 
                           boolean cashTot, boolean pctTot) {
            this.hardCash = hardCash;
            this.hardPct = hardPct;
            this.givePct = givePct;
            this.cashTot = cashTot;
            this.pctTot = pctTot;
        }
    }

    private ProtectionConditions evaluateProtectionConditions(double realised, double unrealised, 
            double currentTotal, double pt, double pg, boolean neverGreen) {
        
        // Stage A: hard-stop on pure losers
        final boolean hardCash = ddUnrCash > 0 && unrealised <= -ddUnrCash && neverGreen;
        final boolean hardPct = ddUnrPct > 0 && unrealised < 0 && neverGreen &&
                               (-unrealised / entryNotional) >= (ddUnrPct - FP_EPSILON);

        // Stage B: profit give-back
        final boolean givePct = pg > 0 && pg > ddUnrCash && unrealised > 0 &&
                               (pg - unrealised) / pg >= (ddGivePct - FP_EPSILON);

        // Total-equity guard
        final boolean cashTot = ddCash > 0 && currentTotal < pt && (pt - currentTotal) >= ddCash;
        final boolean pctTot = ddPct > 0 && pt > 0 && 
                              ((pt - currentTotal) / pt) >= (ddPct - FP_EPSILON);

        return new ProtectionConditions(hardCash, hardPct, givePct, cashTot, pctTot);
    }

    private void handleTotalDrawdown(boolean cashTot, boolean pctTot) {
        if (totalDrawdownHit.compareAndSet(false, (cashTot || pctTot))) {
            this.totalDrawdownTriggerReason = cashTot ? TOTAL_DD_CASH_REASON :
                                             pctTot ? TOTAL_DD_PCT_REASON : TOTAL_DD_UNKNOWN_REASON;
            
            logger.log(String.format(
                "TOTAL DRAWDOWN HIT (DETECTION): Reason: %s, cashTrigger=%b, percentTrigger=%b. Instance: %s, Algo: %s. Action enabled: %b",
                this.totalDrawdownTriggerReason, cashTot, pctTot, instanceId, algo, this.totalDDProtectionActiveConfig));
        }
    }

    private void handleUnrealizedDrawdown(boolean hardCash, boolean hardPct, boolean givePct) {
        final boolean unrealizedConditionMet = hardCash || hardPct || givePct;
        
        if (!unrealizedConditionMet) return;

        // Only increment detection count if an action hasn't been taken yet
        if (!unrealizedDrawdownActionTaken.get()) {
            unrealizedDrawdownDetectionCount.incrementAndGet();
        }

        if (this.unrealizedDDProtectionActiveConfig) {
            handleUnrealizedDrawdownAction(hardCash, hardPct, givePct);
        } else {
            handleUnrealizedDrawdownDetection(hardCash, hardPct, givePct);
        }
    }

    private void handleUnrealizedDrawdownAction(boolean hardCash, boolean hardPct, boolean givePct) {
        if (unrealizedDrawdownActionTaken.compareAndSet(false, true)) {
            this.unrealizedDrawdownTriggerReason = buildReasonString(hardCash, hardPct, givePct);
            
            logger.log(String.format(
                "UNREAL DRAWDOWN HIT (ACTION ENABLED & TAKEN): Reason: %s, hardCash=%b, hardPct=%b, givePct=%b. Instance: %s, Algo: %s. TotalDDHit: %b",
                this.unrealizedDrawdownTriggerReason, hardCash, hardPct, givePct, instanceId, algo, totalDrawdownHit.get()));
        }
    }

    private void handleUnrealizedDrawdownDetection(boolean hardCash, boolean hardPct, boolean givePct) {
        if (!unrealizedDrawdownActionTaken.get()) {
            final String finalDetectionReason = buildReasonString(hardCash, hardPct, givePct);
            
            logger.log(String.format(
                "UNREAL DRAWDOWN DETECTED (ACTION DISABLED BY CONFIG): Reason: %s, hardCash=%b, hardPct=%b, givePct=%b. Instance: %s, Algo: %s. TotalDDHit: %b",
                finalDetectionReason, hardCash, hardPct, givePct, instanceId, algo, totalDrawdownHit.get()));
        }
    }

    private String buildReasonString(boolean hardCash, boolean hardPct, boolean givePct) {
        if (!hardCash && !hardPct && !givePct) {
            return UNREAL_DD_OTHER;
        }
        
        final StringBuilder sb = new StringBuilder(64); // Pre-size for efficiency
        if (hardCash) sb.append(UNREAL_DD_HARD_CASH).append("; ");
        if (hardPct) sb.append(UNREAL_DD_HARD_PCT).append("; ");
        if (givePct) sb.append(UNREAL_DD_GIVE_BACK).append("; ");
        
        return sb.substring(0, sb.length() - 2); // Remove trailing "; "
    }

    private void performThrottledLogging(double realised, double unrealised, double pt, double pg) {
        if (logger.isDebugEnabled()) {
            final long now = System.currentTimeMillis();
            if (now - lastLog >= LOG_THROTTLE_MS) {
                lastLog = now;
                logger.log(String.format(
                    "[DDG_STATE] R=%.2f U=%.2f | PeakTot=%.2f PeakGain=%.2f | TotHit=%s UnrHit=%s CntAct=%d CntDet=%d SnapValT=%.2f SnapValU=%.2f SnapPctT=%.2f%% SnapPctU=%.2f%%",
                    realised, unrealised, pt, pg,
                    totalDrawdownHit.get(), unrealizedDrawdownActionTaken.get(),
                    unrealizedDrawdownFlattenCount.get(), unrealizedDrawdownDetectionCount.get(),
                    currentTotalDrawdownValueSnapshot, currentUnrealizedDrawdownValueSnapshot,
                    currentTotalDrawdownPercentSnapshot * 100, currentUnrealizedDrawdownPercentSnapshot * 100));
            }
        }
    }

    private void updateSnapshots(double realised, double unrealised, double currentTotal, 
                               double pt, double pg, boolean neverGreen) {
        // Total drawdown value
        this.currentTotalDrawdownValueSnapshot = (pt == Double.NEGATIVE_INFINITY) ? 0.0 : currentTotal - pt;

        // Unrealized drawdown value  
        this.currentUnrealizedDrawdownValueSnapshot = (pg == Double.NEGATIVE_INFINITY) ? 0.0 : unrealised - pg;

        // Total drawdown percent
        if (pt == Double.NEGATIVE_INFINITY || pt <= 0) {
            this.currentTotalDrawdownPercentSnapshot = (currentTotal < 0 && entryNotional > 0) ? 
                (-currentTotal) / entryNotional : 0.0;
        } else {
            final double totalDrawdownVal = pt - currentTotal;
            this.currentTotalDrawdownPercentSnapshot = (totalDrawdownVal <= 0) ? 0.0 : totalDrawdownVal / pt;
        }

        // Unrealized drawdown percent
        if (entryNotional <= 0) {
            this.currentUnrealizedDrawdownPercentSnapshot = 0.0;
        } else if (neverGreen) {
            this.currentUnrealizedDrawdownPercentSnapshot = (unrealised < 0) ? 
                (-unrealised) / entryNotional : 0.0;
        } else if (pg <= 0) {
            this.currentUnrealizedDrawdownPercentSnapshot = 0.0;
        } else {
            final double drawdownFromPeak = pg - unrealised;
            this.currentUnrealizedDrawdownPercentSnapshot = (drawdownFromPeak <= 0) ? 
                0.0 : drawdownFromPeak / pg;
        }
    }

    public void incrementUnrealizedDrawdownFlattenCount() {
        unrealizedDrawdownFlattenCount.incrementAndGet();
    }

    public boolean isTripped() { return totalDrawdownHit.get() || unrealizedDrawdownActionTaken.get(); }

    public boolean hasTotalDrawdownHit() {
        return totalDrawdownHit.get();
    }

    public boolean hasUnrealizedDrawdownActionTaken() {
        return unrealizedDrawdownActionTaken.get(); // MODIFIED: Independent of totalDrawdownHit
    }

    public int getUnrealizedDrawdownFlattenCount() {
        return unrealizedDrawdownFlattenCount.get();
    }

    public int getUnrealizedDrawdownDetectionCount() { // New getter
        return unrealizedDrawdownDetectionCount.get();
    }

    // Simplified Getters: Return pre-calculated snapshot values
    public double getCurrentTotalDrawdownValue() {
        return this.currentTotalDrawdownValueSnapshot;
    }

    public double getCurrentUnrealizedDrawdownValue() {
        return this.currentUnrealizedDrawdownValueSnapshot;
    }

    public double getCurrentTotalDrawdownPercent() {
        return this.currentTotalDrawdownPercentSnapshot;
    }

    public double getCurrentUnrealizedDrawdownPercent() {
        return this.currentUnrealizedDrawdownPercentSnapshot;
    }

    public String getTotalDrawdownTriggerReason() {
        return totalDrawdownTriggerReason;
    }

    public String getUnrealizedDrawdownTriggerReason() {
        return unrealizedDrawdownTriggerReason;
    }

   /** full end‑of‑day reset (clears notional & counters). */
    public synchronized void resetDaily() {
        peakGain.set(Double.NEGATIVE_INFINITY);
        peakLoss.set(Double.POSITIVE_INFINITY);
        peakTotal.set(Double.NEGATIVE_INFINITY);
        totalDrawdownHit.set(false);
        unrealizedDrawdownActionTaken.set(false);
        unrealizedDrawdownFlattenCount.set(0);
        unrealizedDrawdownDetectionCount.set(0);
        entryNotional = 0.0;
        totalDrawdownTriggerReason = "";
        unrealizedDrawdownTriggerReason = "";

        // Reset snapshot values
        this.currentTotalDrawdownValueSnapshot = 0.0;
        this.currentUnrealizedDrawdownValueSnapshot = 0.0;
        this.currentTotalDrawdownPercentSnapshot = 0.0;
        this.currentUnrealizedDrawdownPercentSnapshot = 0.0;
    }

    /** Reset per‑trade state ONLY for unrealised protections. */
    public void resetUnrealizedTrackingForNewTrade() {
        peakGain.set(Double.NEGATIVE_INFINITY);
        peakLoss.set(Double.POSITIVE_INFINITY);
        /* DO NOT RESET peakTotal. It tracks overall daily equity peak. */
        /* DO NOT RESET totalDrawdownHit. It's a daily limit. */
        unrealizedDrawdownActionTaken.set(false); // Reset for the new trade
        /* DO NOT RESET unrealizedDrawdownFlattenCount. It's a daily counter. */
        /* DO NOT RESET unrealizedDrawdownDetectionCount. It's a daily counter. */
        // entryNotional will be set by armForNewPosition()
        unrealizedDrawdownTriggerReason = "";

        // Reset only unrealized snapshot values related to a single trade's lifecycle
        this.currentUnrealizedDrawdownValueSnapshot = 0.0;
        this.currentUnrealizedDrawdownPercentSnapshot = 0.0;
        // Total drawdown snapshots are daily and will be recalculated by the next 'update' call
        // or reset by resetDaily(). No specific reset here for them beyond what resetDaily does.
    }

    /** Capture entry notional and reset peaks. */
    public void armForNewPosition(double entryPrice, long qtyAbs, double pointValue) {
        if (pointValue <= 0) {
            logger.log(String.format(
                "[DDG_ERROR] Inst: %s | Algo: %s | Invalid pointValue (%.2f) in armForNewPosition. Must be positive. Drawdown calculations might be incorrect.",
                instanceId, algo, pointValue));
            // Depending on severity, could throw IllegalArgumentException here
            // For now, logging and continuing, but this is a problematic state.
            // throw new IllegalArgumentException("pointValue must be positive for armForNewPosition.");
        }
        if (qtyAbs <= 0) {
             logger.log(String.format(
                "[DDG_WARN] Inst: %s | Algo: %s | qtyAbs (%d) is zero or negative in armForNewPosition. Entry notional will be zero or negative.",
                instanceId, algo, qtyAbs));
        }

        resetUnrealizedTrackingForNewTrade(); // Reset unrealized PnL tracking for the new trade
        entryNotional = Math.abs(entryPrice * qtyAbs * pointValue);
        peakGain.set(Double.NEGATIVE_INFINITY);
        peakLoss.set(Double.POSITIVE_INFINITY);
        unrealizedDrawdownActionTaken.set(false);
        unrealizedDrawdownTriggerReason = ""; // Reset reason
        // Reset detection count when arming for new position
       // unrealizedDrawdownDetectionCount.set(0); 
    }

    public void updateEntryNotional(double newNotional) {
        if (newNotional > 0) {
            entryNotional = newNotional;
        }
    }
} 