package com.bookmap.api.simple.demo.indicators;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Debug test to understand why the ready state is not working as expected.
 */
public class ReadyStateDebugTest {
    
    private BucketedAverageWithSTDBands bands;
    
    private static final double STD_MULTIPLIER = 2.0;
    private static final int WINDOW_SIZE_SECS = 30;
    private static final long NANOS_PER_SEC = 1_000_000_000L;
    private static final double BASE_PRICE = 100.0;
    
    @Before
    public void setup() {
        bands = new BucketedAverageWithSTDBands(STD_MULTIPLIER, WINDOW_SIZE_SECS);
        bands.setExtraDebugMode(true);
    }
    
    @Test
    public void testReadyStateProgression() {
        System.out.println("===== READY STATE DEBUG TEST =====");
        
        long baseTime = 1000 * NANOS_PER_SEC;
        
        // Add data points and check ready state progression
        for (int i = 0; i < 50; i++) {
            long timestamp = baseTime + (i * NANOS_PER_SEC);
            double price = BASE_PRICE + (i * 0.01); // Small incremental changes
            
            System.out.println(String.format("\n--- Update %d: t=%d, price=%.2f ---", 
                                            i, timestamp / NANOS_PER_SEC, price));
            
            bands.updatePrice(price, timestamp);
            
            System.out.println("  Ready: " + bands.isReady());
            System.out.println("  Valid: " + bands.isValid());
            System.out.println("  Total samples: " + bands.getSampleCount());
            System.out.println("  Bucket count: " + bands.getBucketCount());
            System.out.println("  Window size (ns): " + bands.getWindowSizeNanos());
            
            if (i % 10 == 9) { // Every 10th iteration, dump buckets
                System.out.println("Bucket dump at iteration " + i + ":");
                System.out.println(bands.dumpBuckets());
            }
            
            // Check band crossing once ready
            if (bands.isReady() && bands.isValid()) {
                System.out.println("  Above upper: " + bands.isAboveUpperBand());
                System.out.println("  Below lower: " + bands.isBelowLowerBand());
                System.out.println("  Upper band: " + bands.getUpperBand());
                System.out.println("  Lower band: " + bands.getLowerBand());
                System.out.println("  Mean: " + bands.getAverage());
                break; // Stop once we reach ready state
            }
        }
        
        System.out.println("\nFinal state:");
        System.out.println("Ready: " + bands.isReady());
        System.out.println("Valid: " + bands.isValid());
        System.out.println("Sample count: " + bands.getSampleCount());
        System.out.println("Bucket count: " + bands.getBucketCount());
        
        // Final bucket dump
        System.out.println("\nFinal bucket dump:");
        System.out.println(bands.dumpBuckets());
    }
    
    @Test
    public void testBucketSizeImpactOnReadyState() {
        System.out.println("===== BUCKET SIZE IMPACT TEST =====");
        
        // Test with smaller bucket size
        bands.setBucketSizeNanos(1_000_000_000L); // 1 second buckets
        bands.setMinSamplesPerBucket(1); // Only 1 sample per bucket needed
        
        long baseTime = 1000 * NANOS_PER_SEC;
        
        for (int i = 0; i < 35; i++) { // 35 seconds of data for 30 second window
            long timestamp = baseTime + (i * NANOS_PER_SEC);
            double price = BASE_PRICE + (i * 0.01);
            
            bands.updatePrice(price, timestamp);
            
            if (i % 5 == 4) {
                System.out.println(String.format("After %d updates: Ready=%b, Valid=%b, Samples=%d, Buckets=%d", 
                                                i+1, bands.isReady(), bands.isValid(), 
                                                bands.getSampleCount(), bands.getBucketCount()));
            }
        }
        
        System.out.println("\nWith 1-second buckets:");
        System.out.println("Ready: " + bands.isReady());
        System.out.println("Valid: " + bands.isValid());
        System.out.println("Sample count: " + bands.getSampleCount());
        System.out.println("Bucket count: " + bands.getBucketCount());
        System.out.println("\nBucket dump:");
        System.out.println(bands.dumpBuckets());
    }
}
