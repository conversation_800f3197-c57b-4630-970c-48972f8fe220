import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.util.Optional;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentSkipListMap;

import static org.junit.jupiter.api.Assertions.*;

// Assuming LiquidityClusterAnalyzer and LiquidityCluster are accessible for testing.
// For simplicity, if they are inner classes of MultiLevelOrderBookExpwithQDSelfCalcV3,
// you might need a MultiLevelOrderBookExpwithQDSelfCalcV3 instance to create an Analyzer instance,
// or make the Analyzer static or top-level for easier testing if its dependencies on the outer class are manageable.
// For these tests, we'll assume we can instantiate LiquidityClusterAnalyzer directly by providing all its dependencies.

// You would need to have the actual MultiLevelOrderBookExpwithQDSelfCalcV3.java file (with its inner classes)
// in your test classpath. The following is a placeholder for where those classes would be defined
// if not in a separate file visible to the test.
/*
// Placeholder for where MultiLevelOrderBookExpwithQDSelfCalcV3 and its inner classes would be defined or imported
class MultiLevelOrderBookExpwithQDSelfCalcV3 {
    // Minimal structure to allow LiquidityClusterAnalyzer instantiation if it accesses outer class fields
    // (Ideally, LiquidityClusterAnalyzer is self-contained post-construction)
    public int clusterRadiusTicksConfig = 2;
    public long minClusterCumSizeConfig = 200;
    public int maxTargetsPerSideConfig = 3;
    public double decayFactorClustersConfig = 0.85; // This should be the one used by Analyzer
    public long crossoverWindowMillisConfig = 5000;
    public double crossoverThresholdConfig = 0.60;
    public int mergeMaxGapTicksConfig = 1; // New config for merging

    // Mocked or simplified dependencies for the Analyzer
    NavigableMap<Integer, Integer> bidPriceDepths = new TreeMap<>((a, b) -> b - a);
    NavigableMap<Integer, Integer> askPriceDepths = new TreeMap<>();
    Map<Integer, Long> lastUpdateNanos = new ConcurrentSkipListMap<>();


    public class LiquidityClusterAnalyzer {
        // Fields to store configuration passed via constructor
        final NavigableMap<Integer, Integer> bidDepth;
        final NavigableMap<Integer, Integer> askDepth;
        final Map<Integer, Long> lastUpdateNanos; // Make sure this is the one used
        final int clusterRadiusTicks;
        final long minClusterCumSize;
        final int maxTargetsPerSide;
        final double decayFactor; // This is the cluster-specific decay factor
        final long crossoverWindowNanos;
        final double crossoverThreshold;
        final int mergeMaxGapTicks; // New field

        // Placeholder constructor - actual one from your patched code
        public LiquidityClusterAnalyzer(NavigableMap<Integer, Integer> bidDepth,
                                        NavigableMap<Integer, Integer> askDepth,
                                        Map<Integer, Long> lastUpdateNanos,
                                        int clusterRadiusTicksParam, long minClusterCumSizeParam, int maxTargetsPerSideParam,
                                        double decayFactorParam, long crossoverWindowMillisParam, double crossoverThresholdParam,
                                        int mergeMaxGapTicksParam) { // Added mergeMaxGapTicksParam
            this.bidDepth = bidDepth;
            this.askDepth = askDepth;
            this.lastUpdateNanos = lastUpdateNanos;
            this.clusterRadiusTicks = clusterRadiusTicksParam;
            this.minClusterCumSize = minClusterCumSizeParam;
            this.maxTargetsPerSide = maxTargetsPerSideParam;
            this.decayFactor = decayFactorParam;
            this.crossoverWindowNanos = crossoverWindowMillisParam * 1_000_000L;
            this.crossoverThreshold = crossoverThresholdParam;
            this.mergeMaxGapTicks = mergeMaxGapTicksParam;
        }

        // Placeholder for LiquidityCluster inner class - actual one from your patched code
        public class LiquidityCluster {
            public final int startPrice, endPrice, centerPrice;
            public final long cumSize, peakLevelSize;
            public final int numDistinctLevels;
            public final int vwapClusterTenths;

            public LiquidityCluster(int minP, int maxP, int c, long cu, long pk, int distinctLevels, int vwapTenths) {
                this.startPrice = minP; this.endPrice = maxP; this.centerPrice = c;
                this.cumSize = cu; this.peakLevelSize = pk;
                this.numDistinctLevels = distinctLevels; this.vwapClusterTenths = vwapTenths;
            }
             @Override public String toString() { // For easy debugging
                 return String.format("Cluster[priceRange=%d-%d, center=%d, vwap=%.1f, cumSize=%d, peak=%d, levels=%d]",
                                      startPrice, endPrice, centerPrice, vwapClusterTenths/10.0, cumSize, peakLevelSize, numDistinctLevels);
             }
        }
        // ... (compute, recalculateClusterProperties, pass methods as per your implementation)
        // For testing, these methods need to be present and correct.
        // public List<LiquidityCluster> getSellTargets(int bestAsk, long ts) { return compute(false, bestAsk, ts); }
        // public List<LiquidityCluster> getBuyTargets(int bestBid, long ts) { return compute(true, bestBid, ts); }
        // private List<LiquidityCluster> compute(boolean bidSide, int ref, long now) { ... }
        // private LiquidityCluster recalculateClusterProperties(...) { ... }
        // private boolean pass(...) { ... }
    }
}
*/

class LiquidityClusterAnalyzerTest {

    // --- Default Configuration Parameters (ES-centric starting points) ---
    // These would be instance fields in MultiLevelOrderBookExpwithQDSelfCalcV3
    // For testing, we pass them directly to the Analyzer's constructor.
    private int clusterRadiusTicks = 2;
    private long minClusterCumSize = 200;
    private int maxTargetsPerSide = 3;
    private double decayFactorClusters = 0.85; // Cluster-specific decay
    private long crossoverWindowMillis = 5000;
    private double crossoverThreshold = 0.60;
    private int mergeMaxGapTicks = 1; // Allow merging if 0 or 1 tick apart

    private NavigableMap<Integer, Integer> bids;
    private NavigableMap<Integer, Integer> asks;
    private Map<Integer, Long> lastUpdateNanosMap;
    private long currentTimeNanos;

    // Assuming LiquidityClusterAnalyzer is an inner class, we might need an outer instance
    // Or, ideally, the Analyzer is refactored slightly to not need Outer.this access if it did.
    // For this test structure, we assume we can instantiate LiquidityClusterAnalyzer directly with all its needed config.
    // If MultiLevelOrderBookExpwithQDSelfCalcV3.this.someConfig is used internally by Analyzer's compute,
    // then an instance of the outer class would be needed here.
    // The patch design had the background task create a *local* analyzer passing configs,
    // so the analyzer should use its *own* fields set via constructor.

    // MultiLevelOrderBookExpwithQDSelfCalcV3 outerInstanceForAnalyzer; // If needed for Outer.this access
    MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer analyzer;


    @BeforeEach
    void setUp() {
        bids = new TreeMap<>((a, b) -> b - a); // Price descending
        asks = new TreeMap<>(); // Price ascending
        lastUpdateNanosMap = new ConcurrentSkipListMap<>(); // Using ConcurrentSkipListMap as in main code
        currentTimeNanos = System.nanoTime(); // Or a fixed reference time for predictable tests

        // Re-initialize analyzer with default parameters for each test, or modify parameters per test
        // outerInstanceForAnalyzer = new MultiLevelOrderBookExpwithQDSelfCalcV3(...); // If needed
        analyzer = new MultiLevelOrderBookExpwithQDSelfCalcV3().new LiquidityClusterAnalyzer( // Example instantiation assuming outer needed
        // analyzer = new LiquidityClusterAnalyzer( // Simpler instantiation if analyzer is made static or dependencies are all passed
                bids, asks, lastUpdateNanosMap,
                clusterRadiusTicks, minClusterCumSize, maxTargetsPerSide,
                decayFactorClusters, crossoverWindowMillis, crossoverThreshold,
                mergeMaxGapTicks
        );
    }

    private void addLiquidity(boolean isBid, int price, int size, long updateTimeNanos) {
        if (isBid) {
            bids.put(price, size);
        } else {
            asks.put(price, size);
        }
        lastUpdateNanosMap.put(price, updateTimeNanos);
    }

    private void addFreshLiquidity(boolean isBid, int price, int size) {
        addLiquidity(isBid, price, size, currentTimeNanos);
    }

    private void addOldLiquidity(boolean isBid, int price, int size, long ageMillis) {
        addLiquidity(isBid, price, size, currentTimeNanos - TimeUnit.MILLISECONDS.toNanos(ageMillis));
    }

    @Test
    @DisplayName("Should return no clusters for empty order book")
    void testEmptyOrderBook() {
        List<MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster> askClusters = analyzer.getSellTargets(1000, currentTimeNanos);
        assertTrue(askClusters.isEmpty(), "Ask clusters should be empty");

        List<MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster> bidClusters = analyzer.getBuyTargets(999, currentTimeNanos);
        assertTrue(bidClusters.isEmpty(), "Bid clusters should be empty");
    }

    @Test
    @DisplayName("Should detect a single significant fresh cluster on ask side")
    void testSingleFreshAskCluster() {
        // Liquidity: 100 @ 1001, 150 @ 1002, 100 @ 1003 (Total 350)
        // Assuming minClusterCumSize = 200, clusterRadiusTicks = 2 (window 5 ticks)
        addFreshLiquidity(false, 1001, 100);
        addFreshLiquidity(false, 1002, 150); // Peak
        addFreshLiquidity(false, 1003, 100);
        // Add some distant, small liquidity to ensure it's not picked up
        addFreshLiquidity(false, 1010, 10);

        // Analyzer parameters for this test
        analyzer = new MultiLevelOrderBookExpwithQDSelfCalcV3().new LiquidityClusterAnalyzer(
                bids, asks, lastUpdateNanosMap,
                2, 200, 1, 0.85, 5000, 0.60, 1);

        List<MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster> clusters = analyzer.getSellTargets(1000, currentTimeNanos);
        // System.out.println("Ask Clusters: " + clusters);

        assertEquals(1, clusters.size(), "Should detect one cluster");
        MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster c = clusters.get(0);

        assertEquals(1001, c.startPrice, "Cluster start price"); // Assuming recalculateProperties normalizes
        assertEquals(1003, c.endPrice, "Cluster end price");
        assertTrue(c.cumSize >= 200, "Cumulative size should meet threshold"); // Exact value depends on smoothing & recalculation
        assertEquals(150, c.peakLevelSize, "Peak level size"); // Assuming no decay for fresh
        assertEquals(1002, c.centerPrice, "Center price should be weighted towards 1002"); // Or c.vwapClusterTenths / 10.0
        assertEquals(10020, c.vwapClusterTenths, "VWAP should be around 1002.0");
        assertEquals(3, c.numDistinctLevels, "Number of distinct levels");
    }

    @Test
    @DisplayName("Should detect a single significant fresh cluster on bid side")
    void testSingleFreshBidCluster() {
        addFreshLiquidity(true, 999, 100);
        addFreshLiquidity(true, 998, 150); // Peak
        addFreshLiquidity(true, 997, 100);
        addFreshLiquidity(true, 990, 10);

        analyzer = new MultiLevelOrderBookExpwithQDSelfCalcV3().new LiquidityClusterAnalyzer(
            bids, asks, lastUpdateNanosMap, 2, 200, 1, 0.85, 5000, 0.60, 1);

        List<MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster> clusters = analyzer.getBuyTargets(1000, currentTimeNanos);
        // System.out.println("Bid Clusters: " + clusters);

        assertEquals(1, clusters.size());
        MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster c = clusters.get(0);
        assertEquals(997, c.startPrice, "Cluster start price (min for bids)");
        assertEquals(999, c.endPrice, "Cluster end price (max for bids)");
        assertTrue(c.cumSize >= 200);
        assertEquals(150, c.peakLevelSize);
        assertEquals(998, c.centerPrice);
        assertEquals(9980, c.vwapClusterTenths);
        assertEquals(3, c.numDistinctLevels);
    }

    @Nested
    @DisplayName("Time Decay Tests")
    class TimeDecayTests {
        @Test
        @DisplayName("Old liquidity should result in smaller effective cluster size")
        void testOldLiquidityImpact() {
            long veryOldAgeMillis = 60000; // 1 minute, assuming decayFactor 0.85, this will be heavily decayed
            addOldLiquidity(false, 1001, 200, veryOldAgeMillis);
            addOldLiquidity(false, 1002, 300, veryOldAgeMillis); // Original peak
            addOldLiquidity(false, 1003, 200, veryOldAgeMillis);

            // With high minClusterCumSize, this decayed cluster might not appear
            analyzer = new MultiLevelOrderBookExpwithQDSelfCalcV3().new LiquidityClusterAnalyzer(
                bids, asks, lastUpdateNanosMap, 2, 50, 1, 0.85, 5000, 0.60, 1); // Lower minCumSize for test

            List<MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster> clusters = analyzer.getSellTargets(1000, currentTimeNanos);
            
            if (!clusters.isEmpty()) {
                MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster c = clusters.get(0);
                // Original sum was 200+300+200 = 700. With decayFactor=0.85, age=60s (many decay periods)
                // (0.85^60) is very small. The effective cumSize should be much less than 700.
                // Example: Math.pow(0.85, 60) approx 0.0001. So cumSize would be tiny.
                // This test needs careful calculation of expected decayed size or assert it's significantly smaller.
                // For simplicity, let's make age smaller for a less drastic decay for assertion.
                addLiquidity(false, 1005, 200, currentTimeNanos - TimeUnit.SECONDS.toNanos(10)); // 10s old
                addLiquidity(false, 1006, 300, currentTimeNanos - TimeUnit.SECONDS.toNanos(10));
                addLiquidity(false, 1007, 200, currentTimeNanos - TimeUnit.SECONDS.toNanos(10));
                
                clusters = analyzer.getSellTargets(1004, currentTimeNanos); // Update ref
                // System.out.println("Old Liquidity Clusters: " + clusters);
                assertFalse(clusters.isEmpty(), "Should still detect a cluster if minCumSize is low enough");
                MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster cOld = clusters.get(0);

                long expectedPeakAfter10sDecay = (long) (300 * Math.pow(0.85, 10)); // Approx 300 * 0.196 = 58
                assertTrue(cOld.peakLevelSize <= expectedPeakAfter10sDecay +1 && cOld.peakLevelSize >= expectedPeakAfter10sDecay -1 , "Peak size should be decayed.");
                assertTrue(cOld.cumSize < 700 * 0.5, "Cumulative size should be significantly decayed"); // Rough check
            } else {
                 fail("Cluster expected even with decay, check parameters or decay math");
            }
        }
    }

    @Nested
    @DisplayName("Crossover Filter Tests")
    class CrossoverFilterTests {
        // These tests are more complex as they require simulating time passing and history.
        // They would involve calling compute multiple times with advancing currentTimeNanos
        // and changing liquidity to test the pass() method's history mechanism.

        @Test
        @DisplayName("Transient cluster (shrinks significantly) should be filtered by crossover")
        void testTransientClusterFiltering() {
            // Setup: Create LiquidityClusterAnalyzer with specific crossover settings
            long windowMillis = 100; // Short window for test
            double threshold = 0.5; // Needs to maintain 50% of peak
             analyzer = new MultiLevelOrderBookExpwithQDSelfCalcV3().new LiquidityClusterAnalyzer(
                bids, asks, lastUpdateNanosMap, 1, 50, 1, 0.85, windowMillis, threshold, 0);

            // Time 0: Strong cluster
            long t0 = currentTimeNanos;
            addFreshLiquidity(false, 1001, 100); // For cluster at 1001
            analyzer.getSellTargets(1000, t0); // Call to populate history

            // Time 1 (e.g., t0 + 30ms): Still strong
            long t1 = t0 + TimeUnit.MILLISECONDS.toNanos(30);
            asks.clear(); lastUpdateNanosMap.clear(); // Reset book for simplicity or update existing
            addLiquidity(false, 1001, 90, t1);
            List<MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster> clustersT1 = analyzer.getSellTargets(1000, t1);
            assertFalse(clustersT1.isEmpty(), "Cluster should exist at t1");
            long sizeAtT1 = clustersT1.get(0).cumSize;

            // Time 2 (e.g., t0 + 60ms): Cluster shrinks significantly (below 50% of peak at t0/t1)
            long t2 = t0 + TimeUnit.MILLISECONDS.toNanos(60);
            asks.clear(); lastUpdateNanosMap.clear();
            addLiquidity(false, 1001, 20, t2); // Much smaller size
            List<MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster> clustersT2 = analyzer.getSellTargets(1000, t2);
             // Peak in window was around 90-100. Current is 20. 20 < 0.5 * 90. Should be filtered.
            assertTrue(clustersT2.isEmpty(), "Shrunken cluster should be filtered out by crossover");

            // Time 3 (e.g., t0 + 150ms, outside window): Old peak forgotten, new small cluster might appear if it meets minClusterCumSize
            long t3 = t0 + TimeUnit.MILLISECONDS.toNanos(150);
            asks.clear(); lastUpdateNanosMap.clear();
            addLiquidity(false, 1001, 25, t3); // Small but potentially above a very low minClusterCumSize
            analyzer = new MultiLevelOrderBookExpwithQDSelfCalcV3().new LiquidityClusterAnalyzer( // re-init with low minClusterCumSize
                bids, asks, lastUpdateNanosMap, 1, 10, 1, 0.85, windowMillis, threshold, 0);
            List<MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster> clustersT3 = analyzer.getSellTargets(1000, t3);
            assertFalse(clustersT3.isEmpty(), "Small cluster might reappear if old history expired and it meets min size");
        }
    }

    @Nested
    @DisplayName("Cluster Merging Tests")
    class ClusterMergingTests {
        @Test
        @DisplayName("Should not merge distant clusters")
        void testNoMergeDistantClusters() {
            addFreshLiquidity(false, 1001, 100);
            addFreshLiquidity(false, 1002, 100); // Cluster 1

            addFreshLiquidity(false, 1010, 100);
            addFreshLiquidity(false, 1011, 100); // Cluster 2

            analyzer = new MultiLevelOrderBookExpwithQDSelfCalcV3().new LiquidityClusterAnalyzer(
                bids, asks, lastUpdateNanosMap, 1, 150, 2, 0.85, 5000, 0.60, 1); // mergeMaxGapTicks = 1

            List<MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster> clusters = analyzer.getSellTargets(1000, currentTimeNanos);
            // System.out.println("No Merge Distant: " + clusters);
            assertEquals(2, clusters.size(), "Should find two distinct clusters");
        }

        @Test
        @DisplayName("Should merge touching clusters")
        void testMergeTouchingClusters() {
            // Cluster 1: 1001-1002
            addFreshLiquidity(false, 1001, 150);
            addFreshLiquidity(false, 1002, 150);
            // Cluster 2: 1003-1004 (touches C1 if end of C1 is 1002 and start of C2 is 1003)
            addFreshLiquidity(false, 1003, 150);
            addFreshLiquidity(false, 1004, 150);

            // mergeMaxGapTicks = 0 means only direct touching or overlap
            // mergeMaxGapTicks = 1 means can be 1 tick apart (e.g. C1 ends 1002, C2 starts 1004)
            // The patch logic was: asks: c1.endPrice + currentMergeMaxGapTicks >= c2.startPrice
            // If C1 ends 1002, C2 starts 1003. mergeMaxGapTicks=0. 1002 + 0 >= 1003 (false) -> Need to fix merge condition for "touching"
            // Gap was: next.startPrice - current.endPrice - 1. For touching, gap = 1003 - 1002 - 1 = 0.
            // Merge if gap <= mergeMaxGapTicks. So if mergeMaxGapTicks = 0, touching merges.
            analyzer = new MultiLevelOrderBookExpwithQDSelfCalcV3().new LiquidityClusterAnalyzer(
                bids, asks, lastUpdateNanosMap, 1, 250, 1, 0.85, 5000, 0.60, 0); // mergeMaxGapTicks = 0

            List<MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster> clusters = analyzer.getSellTargets(1000, currentTimeNanos);
            // System.out.println("Merge Touching: " + clusters);
            assertEquals(1, clusters.size(), "Clusters should merge");
            MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster merged = clusters.get(0);
            assertEquals(1001, merged.startPrice);
            assertEquals(1004, merged.endPrice);
            // cumSize should be sum of all 4 levels (150*4 = 600, assuming no decay/smoothing loss)
            // This depends heavily on recalculateClusterProperties
            assertTrue(merged.cumSize > 550 && merged.cumSize <= 600, "Merged cumSize should be sum of components");
            assertEquals(4, merged.numDistinctLevels, "Merged distinct levels");
            // VWAP would be (1001+1002+1003+1004)/4 * 150 / (4*150) = average of prices
            double expectedVwap = (1001.0+1002.0+1003.0+1004.0)/4.0; // = 1002.5
            assertEquals((int)Math.round(expectedVwap*10), merged.vwapClusterTenths, "Merged VWAP");
        }

        @Test
        @DisplayName("Should merge clusters with 1 tick gap if mergeMaxGapTicks allows")
        void testMergeWithOneTickGap() {
            addFreshLiquidity(false, 1001, 150); // Cluster 1
            addFreshLiquidity(false, 1002, 150);
            // Gap at 1003
            addFreshLiquidity(false, 1004, 150); // Cluster 2
            addFreshLiquidity(false, 1005, 150);

            analyzer = new MultiLevelOrderBookExpwithQDSelfCalcV3().new LiquidityClusterAnalyzer(
                bids, asks, lastUpdateNanosMap, 1, 250, 1, 0.85, 5000, 0.60, 1); // mergeMaxGapTicks = 1

            List<MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster> clusters = analyzer.getSellTargets(1000, currentTimeNanos);
            // System.out.println("Merge 1-tick gap: " + clusters);
            assertEquals(1, clusters.size(), "Clusters should merge across 1 tick gap");
            MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster merged = clusters.get(0);
            assertEquals(1001, merged.startPrice);
            assertEquals(1005, merged.endPrice); // Price 1003 is part of range, but has 0 size for recalculation
            assertEquals(4, merged.numDistinctLevels, "Should count 4 levels with liquidity");
        }

         @Test
        @DisplayName("Should not merge clusters with 2 tick gap if mergeMaxGapTicks is 1")
        void testNoMergeWithTwoTickGap() {
            addFreshLiquidity(false, 1001, 150); 
            addFreshLiquidity(false, 1002, 150); // Cluster 1 (ends 1002)
            // Gap at 1003, 1004
            addFreshLiquidity(false, 1005, 150); // Cluster 2 (starts 1005)
            addFreshLiquidity(false, 1006, 150);

            analyzer = new MultiLevelOrderBookExpwithQDSelfCalcV3().new LiquidityClusterAnalyzer(
                bids, asks, lastUpdateNanosMap, 1, 250, 2, 0.85, 5000, 0.60, 1); // mergeMaxGapTicks = 1

            List<MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster> clusters = analyzer.getSellTargets(1000, currentTimeNanos);
            // System.out.println("No Merge 2-tick gap: " + clusters);
            assertEquals(2, clusters.size(), "Clusters should NOT merge across 2 tick gap with mergeMaxGapTicks=1");
        }

        @Test
        @DisplayName("Should perform chain merging (A-B, then AB-C)")
        void testChainMerging() {
            addFreshLiquidity(false, 1001, 100); // A
            addFreshLiquidity(false, 1002, 100); 
            addFreshLiquidity(false, 1003, 100); // B (touches A)
            addFreshLiquidity(false, 1004, 100);
            addFreshLiquidity(false, 1005, 100); // C (touches B)
            addFreshLiquidity(false, 1006, 100);

            analyzer = new MultiLevelOrderBookExpwithQDSelfCalcV3().new LiquidityClusterAnalyzer(
                bids, asks, lastUpdateNanosMap, 1, 150, 1, 0.85, 5000, 0.60, 0); // mergeMaxGapTicks = 0 for touching

            List<MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster> clusters = analyzer.getSellTargets(1000, currentTimeNanos);
            // System.out.println("Chain Merge: " + clusters);
            assertEquals(1, clusters.size(), "All three clusters should merge into one");
            MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster merged = clusters.get(0);
            assertEquals(1001, merged.startPrice);
            assertEquals(1006, merged.endPrice);
            assertEquals(6, merged.numDistinctLevels);
            assertTrue(merged.cumSize >= 550 && merged.cumSize <= 600);
        }
    }

    @Test
    @DisplayName("Parameter Test: maxTargetsPerSide should truncate results")
    void testMaxTargets() {
        // Create 3 distinct clusters
        addFreshLiquidity(false, 1001, 200); // C1 @ 1001 (closest to ref 1000)
        addFreshLiquidity(false, 1005, 200); // C2 @ 1005
        addFreshLiquidity(false, 1010, 200); // C3 @ 1010

        analyzer = new MultiLevelOrderBookExpwithQDSelfCalcV3().new LiquidityClusterAnalyzer(
            bids, asks, lastUpdateNanosMap, 1, 150, 2, 0.85, 5000, 0.60, 0); // maxTargetsPerSide = 2

        List<MultiLevelOrderBookExpwithQDSelfCalcV3.LiquidityClusterAnalyzer.LiquidityCluster> clusters = analyzer.getSellTargets(1000, currentTimeNanos);
        // System.out.println("Max Targets: " + clusters);
        assertEquals(2, clusters.size(), "Should only return maxTargetsPerSide clusters");
        // Clusters are sorted by distance to ref. ref=1000.
        // C1 center ~1001 (dist 1)
        // C2 center ~1005 (dist 5)
        // C3 center ~1010 (dist 10)
        assertEquals(1001, clusters.get(0).startPrice); // C1
        assertEquals(1005, clusters.get(1).startPrice); // C2
    }
    
    // TODO: Add more tests:
    // - Varying clusterRadiusTicks and observing smoothing effect
    // - Varying minClusterCumSize and observing filtering effect
    // - More complex book structures for merging (e.g., ensure properties of merged clusters are spot on)
    // - Tests for recalculateClusterProperties directly if made accessible or through compute's results
    // - Test specific edge cases for pass() method if its logic is complex
    // - Test with LIQUIDITY_SCAN_DEPTH_TICKS limits
}