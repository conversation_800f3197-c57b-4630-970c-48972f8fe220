package com.bookmap.api.simple.demo.indicators;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

/**
 * Comprehensive test suite for BucketedAverageWithSTDBands class.
 * Tests all major functionality including statistical calculations, band crossing detection,
 * bucketing mechanisms, edge cases, and configuration parameters.
 */
public class BucketedAverageWithSTDBandsTest {
    
    // Test constants
    private static final double STD_MULTIPLIER = 2.0;
    private static final int WINDOW_SIZE_SECS = 10;
    private static final long NANOS_PER_SEC = 1_000_000_000L;
    private static final double INITIAL_PRICE = 1000.0;
    private static final double DELTA = 1e-6;
    private static final long BUCKET_SIZE_NANOS = 5_000_000_000L; // 5 seconds, matching default
    
    private BucketedAverageWithSTDBands bands;
    private long baseTime;
    
    @Before
    public void setUp() {
        bands = new BucketedAverageWithSTDBands(STD_MULTIPLIER, WINDOW_SIZE_SECS);
        // Note: BucketedAverageWithSTDBands uses a default bucket size of 5 seconds (DEFAULT_BUCKET_SIZE_SECS)
        baseTime = System.nanoTime();
    }
    
    // ============ CONSTRUCTOR TESTS ============
    
    @Test
    public void testBasicConstructor() {
        BucketedAverageWithSTDBands instance = new BucketedAverageWithSTDBands(1.5, 30);
        
        assertEquals(1.5, instance.getStdMultiplier(), DELTA);
        assertEquals(30, instance.getWindowSizeSecs());
        assertTrue(instance.isRunningAverage());
        assertEquals(0, instance.getSampleCount());
        assertFalse(instance.isValid());
        assertFalse(instance.isReady());
    }
    
    @Test
    public void testExtendedConstructor() {
        double stdMult = 2.5;
        int windowSecs = 60;
        int expirationBuffer = 10;
        double transitionFactor = 0.3;
        double maxChangeRatio = 0.8;
        
        BucketedAverageWithSTDBands instance = new BucketedAverageWithSTDBands(
            stdMult, windowSecs, expirationBuffer, transitionFactor, maxChangeRatio);
        
        assertEquals(stdMult, instance.getStdMultiplier(), DELTA);
        assertEquals(windowSecs, instance.getWindowSizeSecs());
        // Note: getTransitionSmoothingFactor() method is not available in the public API
        assertEquals(0.001, instance.getHysteresisFactor(), DELTA); // Default hysteresis factor
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testConstructorInvalidStdMultiplier() {
        new BucketedAverageWithSTDBands(-1.0, 10);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testConstructorInvalidWindowSize() {
        new BucketedAverageWithSTDBands(2.0, 0);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testExtendedConstructorInvalidTransitionFactor() {
        new BucketedAverageWithSTDBands(2.0, 10, 5, 1.5, 0.5); // transition factor > 1.0
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testExtendedConstructorInvalidMaxChangeRatio() {
        new BucketedAverageWithSTDBands(2.0, 10, 5, 0.5, 0.02); // max change ratio < 0.05
    }
    
    // ============ BASIC FUNCTIONALITY TESTS ============
    
    @Test
    public void testInitialState() {
        assertEquals(0.0, bands.getAverage(), DELTA);
        assertEquals(0.0, bands.getStandardDeviation(), DELTA);
        assertEquals(0, bands.getSampleCount());
        assertEquals(0, bands.getBucketCount());
        assertFalse(bands.isValid());
        assertFalse(bands.isReady());
    }
    
    @Test
    public void testSinglePriceUpdate() {
        bands.updatePrice(INITIAL_PRICE, baseTime);
        
        // Buckets need MIN_SAMPLES_PER_BUCKET (2) samples to be considered mature
        // So a single sample results in 0 effective samples for statistics
        assertEquals(0, bands.getSampleCount());
        assertEquals(1, bands.getBucketCount());
        assertFalse(bands.isReady()); // Single sample not enough for meaningful stats
    }
    
    @Test
    public void testMinimumSamplesPerBucket() {
        // Test that we need at least 2 samples per bucket for statistics
        // Default bucket size is 5 seconds, so samples within 5s go to same bucket
        
        // Use a controlled baseTime that doesn't align with bucket boundaries
        long controlledBaseTime = 1000_000_000_000L + 1_234_567_890L; // Some offset from boundary
        
        // Add first sample
        bands.updatePrice(INITIAL_PRICE, controlledBaseTime);
        System.out.println("After first sample: buckets=" + bands.getBucketCount() + ", samples=" + bands.getSampleCount());
        assertEquals(0, bands.getSampleCount()); // Not mature yet
        assertFalse(bands.isReady());
        
        // Add second sample to same bucket (within 5 seconds)
        bands.updatePrice(INITIAL_PRICE + 1.0, controlledBaseTime + 1_000_000_000L); // 1 second later
        System.out.println("After second sample: buckets=" + bands.getBucketCount() + ", samples=" + bands.getSampleCount());
        assertEquals(0, bands.getSampleCount()); // Still not recalculated until bucket is finished
        
        // Add sample that starts a new bucket (6 seconds later)
        bands.updatePrice(INITIAL_PRICE + 2.0, controlledBaseTime + 6_000_000_000L);
        System.out.println("After third sample: buckets=" + bands.getBucketCount() + ", samples=" + bands.getSampleCount());
        System.out.println("Bucket dump:\n" + bands.dumpBuckets());
        // Now the first bucket (with 2 samples) becomes mature and is counted
        assertEquals(2, bands.getSampleCount()); // Previous bucket's 2 samples now counted
        assertEquals(2, bands.getBucketCount());
        assertFalse(bands.isReady()); // Still need 2 mature buckets and sufficient time span
    }
    
    @Test
    public void testMultiplePriceUpdates() {
        // Test proper bucketing behavior with adequate samples per bucket
        // Default bucket size is 5 seconds, so we need to space samples appropriately
        
        // Create buckets with 3 samples each, spaced 6 seconds apart to ensure separate buckets
        for (int bucket = 0; bucket < 5; bucket++) {
            long bucketStartTime = baseTime + bucket * 6_000_000_000L; // 6 second intervals
            
            // Add 3 samples to each bucket (within the 5-second bucket size)
            for (int sample = 0; sample < 3; sample++) {
                double price = INITIAL_PRICE + bucket * 3 + sample; // Varying prices
                long sampleTime = bucketStartTime + sample * 1_000_000_000L; // 1 second apart within bucket
                bands.updatePrice(price, sampleTime);
            }
        }
        
        // After 5 buckets with 3 samples each, we should have mature buckets
        assertTrue("Should have at least 12 samples", bands.getSampleCount() >= 12); // 4 completed buckets * 3 samples
        assertTrue("Should have multiple buckets", bands.getBucketCount() >= 4);
        assertTrue("Should be valid with sufficient data", bands.isValid());
        assertTrue("Should be ready with multiple mature buckets", bands.isReady());
        
        // Check that mean calculation works with bucketed data
        // We're adding prices from 1000.0 to 1014.0 across buckets
        double actualMean = bands.getAverage();
        assertTrue("Mean should be reasonable", actualMean >= 1006.0 && actualMean <= 1008.0);
    }
    
    @Test
    public void testStatisticalCalculations() {
        // Test statistical calculations with proper bucketing
        // Use known data spread across multiple buckets
        
        // Create 3 buckets, each with exactly 2 samples to make them mature
        double[] bucketMeans = {1000.0, 1002.0, 1004.0}; // Different means per bucket
        
        for (int bucket = 0; bucket < bucketMeans.length; bucket++) {
            long bucketStartTime = baseTime + bucket * 6_000_000_000L; // 6 second intervals
            double bucketMean = bucketMeans[bucket];
            
            // Add 2 samples per bucket around the mean
            bands.updatePrice(bucketMean - 0.5, bucketStartTime);
            bands.updatePrice(bucketMean + 0.5, bucketStartTime + 1_000_000_000L);
        }
        
        // Add one more sample to trigger final bucket calculation
        bands.updatePrice(1006.0, baseTime + 18_000_000_000L); // 18 seconds later
        
        assertTrue("Should be ready with multiple mature buckets", bands.isReady());
        assertTrue("Should have sample count > 0", bands.getSampleCount() > 0);
        
        // Check statistical calculations
        double actualMean = bands.getAverage();
        assertTrue("Mean should be around 1002", Math.abs(actualMean - 1002.0) < 1.0);
        
        double stdDev = bands.getStandardDeviation();
        assertTrue("Standard deviation should be positive", stdDev > 0);
        
        // Check bands are calculated correctly
        double expectedUpperBand = bands.getAverage() + STD_MULTIPLIER * bands.getStandardDeviation();
        double expectedLowerBand = bands.getAverage() - STD_MULTIPLIER * bands.getStandardDeviation();
        
        assertEquals(expectedUpperBand, bands.getUpperBand(), DELTA);
        assertEquals(expectedLowerBand, bands.getLowerBand(), DELTA);
    }
    
    // ============ BAND CROSSING DETECTION TESTS ============
    
    @Test
    public void testBandCrossingDetection() {
        // Initialize with stable data
        initializeWithStableData();
        
        double mean = bands.getAverage();
        double std = bands.getStandardDeviation();
        
        // Test price above upper band
        double priceAboveBand = mean + (STD_MULTIPLIER * std) + 1.0;
        bands.updatePrice(priceAboveBand, baseTime + 20 * NANOS_PER_SEC);
        
        // Note: Band crossing detection may require hysteresis confirmation
        // so we test the detection methods exist and respond appropriately
        // The exact behavior depends on hysteresis settings
        assertFalse(bands.isAboveUpperBand() && bands.isBelowLowerBand()); // Can't be both
    }
    
    @Test
    public void testBandCrossingMethods() {
        initializeWithStableData();
        
        // Test all band crossing methods exist and return boolean values
        boolean aboveUpper = bands.isAboveUpperBand();
        boolean belowLower = bands.isBelowLowerBand();
        boolean noLongerAboveUpper = bands.isNoLongerAboveUpperBand();
        boolean noLongerBelowLower = bands.isNoLongerBelowLowerBand();
        
        // These are boolean values - basic sanity check
        assertTrue(aboveUpper || !aboveUpper); // Always true, just checking method exists
        assertTrue(belowLower || !belowLower);
        assertTrue(noLongerAboveUpper || !noLongerAboveUpper);
        assertTrue(noLongerBelowLower || !noLongerBelowLower);
    }
    
    // ============ BUCKETING MECHANISM TESTS ============
    
    @Test
    public void testBucketingMechanismCore() {
        // Test with controlled timestamps to ensure predictable bucketing
        long t0 = 1 * BUCKET_SIZE_NANOS; // Start of first bucket
        long t1 = 2 * BUCKET_SIZE_NANOS; // Start of second bucket
        long t2 = 3 * BUCKET_SIZE_NANOS; // Start of third bucket

        // All samples have the same price for simplicity
        double price = 100.0;

        // Add first sample - should be in the first bucket
        bands.updatePrice(price, t0);

        // Add second sample - should transition to a new bucket
        bands.updatePrice(price, t1);

        // Add third sample - should transition to yet another new bucket
        bands.updatePrice(price, t2);
    }
    
    @Test
    public void testBucketCreation() {
        // Test bucket creation with proper timing to ensure multiple buckets
        // Default bucket size is 5 seconds, so space samples 6 seconds apart
        
        assertEquals(0, bands.getBucketCount()); // Start with no buckets
        
        // Create 4 distinct buckets, each with sufficient samples
        for (int i = 0; i < 4; i++) {
            long bucketTime = baseTime + i * 6_000_000_000L; // 6 second intervals
            
            // Add 3 samples per bucket to make them mature and provide statistical diversity
            bands.updatePrice(INITIAL_PRICE + i, bucketTime);
            bands.updatePrice(INITIAL_PRICE + i + 0.1, bucketTime + 1_000_000_000L); // 1s later
            bands.updatePrice(INITIAL_PRICE + i + 0.2, bucketTime + 2_000_000_000L); // 2s later
        }
        
        assertTrue("Should have multiple buckets", bands.getBucketCount() >= 3);
        assertTrue("Should have counted samples from mature buckets", bands.getSampleCount() >= 6);
        assertTrue("Should be ready with sufficient data", bands.isReady());
    }
    
    @Test
    public void testBucketExpiration() {
        // Add old data that should eventually expire
        long oldTime = baseTime - (WINDOW_SIZE_SECS + 5) * NANOS_PER_SEC;
        
        // Add some old data
        for (int i = 0; i < 5; i++) {
            bands.updatePrice(INITIAL_PRICE + i, oldTime + i * NANOS_PER_SEC);
        }
        
        int bucketsAfterOldData = bands.getBucketCount();
        
        // Add new data within the window
        for (int i = 0; i < 5; i++) {
            bands.updatePrice(INITIAL_PRICE + 10 + i, baseTime + i * NANOS_PER_SEC);
        }
        
        // Bucket count should reflect expiration of old data
        // (Exact behavior depends on expiration buffer settings)
        assertTrue(bands.getBucketCount() >= 1);
        assertTrue("Old data should have some impact on bucket count", bucketsAfterOldData >= 0);
    }
    
    // ============ EDGE CASE TESTS ============
    
    @Test
    public void testNaNValues() {
        // Test that NaN values are handled gracefully
        bands.updatePrice(Double.NaN, baseTime);
        
        // Should not break the calculator
        assertEquals(0, bands.getSampleCount());
        assertFalse(bands.isValid());
    }
    
    @Test
    public void testInfiniteValues() {
        // Test infinite values
        bands.updatePrice(Double.POSITIVE_INFINITY, baseTime);
        bands.updatePrice(Double.NEGATIVE_INFINITY, baseTime + NANOS_PER_SEC);
        
        // Should handle gracefully
        assertFalse(Double.isInfinite(bands.getAverage()));
        assertFalse(Double.isInfinite(bands.getStandardDeviation()));
    }
    
    @Test
    public void testExtremeValues() {
        // Test very large and very small values
        bands.updatePrice(1e12, baseTime);
        bands.updatePrice(1e-12, baseTime + NANOS_PER_SEC);
        bands.updatePrice(1000.0, baseTime + 2 * NANOS_PER_SEC);
        
        assertTrue(bands.getSampleCount() > 0);
        assertFalse(Double.isNaN(bands.getAverage()));
        assertFalse(Double.isNaN(bands.getStandardDeviation()));
    }
    
    @Test
    public void testIdenticalValues() {
        // Test with identical values using proper bucketing
        // Create multiple buckets with identical values to test zero standard deviation
        
        // Create 3 buckets, each with 3 identical samples
        for (int bucket = 0; bucket < 3; bucket++) {
            long bucketStartTime = baseTime + bucket * 6_000_000_000L; // 6 second intervals
            
            // Add 3 identical samples per bucket
            for (int sample = 0; sample < 3; sample++) {
                bands.updatePrice(INITIAL_PRICE, bucketStartTime + sample * 1_000_000_000L);
            }
        }
        
        // Add sample to trigger final calculation
        bands.updatePrice(INITIAL_PRICE, baseTime + 18_000_000_000L);
        
        assertTrue("Should be ready with multiple buckets", bands.isReady());
        assertEquals(INITIAL_PRICE, bands.getAverage(), DELTA);
        assertTrue("Standard deviation should be close to zero for identical values", 
                   bands.getStandardDeviation() < 0.1); // Allow small minimum variance
        
        // Bands should be very close to the mean for identical values
        assertTrue("Upper band should be close to mean", 
                   Math.abs(bands.getUpperBand() - INITIAL_PRICE) < 1.0);
        assertTrue("Lower band should be close to mean", 
                   Math.abs(bands.getLowerBand() - INITIAL_PRICE) < 1.0);
    }
    
    // ============ CONFIGURATION TESTS ============
    
    @Test
    public void testHysteresisFactorConfiguration() {
        bands.setHysteresisFactor(0.005); // 0.5%
        assertEquals(0.005, bands.getHysteresisFactor(), DELTA);
        
        // Test boundary values
        bands.setHysteresisFactor(0.0001); // Minimum
        assertEquals(0.0001, bands.getHysteresisFactor(), DELTA);
        
        bands.setHysteresisFactor(0.01); // Maximum
        assertEquals(0.01, bands.getHysteresisFactor(), DELTA);
        
        // Test clamping
        bands.setHysteresisFactor(0.02); // Above maximum
        assertEquals(0.01, bands.getHysteresisFactor(), DELTA);
        
        bands.setHysteresisFactor(0.00005); // Below minimum
        assertEquals(0.0001, bands.getHysteresisFactor(), DELTA);
    }
    
    @Test
    public void testBucketSizeConfiguration() {
        // Test configuring smaller bucket size for more frequent calculations
        long newBucketSize = 2_000_000_000L; // 2 seconds
        bands.setBucketSizeNanos(newBucketSize);
        
        // Add data with 3-second intervals to create distinct buckets
        for (int i = 0; i < 6; i++) {
            long timestamp = baseTime + i * 3_000_000_000L; // 3 second intervals
            bands.updatePrice(INITIAL_PRICE + i * 0.1, timestamp);
            // Add second sample to same bucket to make it mature
            bands.updatePrice(INITIAL_PRICE + i * 0.1 + 0.05, timestamp + 500_000_000L); // 0.5s later
        }
        
        assertTrue("Should have multiple buckets with smaller bucket size", bands.getBucketCount() >= 4);
        assertTrue("Should have samples from mature buckets", bands.getSampleCount() > 0);
    }
    
    @Test
    public void testTransitionSmoothingFactor() {
        bands.setTransitionSmoothingFactor(0.7);
        // Note: getTransitionSmoothingFactor() method is not available in the public API
        // Just test that the setter doesn't throw an exception
        
        // Test boundary clamping by setting values and observing no exceptions
        bands.setTransitionSmoothingFactor(1.5); // Above 1.0
        bands.setTransitionSmoothingFactor(-0.1); // Below 0.0
        
        // Test passes if no exceptions are thrown
        assertTrue(true);
    }
    
    // ============ SIGNAL SNAPSHOT TESTS ============
    
    @Test
    public void testSignalSnapshot() {
        initializeWithStableData();
        
        BucketedAverageWithSTDBands.SignalSnapshot snapshot = bands.getSignalSnapshot();
        
        assertNotNull(snapshot);
        assertTrue(snapshot.isReady());
        assertEquals(bands.getAverage(), snapshot.getMean(), DELTA);
        assertEquals(bands.getStandardDeviation(), snapshot.getStandardDeviation(), DELTA);
        assertEquals(bands.getUpperBand(), snapshot.getUpperBand(), DELTA);
        assertEquals(bands.getLowerBand(), snapshot.getLowerBand(), DELTA);
        assertEquals(bands.getLastPrice(), snapshot.getLastPrice(), DELTA);
        
        // Test band crossing states in snapshot
        assertEquals(bands.isAboveUpperBand(), snapshot.isAboveUpperBand());
        assertEquals(bands.isBelowLowerBand(), snapshot.isBelowLowerBand());
        assertEquals(bands.isNoLongerAboveUpperBand(), snapshot.isNoLongerAboveUpperBand());
        assertEquals(bands.isNoLongerBelowLowerBand(), snapshot.isNoLongerBelowLowerBand());
    }
    
    // ============ MEAN SLOPE AND TREND TESTS ============
    
    @Test
    public void testMeanSlopeCalculation() {
        initializeWithStableData();
        
        double meanSlope = bands.getMeanSlope();
        assertFalse(Double.isNaN(meanSlope));
        
        BucketedAverageWithSTDBands.MeanSlopeState slopeState = bands.getMeanSlopeState();
        assertNotNull(slopeState);
    }
    
    @Test
    public void testMeanSlopeThreshold() {
        double threshold = 0.01;
        bands.setMeanSlopeThreshold(threshold);
        
        // Add trending data
        for (int i = 0; i < 10; i++) {
            bands.updatePrice(INITIAL_PRICE + i * 0.1, baseTime + i * NANOS_PER_SEC);
        }
        
        assertTrue(bands.isValid());
        // Slope state should respond to trending data
        assertNotNull(bands.getMeanSlopeState());
    }
    
    // ============ INPUT DATA HEALTH MONITORING TESTS ============
    
    @Test
    public void testInputDataHealthTracking() {
        bands.setHealthMonitoringEnabled(true);
        
        // Add some data
        initializeWithStableData();
        
        // Test health status
        assertNotNull(bands.getInputDataHealth());
        assertTrue(bands.isInputDataHealthy());
    }
    
    // ============ RESET AND STATE MANAGEMENT TESTS ============
    
    @Test
    public void testReset() {
        initializeWithStableData();
        
        // Verify we have data
        assertTrue(bands.getSampleCount() > 0);
        assertTrue(bands.getBucketCount() > 0);
        assertTrue(bands.isValid());
        
        // Reset
        bands.reset();
        
        // Verify reset state
        assertEquals(0, bands.getSampleCount());
        assertEquals(0, bands.getBucketCount());
        assertFalse(bands.isValid());
        assertFalse(bands.isReady());
        assertEquals(0.0, bands.getAverage(), DELTA);
        assertEquals(0.0, bands.getStandardDeviation(), DELTA);
    }
    
    @Test
    public void testUpdateTime() {
        initializeWithStableData();
        
        long newTime = baseTime + 100 * NANOS_PER_SEC;
        bands.updateTime(newTime);
        
        assertEquals(newTime, bands.getLatestTimestamp());
    }
    
    // ============ PERFORMANCE AND MEMORY TESTS ============
    
    @Test
    public void testLargeDataVolume() {
        // Test with large amount of data to ensure memory efficiency
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 10000; i++) {
            bands.updatePrice(INITIAL_PRICE + Math.sin(i * 0.01) * 10, 
                             baseTime + i * 100_000_000L); // 0.1 second intervals
        }
        
        long endTime = System.currentTimeMillis();
        
        assertTrue(bands.getSampleCount() > 0);
        assertTrue(bands.isValid());
        
        // Should complete in reasonable time (less than 5 seconds)
        assertTrue(endTime - startTime < 5000);
        
        // Memory should be managed through bucketing
        assertTrue(bands.getBucketCount() < bands.getSampleCount());
    }
    
    @Test
    public void testNoCombJitter() {
        // Original test - ensure no statistical collapse
        BucketedAverageWithSTDBands b = new BucketedAverageWithSTDBands(2.0, 60);
        long t0 = System.nanoTime();
        
        // 100 ticks every 10 ms for 5 s
        for (int i = 0; i < 500; i++) {
            b.updatePrice(100.0 + Math.sin(i/10.0)*0.25,
                          t0 + i * 10_000_000L);
        }
        double firstStd = b.getStandardDeviation();

        // Next second – first tick only (would collapse old version)
        b.updatePrice(100.1, t0 + 5_010_000_000L);
        double stdJustAfter = b.getStandardDeviation();

        assertEquals(firstStd, stdJustAfter, 1e-6);  // no collapse allowed
    }
    
    // ============ HELPER METHODS ============
    
    /**
     * Initialize the bands calculator with stable data for testing.
     * Creates multiple mature buckets with sufficient data for statistical calculations.
     */
    private void initializeWithStableData() {
        // Create 4 buckets, each with 3 samples to ensure statistical stability
        for (int bucket = 0; bucket < 4; bucket++) {
            long bucketStartTime = baseTime + bucket * 6_000_000_000L; // 6 second intervals
            
            for (int sample = 0; sample < 3; sample++) {
                // Add slight variation around base price
                double price = INITIAL_PRICE + (bucket % 3) * 0.1 + sample * 0.01;
                long sampleTime = bucketStartTime + sample * 1_000_000_000L; // 1 second apart
                bands.updatePrice(price, sampleTime);
            }
        }
        
        // Ensure the system is ready with sufficient data
        assertTrue("Helper method should create ready state", bands.isReady());
        assertTrue("Helper method should create valid state", bands.isValid());
        assertTrue("Helper method should have multiple buckets", bands.getBucketCount() >= 3);
        assertTrue("Helper method should have sample count > 0", bands.getSampleCount() > 0);
    }
}