package com.bookmap.api.simple.demo.indicators;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test to validate that band crossing detection works correctly in both directions.
 * This test is designed to ensure our hysteresis logic works properly.
 */
public class BandCrossingValidationTest {
    
    private static final long NANOS_PER_SEC = 1_000_000_000L;
    private static final double BASE_PRICE = 100.0;
    private static final double STD_MULTIPLIER = 2.0;
    private static final int WINDOW_SIZE_SECS = 10; // Shorter window for faster testing
    
    @Test
    public void testSymmetricBandCrossings() {
        System.out.println("===== SYMMETRIC BAND CROSSING VALIDATION =====");
        
        BucketedAverageWithSTDBands bands = new BucketedAverageWithSTDBands(STD_MULTIPLIER, WINDOW_SIZE_SECS);
        long baseTime = 1000 * NANOS_PER_SEC;
        
        // Initialize with stable prices around 100
        // Ensure we span enough time for ready state (70% of window = 7 seconds for 10-second window)
        int numInitialSamples = (int)(WINDOW_SIZE_SECS * 0.8); // 8 samples over 8 seconds
        for (int i = 0; i < numInitialSamples; i++) {
            double price = BASE_PRICE + ((Math.random() - 0.5) * 0.01); // Small random variation
            bands.updatePrice(price, baseTime + (i * NANOS_PER_SEC));
        }
        
        double initialMean = bands.getAverage();
        double initialUpper = bands.getUpperBand();
        double initialLower = bands.getLowerBand();
        double hysteresis = bands.getHysteresisFactor();
        
        System.out.println("Initial state:");
        System.out.println("  Mean: " + initialMean);
        System.out.println("  Upper band: " + initialUpper);
        System.out.println("  Lower band: " + initialLower);
        System.out.println("  Hysteresis factor: " + hysteresis);
        
        // Calculate deadband thresholds
        double upperThreshold = initialUpper + (Math.abs(initialUpper) * hysteresis);
        double lowerThreshold = initialLower - (Math.abs(initialLower) * hysteresis);
        
        System.out.println("  Upper threshold (with deadband): " + upperThreshold);
        System.out.println("  Lower threshold (with deadband): " + lowerThreshold);
        
        long timestamp = baseTime + (numInitialSamples * NANOS_PER_SEC);
        
        // Test 1: Cross above upper band
        System.out.println("\n--- Testing Upper Band Crossing ---");
        double upperCrossPrice = upperThreshold + 0.001; // Just above threshold
        bands.updatePrice(upperCrossPrice, timestamp++);
        
        System.out.println("Price " + upperCrossPrice + " (above threshold " + upperThreshold + ")");
        System.out.println("  isAboveUpperBand: " + bands.isAboveUpperBand());
        System.out.println("  isReady: " + bands.isReady());
        System.out.println("  isValid: " + bands.isValid());
        System.out.println("  totalSamples: " + bands.getSampleCount());
        System.out.println("  MIN_SAMPLES_FOR_BAND_CROSSING: 5");
        assertTrue("Should detect upper band crossing", bands.isAboveUpperBand());
        
        // Test 2: Return to inside bands from upper
        double returnPrice = initialMean;
        bands.updatePrice(returnPrice, timestamp++);
        
        System.out.println("Return price " + returnPrice + " (mean level)");
        System.out.println("  isNoLongerAboveUpperBand: " + bands.isNoLongerAboveUpperBand());
        System.out.println("  isAboveUpperBand: " + bands.isAboveUpperBand());
        assertTrue("Should detect return from upper band", bands.isNoLongerAboveUpperBand());
        assertFalse("Should no longer be above upper band", bands.isAboveUpperBand());
        
        // Test 3: Cross below lower band
        System.out.println("\n--- Testing Lower Band Crossing ---");
        double lowerCrossPrice = lowerThreshold - 0.001; // Just below threshold
        bands.updatePrice(lowerCrossPrice, timestamp++);
        
        System.out.println("Price " + lowerCrossPrice + " (below threshold " + lowerThreshold + ")");
        System.out.println("  isBelowLowerBand: " + bands.isBelowLowerBand());
        assertTrue("Should detect lower band crossing", bands.isBelowLowerBand());
        
        // Test 4: Return to inside bands from lower
        bands.updatePrice(returnPrice, timestamp++);
        
        System.out.println("Return price " + returnPrice + " (mean level)");
        System.out.println("  isNoLongerBelowLowerBand: " + bands.isNoLongerBelowLowerBand());
        System.out.println("  isBelowLowerBand: " + bands.isBelowLowerBand());
        assertTrue("Should detect return from lower band", bands.isNoLongerBelowLowerBand());
        assertFalse("Should no longer be below lower band", bands.isBelowLowerBand());
        
        System.out.println("\n=== SYMMETRIC BAND CROSSING TEST PASSED ===");
    }
    
    @Test
    public void testMultipleOscillations() {
        System.out.println("===== MULTIPLE OSCILLATIONS TEST =====");
        
        BucketedAverageWithSTDBands bands = new BucketedAverageWithSTDBands(STD_MULTIPLIER, WINDOW_SIZE_SECS);
        long baseTime = 2000 * NANOS_PER_SEC;
        
        // Initialize with stable prices
        for (int i = 0; i < 30; i++) {
            bands.updatePrice(BASE_PRICE, baseTime + (i * NANOS_PER_SEC));
        }
        
        double initialUpper = bands.getUpperBand();
        double initialLower = bands.getLowerBand();
        double hysteresis = bands.getHysteresisFactor();
        
        // Calculate crossing thresholds
        double upperCrossThreshold = initialUpper + (Math.abs(initialUpper) * hysteresis) + 0.001;
        double lowerCrossThreshold = initialLower - (Math.abs(initialLower) * hysteresis) - 0.001;
        
        System.out.println("Test thresholds:");
        System.out.println("  Upper crossing price: " + upperCrossThreshold);
        System.out.println("  Lower crossing price: " + lowerCrossThreshold);
        
        long timestamp = baseTime + (31 * NANOS_PER_SEC);
        int upperCrossings = 0;
        int lowerCrossings = 0;
        
        // Create 5 oscillations between upper and lower bands
        for (int cycle = 0; cycle < 5; cycle++) {
            System.out.println("\n--- Cycle " + (cycle + 1) + " ---");
            
            // Go to upper band
            bands.updatePrice(upperCrossThreshold, timestamp++);
            if (bands.isAboveUpperBand()) {
                upperCrossings++;
                System.out.println("Upper crossing detected (total: " + upperCrossings + ")");
            }
            
            // Return to middle
            bands.updatePrice(BASE_PRICE, timestamp++);
            
            // Go to lower band  
            bands.updatePrice(lowerCrossThreshold, timestamp++);
            if (bands.isBelowLowerBand()) {
                lowerCrossings++;
                System.out.println("Lower crossing detected (total: " + lowerCrossings + ")");
            }
            
            // Return to middle
            bands.updatePrice(BASE_PRICE, timestamp++);
        }
        
        System.out.println("\nFinal results:");
        System.out.println("  Upper crossings: " + upperCrossings);
        System.out.println("  Lower crossings: " + lowerCrossings);
        
        assertTrue("Should have detected multiple upper crossings", upperCrossings >= 3);
        assertTrue("Should have detected multiple lower crossings", lowerCrossings >= 3);
        
        System.out.println("=== MULTIPLE OSCILLATIONS TEST PASSED ===");
    }
}
