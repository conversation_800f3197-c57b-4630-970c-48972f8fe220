package com.bookmap.api.simple.demo.indicators;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Debug test to understand why the validation test is failing
 */
public class ValidationDebugTest {
    
    private static final long NANOS_PER_SEC = 1_000_000_000L;
    private static final double BASE_PRICE = 100.0;
    private static final double STD_MULTIPLIER = 2.0;
    private static final int WINDOW_SIZE_SECS = 10; // Shorter window for faster testing
    
    @Test
    public void debugValidationIssue() {
        System.out.println("===== VALIDATION DEBUG TEST =====");
        
        BucketedAverageWithSTDBands bands = new BucketedAverageWithSTDBands(STD_MULTIPLIER, WINDOW_SIZE_SECS);
        long baseTime = 1000 * NANOS_PER_SEC;
        
        System.out.println("Window size: " + WINDOW_SIZE_SECS + " seconds");
        System.out.println("Required time span (70%): " + (WINDOW_SIZE_SECS * 0.7) + " seconds");
        
        // Add samples over a longer time span to meet the 70% requirement
        int numSamples = (int)(WINDOW_SIZE_SECS * 0.8); // 80% of window duration
        System.out.println("Adding " + numSamples + " samples over " + numSamples + " seconds");
        
        for (int i = 0; i < numSamples; i++) {
            double price = BASE_PRICE + ((Math.random() - 0.5) * 0.01); // Small random variation
            long timestamp = baseTime + (i * NANOS_PER_SEC);
            bands.updatePrice(price, timestamp);
            
            if (i % 3 == 0) { // Print every 3rd sample
                System.out.println("Sample " + i + ": isReady=" + bands.isReady() + 
                                 ", isValid=" + bands.isValid() + 
                                 ", totalSamples=" + bands.getSampleCount());
            }
        }
        
        System.out.println("\nFinal state after " + numSamples + " samples:");
        System.out.println("  isReady: " + bands.isReady());
        System.out.println("  isValid: " + bands.isValid());
        System.out.println("  totalSamples: " + bands.getSampleCount());
        System.out.println("  mean: " + bands.getAverage());
        System.out.println("  std: " + bands.getStandardDeviation());
        System.out.println("  upperBand: " + bands.getUpperBand());
        System.out.println("  lowerBand: " + bands.getLowerBand());
        
        if (!bands.isReady()) {
            System.out.println("ERROR: Bands are not ready even with sufficient samples!");
            return;
        }
        
        double hysteresis = bands.getHysteresisFactor();
        double upperThreshold = bands.getUpperBand() + (Math.abs(bands.getUpperBand()) * hysteresis);
        double lowerThreshold = bands.getLowerBand() - (Math.abs(bands.getLowerBand()) * hysteresis);
        
        System.out.println("\nCrossing thresholds:");
        System.out.println("  Upper threshold: " + upperThreshold);
        System.out.println("  Lower threshold: " + lowerThreshold);
        
        // Test upper band crossing
        long nextTimestamp = baseTime + (numSamples * NANOS_PER_SEC);
        double upperCrossPrice = upperThreshold + 0.01;
        bands.updatePrice(upperCrossPrice, nextTimestamp++);
        
        System.out.println("\nUpper crossing test:");
        System.out.println("  Price: " + upperCrossPrice + " (should be > " + upperThreshold + ")");
        System.out.println("  isAboveUpperBand: " + bands.isAboveUpperBand());
        System.out.println("  isReady: " + bands.isReady());
        System.out.println("  isValid: " + bands.isValid());
        
        // Test lower band crossing
        double lowerCrossPrice = lowerThreshold - 0.01;
        bands.updatePrice(lowerCrossPrice, nextTimestamp++);
        
        System.out.println("\nLower crossing test:");
        System.out.println("  Price: " + lowerCrossPrice + " (should be < " + lowerThreshold + ")");
        System.out.println("  isBelowLowerBand: " + bands.isBelowLowerBand());
        System.out.println("  isReady: " + bands.isReady());
        System.out.println("  isValid: " + bands.isValid());
        
        assertTrue("Bands should be ready after sufficient samples", bands.isReady());
        assertTrue("Bands should be valid after sufficient samples", bands.isValid());
        assertTrue("Should detect upper band crossing", bands.isAboveUpperBand());
        assertTrue("Should detect lower band crossing", bands.isBelowLowerBand());
    }
}
