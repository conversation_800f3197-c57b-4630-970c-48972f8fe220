# NubiaAutoMidasAnchoredVWAPV5_8_0 Entry & Exit Systems Comprehensive Guide

## Table of Contents
1. [Entry Systems](#entry-systems)
   - [Mini ATR Entry System](#1-mini-atr-entry-system)
   - [MA Pending Entry System](#2-ma-pending-entry-system)
   - [General Pending Entry System](#3-general-pending-entry-system)
   - [Entry Triple Stop System](#4-entry-triple-stop-system)
   - [Direct MA Entries](#5-direct-ma-entries)
   - [MA Rejection Opportunity Entries](#6-ma-rejection-opportunity-entries)
2. [Exit Systems](#exit-systems)
   - [MA Triple Stop Exit System](#1-ma-triple-stop-exit-system)
   - [Standard Stop/Target Exits](#2-standard-stoptarget-exits)
   - [ATR-Based Exits](#3-atr-based-exits)
   - [MA1 Profit Exit System](#4-ma1-profit-exit-system)
   - [Catastrophic Stop System](#5-catastrophic-stop-system)
3. [System Integration & Priority](#system-integration--priority)
4. [Trade Reason Codes Reference](#trade-reason-codes-reference)

---

## Entry Systems

### 1. Mini ATR Entry System

**Purpose**: ATR-based trend following entry system with exclusive or alongside operation modes.

**Key Variables**:
- `miniAtrEntryPrice`: Entry trigger price
- `miniAtrEntryStopPips`: Stop distance in pips
- `useMiniAtrTrendForTradingInt`: Operation mode (0=Off, 1=Alongside, 2=Exclusive)

**Operation Modes**:
- **Off (0)**: System disabled
- **Alongside (1)**: Works with other entry systems
- **Exclusive (2)**: Bypasses all other entry systems when conditions met

**Entry Logic**:
```java
if (useMiniAtrTrendForTradingInt == 2) { // Exclusive mode
    // If conditions met, execute immediately and skip other entries
    return;
}
```

**Configuration Parameters**:
- `miniAtrEntryStopPips`: Stop distance from entry
- `miniAtrRequiredThicknessMultiplier`: Thickness requirement multiplier
- `miniAtrTickCountThreshold`: Tick count threshold for validation

---

### 2. MA Pending Entry System

**Purpose**: Creates pending entries based on Moving Average signals with trailing stop capability.

**Key Variables**:
- `maPendingEntryDirection`: Direction (1=long, -1=short, 0=none)
- `maPendingEntryStopLevel`: Current stop trigger level
- `maPendingEntryConfirmationCounter`: Confirmation bar counter
- `maPendingEntrySetupReasonCode`: Reason code for the setup

**Features**:
- **ATR-Based Trailing**: Stop level adjusts based on ATR distance
- **Fixed Pip Trailing**: Alternative to ATR-based stops
- **Confirmation Bars**: Optional delay before entry activation
- **Best Price Tracking**: Trails favorable price movements

**Entry Process**:
1. **Setup Phase**: MA signal detected → Initialize pending entry
2. **Trailing Phase**: Update stop level as price moves favorably
3. **Trigger Phase**: Execute when price hits stop level

**Configuration Parameters**:
- `usePendingEntryStops`: Enable/disable pending entries
- `pendingEntryStopPips`: Stop distance in pips
- `pendingEntryConfirmationBars`: Confirmation delay
- `pendingEntryProfitDistancePips`: Profit distance for targets
- `pendingEntryProfitableBufferPips`: Buffer for profitable entries

**Trade Reason Codes**:
- 3: MA Pending Long Triggered
- 4: MA Pending Short Triggered
- 13: MA Pending Long (General Setup)
- 14: MA Pending Short (General Setup)

---

### 3. General Pending Entry System

**Purpose**: Generic pending entry system for non-MA based signals.

**Key Variables**:
- `pendingEntryDirection`: Direction (1=long, -1=short, 0=none)
- `pendingEntryStopLevel`: Trigger level
- `bestPriceForTrailingEntry`: Best price for trailing

**Features**:
- Similar to MA pending but for general use
- Can be overridden by Entry Triple Stop
- Uses same trailing logic as MA pending

**Indicator**:
- `pendingEntryStopIndicator`: Visual representation of stop level

---

### 4. Entry Triple Stop System

**Purpose**: Three-layer entry system that monitors and can override pending entries.

**Key Variables**:
- `entryTriple_Enabled`: Master enable/disable
- `entryTripleL2Long_CurrentStopLevel`: L2 trigger for long entries
- `entryTripleL2Short_CurrentStopLevel`: L2 trigger for short entries

**Unique Features**:
- **Dependency**: Only active when pending entries exist
- **Override Priority**: Highest priority, can cancel pending entries
- **Three Layers**:
  - L1: Initial stop level
  - L2: Entry trigger level
  - L3: Catastrophic stop level

**Entry Logic**:
```java
// Only active when pending entries exist
boolean hasPendingEntries = (maPendingEntryDirection != 0) || (pendingEntryDirection != 0);

// L2 breach triggers entry and cancels pending
if (currentPrice > entryTripleL2Long_CurrentStopLevel) {
    // Cancel pending entries
    maPendingEntryDirection = 0;
    // Execute Entry Triple entry
}
```

**Configuration Parameters**:
- `entryTriple_L1_OffsetPips`: Layer 1 offset
- `entryTriple_L1_TrailingOffsetPips`: L1 trailing offset
- `entryTriple_L2_FixedOffsetPips`: Layer 2 fixed offset
- `entryTriple_L3_OffsetPips`: Layer 3 offset

**Trade Reason Codes**:
- 15: Entry Triple Long Entry
- 16: Entry Triple Short Entry

**Indicators** (Show NaN when no pending entries):
- `entryTripleL1LongIndicator`, `entryTripleL1ShortIndicator`
- `entryTripleL2LongIndicator`, `entryTripleL2ShortIndicator`
- `entryTripleL3LongIndicator`, `entryTripleL3ShortIndicator`

---

### 5. Direct MA Entries

**Purpose**: Immediate entry execution based on MA breakouts when pending entries are disabled.

**Entry Conditions**:
- Price breaks above/below specific MAs
- No pending entry setup required
- Executes immediately on signal

**Trade Reason Codes**:
- 5: Standard MA Long Entry - Breakout Style 1
- 6: Standard MA Short Entry - Breakout Style 1
- 7: Standard MA Long Entry - Pullback to MA1 w/ ATR Stop
- 8: Standard MA Long Entry - Trend Following Pullback w/ Fixed Stop
- 9: Standard MA Short Entry - Pullback to MA1 w/ ATR Stop
- 10: Standard MA Short Entry - Trend Following Pullback w/ Fixed Stop
- 11: Standard MA Short Entry - Re-entry after Exit Pullback

---

### 6. MA Rejection Opportunity Entries

**Purpose**: Entries based on price rejection from MA3 or MA4 levels.

**Key Variables**:
- `ma3LastCross[]`: Tracks MA3 crossing events
- `ma4LastCross[]`: Tracks MA4 crossing events
- `maOpportunityHysteresisPips`: Hysteresis for rejection detection

**Features**:
- **MA3 Rejection**: Primary rejection level
- **MA4 Rejection**: Secondary rejection level (only if MA3 doesn't trigger)
- **Lookback Analysis**: Checks recent bar patterns
- **VWAP Integration**: Can consider VWAP in rejection logic

**Configuration Parameters**:
- `enableMA3OpportunityEntry`: Enable MA3 rejection entries
- `enableMA4OpportunityEntry`: Enable MA4 rejection entries
- `maOpportunityLookbackBars`: Bars to analyze for pattern
- `maOpportunityHysteresisPips`: Price buffer for rejection

**Trade Reason Codes**:
- 1: MA Rejection Long Entry
- 2: MA Rejection Short Entry

---

## Exit Systems

### 1. MA Triple Stop Exit System

**Purpose**: Advanced exit system for MA-based trades with three protective layers.

**Key Components**:
- **Flattening Logic**: Immediate exit on catastrophic conditions
- **P&L Based Exits**: Exit based on profit/loss thresholds
- **Dynamic Stop Adjustment**: Stops adjust based on trade performance

**Exit Conditions**:
1. **Flattening Trigger**: Immediate exit when flattening requested
2. **Catastrophic Stop**: L3 level breach
3. **Standard Stop**: L1 level breach
4. **Profit Target**: Configurable profit targets

**Features**:
- Only applies to MA trades (`isMaTradeActive = true`)
- Three-layer protection system
- Dynamic stop adjustments based on P&L

**Configuration**:
- `enableMaTripleStopExits`: Master enable/disable
- Various L1, L2, L3 offset parameters

---

### 2. Standard Stop/Target Exits

**Purpose**: Basic stop loss and take profit exits.

**Key Variables**:
- `entryPrice`: Trade entry price
- `stopPrice`: Stop loss level
- `targetPrice`: Take profit level

**Exit Logic**:
```java
if (currentPosition > 0) { // Long position
    if (currentPrice <= stopPrice) // Stop hit
    if (currentPrice >= targetPrice) // Target hit
}
```

**Trade Reason Codes**:
- -1: Target Hit
- -2: Standard Stop Hit

---

### 3. ATR-Based Exits

**Purpose**: Dynamic exits based on Average True Range.

**Features**:
- **ATR Trailing Stops**: Stop distance based on market volatility
- **Dynamic Adjustment**: Adapts to changing market conditions
- **Breakout Detection**: Special handling for breakout trades

**Configuration**:
- `atrLength`: ATR calculation period
- `defaultATRMultiplier`: Standard ATR multiplier
- `atrBreakoutMultiplier`: Multiplier for breakout trades

---

### 4. MA1 Profit Exit System

**Purpose**: Exits trades when price returns to MA1 with profit.

**Key Variables**:
- `exitMA1WithProfitInt`: Enable parameter (1=enabled, 0=disabled)
- `exitMA1MinBarsInTrade`: Minimum bars before exit allowed
- `exitMA1MinProfitTicks`: Minimum profit requirement

**Exit Conditions**:
1. Trade is profitable by minimum ticks
2. Minimum bars in trade met
3. Price touches MA1

**Trade Reason Codes**:
- -5: MA1 Profit Long Exit
- -6: MA1 Profit Short Exit

---

### 5. Catastrophic Stop System

**Purpose**: Emergency exit system for extreme adverse moves.

**Features**:
- **Fixed Distance**: Set distance from entry
- **Always Active**: Cannot be disabled for risk management
- **Immediate Execution**: No delays or confirmations

**Trade Reason Code**:
- -3: Catastrophic Stop Hit

---

## System Integration & Priority

### Entry Execution Order

```
1. Mini ATR System Check (if exclusive mode, can bypass all others)
   ↓
2. MA Rejection Opportunity Check
   ↓
3. MA Pending Entry Setup/Update
   ↓
4. Entry Triple Stop Check (can override pending entries)
   ↓
5. Direct MA Entries (if pending disabled)
```

### Exit Execution Order

```
1. Catastrophic Stop Check (highest priority)
   ↓
2. MA Triple Stop Exit Logic (for MA trades)
   ↓
3. Standard Stop/Target Check
   ↓
4. MA1 Profit Exit Check
   ↓
5. ATR-Based Trailing Stop Updates
```

### Key Integration Rules

1. **Entry Triple Stop Override**: When triggered, cancels all pending entries
2. **MA Trade Flagging**: `isMaTradeActive` determines which exit systems apply
3. **Position State**: Many systems check `currentPosition` to determine availability
4. **Cooldown Periods**: `barsSinceLastExit` affects re-entry eligibility

---

## Trade Reason Codes Reference

### Entry Codes (Positive)
- **1**: MA Rejection Long Entry
- **2**: MA Rejection Short Entry
- **3**: MA Pending Long Triggered
- **4**: MA Pending Short Triggered
- **5**: Standard MA Long Entry - Breakout Style 1
- **6**: Standard MA Short Entry - Breakout Style 1
- **7**: Standard MA Long Entry - Pullback to MA1 w/ ATR Stop
- **8**: Standard MA Long Entry - Trend Following Pullback w/ Fixed Stop
- **9**: Standard MA Short Entry - Pullback to MA1 w/ ATR Stop
- **10**: Standard MA Short Entry - Trend Following Pullback w/ Fixed Stop
- **11**: Standard MA Short Entry - Re-entry after Exit Pullback
- **13**: MA Pending Long Triggered (General Setup)
- **14**: MA Pending Short Triggered (General Setup)
- **15**: Entry Triple Long Entry
- **16**: Entry Triple Short Entry

### Exit Codes (Negative)
- **-1**: Target Hit (MA or Non-MA)
- **-2**: Standard Stop / MA Trigger Stop Hit
- **-3**: Catastrophic Stop Hit (MA or Non-MA)
- **-4**: MA Rejection Exit (before reversal)
- **-5**: MA1 Profit Long Exit
- **-6**: MA1 Profit Short Exit
- **-99**: Unknown/Other Exit Reason

### Indicator Behavior
- **0**: No active trade / No specific reason plotted

---

## Configuration Best Practices

1. **Entry System Selection**:
   - Use Mini ATR Exclusive mode for pure trend following
   - Enable pending entries for more conservative entries
   - Entry Triple Stop adds extra confirmation layer

2. **Exit System Combination**:
   - Always keep catastrophic stops enabled
   - MA Triple Stop for MA-based trades
   - MA1 Profit Exit for mean reversion

3. **Risk Management**:
   - Set appropriate stop distances based on volatility
   - Use ATR multipliers for dynamic adaptation
   - Monitor trade reason codes for strategy analysis

4. **System Conflicts**:
   - Avoid enabling conflicting entry modes
   - Ensure exit parameters don't overlap
   - Test parameter combinations in simulation first