public class BucketDebugTest {
    public static void main(String[] args) {
        long bucketSizeNanos = 5_000_000_000L; // 5 seconds
        
        // Simulate the test scenario
        long baseTime = System.nanoTime();
        
        System.out.println("baseTime: " + baseTime);
        System.out.println("bucketSizeNanos: " + bucketSizeNanos);
        
        // Calculate bucket starts for the three test samples
        long timestamp1 = baseTime;
        long timestamp2 = baseTime + 1_000_000_000L;  // +1 second
        long timestamp3 = baseTime + 6_000_000_000L;  // +6 seconds
        
        long bucket1 = (timestamp1 / bucketSizeNanos) * bucketSizeNanos;
        long bucket2 = (timestamp2 / bucketSizeNanos) * bucketSizeNanos;
        long bucket3 = (timestamp3 / bucketSizeNanos) * bucketSizeNanos;
        
        System.out.println("\nTimestamp 1: " + timestamp1);
        System.out.println("  Division: " + (timestamp1 / bucketSizeNanos));
        System.out.println("  Bucket start: " + bucket1);
        
        System.out.println("\nTimestamp 2: " + timestamp2);
        System.out.println("  Division: " + (timestamp2 / bucketSizeNanos));
        System.out.println("  Bucket start: " + bucket2);
        
        System.out.println("\nTimestamp 3: " + timestamp3);
        System.out.println("  Division: " + (timestamp3 / bucketSizeNanos));
        System.out.println("  Bucket start: " + bucket3);
        
        System.out.println("\nBucket comparison:");
        System.out.println("  Bucket 1 == Bucket 2: " + (bucket1 == bucket2));
        System.out.println("  Bucket 2 == Bucket 3: " + (bucket2 == bucket3));
        System.out.println("  Bucket 1 == Bucket 3: " + (bucket1 == bucket3));
        
        // Also calculate the offsets from bucket boundaries
        System.out.println("\nOffset from bucket start:");
        System.out.println("  Sample 1 offset: " + (timestamp1 - bucket1));
        System.out.println("  Sample 2 offset: " + (timestamp2 - bucket2));
        System.out.println("  Sample 3 offset: " + (timestamp3 - bucket3));
    }
}
