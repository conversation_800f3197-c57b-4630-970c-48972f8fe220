Entry Triple Stop Implementation - Required Modifications
========================================================

MODIFICATION 1: Add L2 tracking variables (around line 926)
-----------------------------------------------------------
After line: private StopSpec entryTripleL1Spec, entryTripleL2Spec, entryTripleL3Spec;

Add these lines:
    // L2 tracking variables for Entry Triple Stop
    private double entryTripleL2Long_CurrentStopLevel = Double.NaN;
    private double entryTripleL2Short_CurrentStopLevel = Double.NaN;


MODIFICATION 2: Update the updateEntryTripleIndicators method (around line 5525)
--------------------------------------------------------------------------------
In the updateEntryTripleIndicators method, after calculating l2, store it:

For the short side (when currentPosition <= 0):
    double l2 = TrailingStopControllerV3.getStopPrice(simShort, bar, entryTripleL2Spec);
    entryTripleL2Long_CurrentStopLevel = l2;  // ADD THIS LINE

For the long side (when currentPosition >= 0):
    double l2 = TrailingStopControllerV3.getStopPrice(simLong, bar, entryTripleL2Spec);
    entryTripleL2Short_CurrentStopLevel = l2;  // ADD THIS LINE


MODIFICATION 3: Uncomment the updateEntryTripleIndicators call (around line 2074)
----------------------------------------------------------------------------------
Change this line:
    // updateEntryTripleIndicators(bar);

To:
    updateEntryTripleIndicators(bar);


MODIFICATION 4: Add Entry Triple Stop checking in tradeLogic (find the tradeLogic method)
-----------------------------------------------------------------------------------------
In the tradeLogic method, add this new entry condition BEFORE the Mini ATR entry logic:

    // === Entry Triple Stop Logic ===
    if (Boolean.TRUE.equals(entryTriple_Enabled) && currentPosition == 0) {
        double currentPrice = bar.getClose();
        
        // Check for Entry Triple Long Stop
        if (!Double.isNaN(entryTripleL2Long_CurrentStopLevel) && 
            currentPrice > entryTripleL2Long_CurrentStopLevel) {
            
            // Entry Triple Long triggered
            currentPosition = 1;
            currentSizeMultiplier = 1.0;
            entryPrice = currentPrice;
            
            // Send order through OrderSenderControllerV2
            OSCInstanceMain.sendMarketOrder(true, positionSize, true, "Entry Triple Long Stop");
            
            // Log the entry with reason code 15
            logTradeEntry(currentPrice, 15);
            
            if (!disableAllLogging) {
                Log.info("Entry Triple Long Stop triggered at price: " + currentPrice);
            }
            return;
        }
        
        // Check for Entry Triple Short Stop
        if (!Double.isNaN(entryTripleL2Short_CurrentStopLevel) && 
            currentPrice < entryTripleL2Short_CurrentStopLevel) {
            
            // Entry Triple Short triggered
            currentPosition = -1;
            currentSizeMultiplier = 1.0;
            entryPrice = currentPrice;
            
            // Send order through OrderSenderControllerV2
            OSCInstanceMain.sendMarketOrder(false, positionSize, true, "Entry Triple Short Stop");
            
            // Log the entry with reason code 16
            logTradeEntry(currentPrice, 16);
            
            if (!disableAllLogging) {
                Log.info("Entry Triple Short Stop triggered at price: " + currentPrice);
            }
            return;
        }
    }


MODIFICATION 5: Add trade reason codes
--------------------------------------
In the getTradeReasonString method, add these cases:

    case 15:
        return "Entry Triple Long";
    case 16:
        return "Entry Triple Short";


MODIFICATION 6: Reset L2 levels in resetTradeState method
----------------------------------------------------------
In the resetTradeState method, add:

    entryTripleL2Long_CurrentStopLevel = Double.NaN;
    entryTripleL2Short_CurrentStopLevel = Double.NaN;


IMPLEMENTATION NOTES:
--------------------
1. The Entry Triple Stop uses L2 (Layer 2) as the trigger level
2. L2 is a composite of Fixed Offset and VolStop using TIGHTEST mode
3. Entry signals occur when price crosses the L2 level
4. Trade reason codes: 15 for Long, 16 for Short
5. Orders are sent through OSCInstanceMain (Main trading controller)
